package com.dl.dops.resourcecenter.war.tencentcloud;


import cn.hutool.core.date.DateUtil;

import java.util.Date;

public class Test {

    public static void main(String[] args) {
        System.out.println(getTimeExpire(60));
    }

    public static String getTimeExpire(int minute){
        long time = minute*60*1000;
        Date now = new Date();
        Date afterDate = new Date(now.getTime() + time);
        return DateUtil.format(afterDate,"yyyy-MM-dd'T'HH:mm:ssXXX");
    }
}
