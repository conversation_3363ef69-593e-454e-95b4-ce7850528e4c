package com.dl.dops.biz.resourcecenter.manager.sop.helper;

import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopBO;
import com.dl.dops.biz.resourcecenter.dal.sop.po.SopPO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-08-26 09:27
 */
public class SopHelper {

    public static SopBO cnvSopPO2DTO(SopPO input) {
        if (Objects.isNull(input)) {
            return null;
        }
        SopBO result = new SopBO();
        fillSopDTO(input, result);
        return result;
    }

    public static void fillSopDTO(SopPO input, SopBO result) {
        result.setId(input.getId());
        result.setSopId(input.getSopId());
        result.setName(input.getName());
        result.setSopType(input.getSopType());
        result.setStatus(input.getStatus());
        result.setRemark(input.getRemark());
        result.setCategory1(input.getCategory1());
        result.setCategory2(input.getCategory2());
        result.setCreateDt(input.getCreateDt());
        result.setCreatorName(input.getCreatorName());
    }
}
