package com.dl.dops.biz.system.manager.role;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.system.dal.role.po.UserRolePO;
import com.dl.dops.biz.system.manager.role.dto.RoleIdDTO;
import com.dl.dops.biz.system.manager.user.bo.UserRolesParamBO;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 14:16
 */
public interface UserRoleManager extends IService<UserRolePO> {

    /**
     * 保存用户-角色配置信息
     *
     * @param bo
     */
    void saveUserRoles(UserRolesParamBO bo);

    List<RoleIdDTO> findByUserId(Long userId);

}
