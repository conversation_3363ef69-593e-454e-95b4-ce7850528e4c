package com.dl.dops.biz.common.forest.aiservice;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.share.digitalasset.DaVirtualManAuthDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManPageRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceAuthorizeDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceChannelDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDetailDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoicePageDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceUpdateDTO;
import com.dl.aiservice.share.digitalman.DigitalManInfoDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainJobDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainJobPageQueryDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultPageQueryDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultUpdateNameParamDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.dops.biz.common.constant.Const;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @describe: AiServiceManager
 * @author: zhousx
 * @date: 2023/3/16 18:07
 */
@Slf4j
@Service
public class AiServiceManager {

    @Resource
    private AiServiceClient aiServiceClient;

    private ExecutorService ttsSerialPool = new ThreadPoolExecutor(1, 1, 30, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(512));

    /**
     * 分页查询数字人列表
     *
     * @param param
     * @return
     */
    public ResultPageModel<DaVirtualManDTO> pageVm(DaVirtualManPageRequestDTO param) {
        return aiServiceClient.pageVm(param);
    }

    /**
     * 通过vmBizId查询数字人详情
     *
     * @param vmBizId
     * @return
     */
    public ResultModel<DigitalManInfoDTO> vmDetail(Long vmBizId) {
        Assert.notNull(vmBizId, "vmBizId不可为空。");
        ResultModel<DigitalManInfoDTO> query = aiServiceClient.query(vmBizId);
        return query;
    }

    /**
     * 保存或更新数字人数据
     *
     * @param param
     * @return
     */
    public ResultModel<String> vmSaveOrUpdate(DaVirtualManDTO param) {
        return aiServiceClient.vmSaveOrUpdate(param);
    }

    /**
     * 查询场景列表
     *
     * @param param
     * @return
     */
    public ResultModel<List<DaVirtualManScenesDTO>> vmSceneList(DaVirtualManRequestDTO param) {
        param.setEnableFilter(Const.ONE);
        return aiServiceClient.vmSceneList(param);
    }

    /**
     * 数字人授权
     *
     * @param param
     * @return
     */
    public ResultModel vmAuth(DaVirtualManAuthDTO param) {
        return aiServiceClient.vmAuth(param);
    }

    /**
     * 新增或者修改场景信息
     *
     * @param param
     * @return
     */
    public ResultModel sceneSaveOrUpdate(DaVirtualManScenesDTO param) {
        return aiServiceClient.sceneSaveOrUpdate(param);
    }

    public ResultPageModel<DaVirtualVoiceDTO> voicePage(DaVirtualVoicePageDTO param) {
        return aiServiceClient.voicePage(param);
    }

    public ResultPageModel<DaVirtualVoiceDTO> pageSearch(DaVirtualVoicePageDTO param) {
        return aiServiceClient.pageSearch(param);
    }

    public ResultModel<DaVirtualVoiceDTO> voiceDetail(DaVirtualVoiceDetailDTO param) {
        return aiServiceClient.voiceDetail(param);

    }

    public ResultModel voiceSaveOrUpdate(DaVirtualVoiceDTO param) {
        return aiServiceClient.voiceSaveOrUpdate(param);

    }

    public ResultModel voiceAuthorize(DaVirtualVoiceAuthorizeDTO param) {
        return aiServiceClient.voiceAuthorize(param);

    }

    public ResultModel voiceEnable(DaVirtualVoiceUpdateDTO param) {
        return aiServiceClient.voiceEnable(param);
    }

    public ResultModel voiceDelete(DaVirtualVoiceUpdateDTO param) {
        return aiServiceClient.voiceDelete(param);
    }

    public ResultModel<List<DaVirtualVoiceChannelDTO>> voiceChannel() {
        return aiServiceClient.voiceChannel();
    }

    public ResultPageModel<VoiceTrainResultDTO> pageTrainResult(VoiceTrainResultPageQueryDTO queryDTO) {
        return aiServiceClient.pageTrainResult(queryDTO);
    }

    public Void updateTrainResultName(VoiceTrainResultUpdateNameParamDTO paramDTO) {
        ResultModel<Void> resultModel = aiServiceClient.updateTrainResultName(paramDTO);
        if (!resultModel.isSuccess()) {
            log.error("修改声音训练结果的训练名失败!,,,paramDTO:{}", JSONUtil.toJsonStr(paramDTO));
            throw BusinessServiceException.getInstance("修改声音训练结果的训练名失败。" + resultModel.getMessage());
        }
        return null;
    }

    public ResultPageModel<VoiceTrainJobDTO> pageTrainResultJob(VoiceTrainJobPageQueryDTO queryDTO) {
        return aiServiceClient.pageTrainResultJob(queryDTO);
    }

    public AudioTrainResponseDTO audioTrain(Integer channel, AudioTrainParamDTO paramDTO) {
        ResultModel<AudioTrainResponseDTO> resultModel = aiServiceClient.audioTrain(channel, paramDTO);
        if (Objects.isNull(resultModel)) {
            log.error("声音训练失败!,,,paramDTO:{}", JSONUtil.toJsonStr(paramDTO));
            throw BusinessServiceException.getInstance("声音训练失败。");
        }
        if (!resultModel.isSuccess()) {
            log.error("声音训练失败!,,,paramDTO:{}", JSONUtil.toJsonStr(paramDTO));
            throw BusinessServiceException.getInstance("声音训练失败。" + resultModel.getMessage());
        }
        return resultModel.getDataResult();
    }

    public TTSResponseDTO tts(Integer channel, TTSProduceParamDTO paramDTO) {
        Future<ResultModel<TTSResponseDTO>> future = ttsSerialPool.submit(() -> aiServiceClient.tts(channel, paramDTO));

        ResultModel<TTSResponseDTO> resultModel = null;
        try {
            resultModel = future.get();
        } catch (ExecutionException e) {
            Throwable throwable = e.getCause();
            log.error("tts语音合成失败!channel:{},,,paramDTO:{},,,e:", channel, JSONUtil.toJsonStr(paramDTO), e);
            if (throwable instanceof BusinessServiceException) {
                throw (BusinessServiceException) throwable;
            }
            throw BusinessServiceException.getInstance("语音合成失败。" + e.getMessage());
        } catch (Exception e) {
            log.error("tts语音合成失败!channel:{},,,paramDTO:{},,,e:", channel, JSONUtil.toJsonStr(paramDTO), e);
            throw BusinessServiceException.getInstance("语音合成失败。" + e.getMessage());
        }

        if (!resultModel.isSuccess()) {
            log.error("tts合成失败,channel:{},,,paramDTO:{},,,resultModel:{}", channel, JSONUtil.toJsonStr(paramDTO),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("语音合成失败。" + resultModel.getMessage());
        }
        return resultModel.getDataResult();
    }

}
