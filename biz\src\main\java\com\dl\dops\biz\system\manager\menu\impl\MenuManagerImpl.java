package com.dl.dops.biz.system.manager.menu.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.system.dal.menu.MenuMapper;
import com.dl.dops.biz.system.dal.menu.po.MenuFunctionTempPO;
import com.dl.dops.biz.system.dal.menu.po.MenuPO;
import com.dl.dops.biz.system.manager.function.dto.FunctionDTO;
import com.dl.dops.biz.system.manager.menu.MenuManager;
import com.dl.dops.biz.system.manager.menu.dto.MenuDTO;
import com.dl.dops.biz.system.manager.menu.helper.MenuTreeHelp;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 09:14
 */
@Component
@DS("dops")
public class MenuManagerImpl extends ServiceImpl<MenuMapper, MenuPO> implements MenuManager {

    @Override
    public List<MenuDTO> listAllMenus() {
        //获取系统菜单列表
        List<MenuPO> list = baseMapper.selectList(Wrappers.lambdaQuery(MenuPO.class).eq(MenuPO::getDisable, 0));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<MenuDTO> dtos = list.stream().map(t -> {
            MenuDTO dto = new MenuDTO();
            dto.setIcon(t.getIcon());
            dto.setParentId(t.getParentId());
            dto.setSort(t.getSort());
            dto.setUrl(t.getUrl());
            dto.setMenuId(t.getMenuId());
            dto.setDisable(t.getDisable());
            dto.setName(t.getName());
            dto.setLevel(t.getMenuLevel());
            return dto;
        }).collect(Collectors.toList());
        return dtos;
    }

    @Override
    public List<MenuDTO> listMenusAndFunctions() {
        //获取系统菜单列表
        List<MenuPO> list = baseMapper.selectList(Wrappers.lambdaQuery(MenuPO.class).eq(MenuPO::getDisable, 0));
        if (CollectionUtils.isNotEmpty(list)) {
            //获取菜单对应的功能
            List<MenuFunctionTempPO> mflist = baseMapper.listMenuFunction();
            //把菜单功能转化为map对象，方便后面转换到菜单对象中
            Map<String, Set<MenuFunctionTempPO>> menuFunctionMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(mflist)) {
                mflist.forEach(mf -> {
                    String menuId = String.valueOf(mf.getMenuId());
                    if (menuFunctionMap.containsKey(menuId)) {
                        menuFunctionMap.get(menuId).add(mf);
                    } else {
                        Set<MenuFunctionTempPO> set = new HashSet<>();
                        set.add(mf);
                        menuFunctionMap.put(menuId, set);
                    }
                });
            }

            //设置菜单对应的Function关系
            List<MenuDTO> dtos = list.stream().map(t -> {
                MenuDTO dto = new MenuDTO();
                dto.setIcon(t.getIcon());
                dto.setParentId(t.getParentId());
                dto.setSort(t.getSort());
                dto.setUrl(t.getUrl());
                dto.setMenuId(t.getMenuId());
                dto.setDisable(t.getDisable());
                dto.setName(t.getName());
                dto.setLevel(t.getMenuLevel());

                if (MapUtils.isNotEmpty(menuFunctionMap)) {
                    Set<MenuFunctionTempPO> set = menuFunctionMap.get(String.valueOf(t.getMenuId()));
                    if (CollectionUtils.isNotEmpty(set)) {
                        List<FunctionDTO> functions = set.stream().map(mf -> {
                            FunctionDTO privilegeFunctionDTO = new FunctionDTO();
                            privilegeFunctionDTO.setFunctionId(mf.getFunctionId());
                            privilegeFunctionDTO.setFunctionCode(mf.getFunctionCode());
                            privilegeFunctionDTO.setIcon(mf.getIcon());
                            privilegeFunctionDTO.setName(mf.getName());
                            privilegeFunctionDTO.setSort(mf.getSort());
                            return privilegeFunctionDTO;
                        }).sorted(Comparator.comparing(FunctionDTO::getSort)).collect(Collectors.toList());

                        dto.setFunctions(functions);
                    }
                }
                return dto;
            }).collect(Collectors.toList());

            //循环处理菜单对象，设置子菜单
            return MenuTreeHelp.listWithTree(dtos);
        }
        return null;
    }

}
