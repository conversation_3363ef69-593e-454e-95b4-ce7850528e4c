package com.dl.dops.biz.resourcecenter.manager.pack.bo;

import com.dl.dops.resourcecenter.pack.enums.RcPackSceneEnum;
import lombok.Data;

@Data
public class PackAddBO {

    /**
     * 服务包id
     */
    private Long packId;

    /**
     * 标题
     */
    private String title;

    /**
     * 所属一级分类id
     */
    private Long category1;

    /**
     * 所属二级分类id
     */
    private Long category2;

    /**
     * 场景概述
     */
    private String sceneOverview;

    /**
     * 适用行业
     */
    private Integer domain;

    /**
     * 详细描述
     */
    private String detailedDescription;

    /**
     * 运营投放建议
     */
    private String suggest;

    /**
     * 来源
     */
    @Deprecated
    private String source;

    /**
     * 场景 1-助你营，2-助你拍
     *
     * @see RcPackSceneEnum
     */
    private Integer scene;

}
