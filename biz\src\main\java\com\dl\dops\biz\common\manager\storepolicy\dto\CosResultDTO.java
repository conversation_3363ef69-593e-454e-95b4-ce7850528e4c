package com.dl.dops.biz.common.manager.storepolicy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CosResultDTO extends BaseTempCredentialDTO {

    @ApiModelProperty("腾讯云存储临时SecretId")
    String tmpSecretId;

    @ApiModelProperty("腾讯云存储临时SecretKey")
    String tmpSecretKey;

    @ApiModelProperty("腾讯云存储sessionToken")
    String sessionToken;

    @ApiModelProperty("腾讯云存储临时生效时间")
    Long startTime;

    @ApiModelProperty("腾讯云存储临时失效时间")
    Long expiredTime;

    @ApiModelProperty("腾讯云存储bucketId")
    String bucketId;

    @ApiModelProperty("腾讯云存储region")
    String region;

}
