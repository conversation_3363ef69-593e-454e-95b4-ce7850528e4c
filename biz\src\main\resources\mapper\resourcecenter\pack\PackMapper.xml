<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.dops.biz.resourcecenter.dal.pack.PackMapper">
    <resultMap id="resultMap" type="com.dl.dops.biz.resourcecenter.dal.pack.po.PackPO">
        <id column="id" property="id"/>
        <result column="pack_id" property="packId"/>
        <result column="title" property="title"/>
        <result column="category1" property="category1"/>
        <result column="category2" property="category2"/>
        <result column="scene_overview" property="sceneOverview"/>
        <result column="detailed_description" property="detailedDescription"/>
        <result column="suggest" property="suggest"/>
        <result column="status" property="status"/>
        <result column="scene" property="scene"/>
        <result column="source" property="source"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_dt" property="createDt"/>
        <result column="create_by" property="createBy"/>
        <result column="modify_dt" property="modifyDt"/>
        <result column="modify_by" property="modifyBy"/>
        <result column="creator_name" property="creatorName"/>
        <result column="modify_name" property="modifyName"/>
    </resultMap>

    <sql id="page_query">
        <if test="title != null and title != ''">
            and pack.title like concat(#{title}, '%')
        </if>
        <if test="createBy != null ">
            and pack.create_by = #{createBy}
        </if>
        <if test="domain != null">
            and pack.domain = #{domain}
        </if>
        <if test="status != null ">
            and pack.status = #{status}
        </if>
        <if test="category1 != null">
            and pack.category1 = #{category1}
        </if>
        <if test="category2 != null">
            and pack.category2 = #{category2}
        </if>
        <if test="scene != null">
            and pack.scene = #{scene}
        </if>
    </sql>

    <select id="pageQuery" parameterType="com.dl.dops.biz.resourcecenter.dal.pack.param.PackQuery"
            resultMap="resultMap">
        select *
        from pack
        where pack.is_deleted = 0
        <include refid="page_query"/>
        order by create_dt desc
        limit #{offset}, #{pageSize}
    </select>

    <select id="count" parameterType="com.dl.dops.biz.resourcecenter.dal.pack.param.PackQuery"
            resultType="java.lang.Integer">
        select count(pack.id)
        from pack
        where pack.is_deleted=0
        <include refid="page_query"/>
    </select>
</mapper>