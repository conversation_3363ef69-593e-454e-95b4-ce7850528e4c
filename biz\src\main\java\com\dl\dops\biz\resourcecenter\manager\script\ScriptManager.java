package com.dl.dops.biz.resourcecenter.manager.script;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.resourcecenter.dal.script.po.ScriptPO;
import com.dl.dops.biz.resourcecenter.manager.script.bo.CntScriptBO;
import com.dl.dops.biz.resourcecenter.manager.script.bo.CntScriptBatchBO;
import com.dl.dops.biz.resourcecenter.manager.script.bo.ScriptDetailBO;
import com.dl.dops.biz.resourcecenter.manager.script.bo.ScriptSearchBO;
import com.dl.framework.common.bo.ResponsePageQueryDO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【script】的数据库操作Service
 * @createDate 2022-06-14 16:09:16
 */
public interface ScriptManager extends IService<ScriptPO>, CommonService {

    Long addScript(CntScriptBO bo);

    ResponsePageQueryDO<List<ScriptDetailBO>> searchScripts(ScriptSearchBO searchBO);

    ScriptDetailBO getDetail(Long scriptId);

    void editScript(CntScriptBO bo);

    void batchSave(List<CntScriptBatchBO> batch);

    ResponsePageQueryDO<List<ScriptDetailBO>> pageQueryScripts(ScriptSearchBO searchBO);

    void publishOrCancel(Long scriptId);
}
