package com.dl.dops.biz.common.util;

import org.springframework.scheduling.support.CronSequenceGenerator;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-06-07 14:07
 */
public class CronHelper {

    /**
     * 计算下次执行时间
     *
     * @param cronExpress
     * @return
     */
    public static Date calNextExeTime(String cronExpress, Date sourceDate) {
        CronSequenceGenerator cronSequenceGenerator = new CronSequenceGenerator(cronExpress);
        Date nextExeTime = cronSequenceGenerator.next(sourceDate);
        return nextExeTime;
    }

}
