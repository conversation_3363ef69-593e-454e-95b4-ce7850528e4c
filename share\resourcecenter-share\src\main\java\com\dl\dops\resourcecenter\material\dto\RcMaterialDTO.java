package com.dl.dops.resourcecenter.material.dto;

import com.dl.dops.resourcecenter.tag.dto.TagInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-22 16:24
 */
@Data
@ApiModel("资源中心素材")
public class RcMaterialDTO implements Serializable {

    private static final long serialVersionUID = 5115509497003127871L;
    @ApiModelProperty("素材id")
    private String materialId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("摘要")
    private String remark;

    @ApiModelProperty("logo图地址")
    private String logoImg;

    @ApiModelProperty("分类Id,兼容前端")
    @Deprecated
    private String categoryId;

    @ApiModelProperty("标签信息列表")
    private List<TagInfoDTO> tagInfoDTOList;

    @ApiModelProperty("分类Ids")
    private String categoryIds;

    @ApiModelProperty("素材大小")
    private Integer size;

    @ApiModelProperty("素材类型")
    private Integer materialType;

    private Integer articleType;

    @ApiModelProperty("素材内容")
    private String content;

    @ApiModelProperty("创建人")
    private String creator;

    private Long createBy;

    private Long modifyBy;

    @ApiModelProperty("微信公众号文章地址")
    private String mpArticleSourceUrl;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("添加来源：0资源中心，1本地上传")
    private Integer createFrom = 0;

    @ApiModelProperty("发布状态 1-已发布，2-取消发布")
    private Integer publishStatus;

}
