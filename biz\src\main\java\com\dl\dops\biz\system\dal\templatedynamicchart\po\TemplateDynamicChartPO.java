package com.dl.dops.biz.system.dal.templatedynamicchart.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("template_dynamic_chart")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TemplateDynamicChartPO extends BasePO {

    private static final long serialVersionUID = 6398182063125294525L;
    Long id;

    /**
     * 图表类型
     */
    Integer type;

    /**
     * 图表名称
     */
    String name;

    /**
     * 封面图
     */
    String coverUrl;

    /**
     * 预览视频
     */
    String previewVideoUrl;

    /**
     * 参数信息
     */
    String paramJson;

    /**
     * 模板ID
     */
    String templateId;


}
