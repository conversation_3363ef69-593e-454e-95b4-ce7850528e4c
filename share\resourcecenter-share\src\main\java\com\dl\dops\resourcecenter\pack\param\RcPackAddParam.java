package com.dl.dops.resourcecenter.pack.param;

import com.dl.dops.resourcecenter.pack.enums.RcPackSceneEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("添加服务包")
public class RcPackAddParam implements Serializable {

    private static final long serialVersionUID = 2510677925409192569L;
    @ApiModelProperty("标题")
    @Length(min = 1, max = 100)
    private String title;

    @ApiModelProperty("所属一级分类id")
    @NotBlank(message = "所属一级分类不能为空")
    private String category1;

    @ApiModelProperty("所属二级分类id")
    private String category2;

    @ApiModelProperty("场景概述")
    @Length(max = 150, message = "场景概述最多150个字")
    private String sceneOverview;

    @ApiModelProperty("所属行业")
    private Integer domain;

    @ApiModelProperty("详细描述")
    @Length(max = 200, message = "详细描述最多200个字")
    private String detailedDescription;

    @ApiModelProperty("运营投放建议")
    @Length(max = 200, message = "运营投放建议最多200个字")
    private String suggest;

    /**
     * 场景 1-助你营，2-快视频
     *
     * @see RcPackSceneEnum
     */
    @ApiModelProperty("场景 1-助你营，2-快视频")
    @NotNull(message = "服务包场景不能为空")
    private Integer scene;

    @ApiModelProperty("链路列表")
    @NotEmpty(message = "链路定义不能为空")
    private List<RcPackChainPathSaveParam> chainPathList;

}
