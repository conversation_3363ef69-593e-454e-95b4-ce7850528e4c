package com.dl.dops.biz.system.manager.util;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjectUtil;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 09:37
 */
public class ValidationUtil {

    /**
     * 验证空
     */
    public static void isNull(Object obj, String entity, String parameter, Object value) {
        if (ObjectUtil.isNull(obj)) {
            String msg = entity + " 不存在: " + parameter + " is " + value;
            throw BusinessServiceException.getInstance(msg);
        }
    }

    /**
     * 验证是否为邮箱
     */
    public static boolean isEmail(String email) {
        return Validator.isEmail(email);
    }
}
