package com.dl.dops.resourcecenter.war.tencentcloud;

import com.dl.dops.Application;
import com.dl.dops.biz.common.tencentcloud.captcha.CaptchaService;
import com.dl.dops.biz.common.tencentcloud.captcha.bo.CaptchaBO;
import com.dl.dops.biz.common.tencentcloud.captcha.dto.CaptchaDTO;
import com.dl.dops.biz.common.tencentcloud.sms.SmsService;
import com.dl.dops.biz.common.tencentcloud.sms.bo.SmsBO;
import com.dl.dops.biz.common.tencentcloud.sms.dto.SmsDTO;
import com.dl.framework.common.utils.JsonUtils;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { Application.class})
public class TencentcloudTest{
    @Autowired
    CaptchaService captchaService;
    @Autowired
    private SmsService smsService;

    @Test
    public void validateCaptcha() throws TencentCloudSDKException {
        CaptchaBO captchaBO = new CaptchaBO();
        captchaBO.setIp("************");
        captchaBO.setRandStr("@rU0");
        captchaBO.setTicket("t03eBPSuFGil8c01atqKUlYk1A87YnxUYnXyWWjqUneTgSnXR57ni18i7SSpkNvVvY4sR5OMiLRlW6Y9uoJRp49ShuP47BxzH_N0owdFEi_evYJBaaF_iG7pQ**");
        captchaBO.setType("web");
        captchaBO.setSceneId(1L);
        CaptchaDTO captchaDTO = captchaService.validateCaptcha(captchaBO);

        System.out.println("图形验证码结果："+JsonUtils.toJSON(captchaDTO));
    }

    @Test
    public void send() throws TencentCloudSDKException {
        SmsBO smsBO = new SmsBO();
        smsBO.setSignName("定力科技");
        smsBO.setTemplateId("1385059");
        smsBO.setTemplateParamSet(new String[]{"1222","30"});
        smsBO.setPhoneNumberSet(new String[]{"13758135216"});
        SmsDTO resp = smsService.send(smsBO);
        System.out.println("短信发送结果："+JsonUtils.toJSON(resp));
    }


}
