package com.dl.dops.biz.resourcecenter.manager.poster;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.resourcecenter.dal.poster.po.PosterPO;
import com.dl.dops.biz.resourcecenter.manager.poster.bo.PosterBO;

import java.util.List;

public interface PosterManager extends IService<PosterPO>, CommonService {

    /**
     * 批量查询海报
     *
     * @param bizIds
     * @return
     */
    List<PosterBO> batchDetail(List<Long> bizIds);

}
