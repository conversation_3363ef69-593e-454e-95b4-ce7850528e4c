package com.dl.dops.resourcecenter.productinstitute.dto;

import java.io.Serializable;
import java.lang.Long;
import java.lang.String;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * DTO: 产品发行机构表
 */
@Data
public class ProductIssueInstituteDTO implements Serializable {

    @ApiModelProperty("表ID")
    private String id;

    @ApiModelProperty("产品机构id")
    private String instituteId;

    @ApiModelProperty("外部代码，用于映射业务，关联金融数据，获取相关资讯")
    private String extCode;

    @ApiModelProperty("机构全名")
    private String fullName;

    @ApiModelProperty("机构简称")
    private String abbrName;

    @ApiModelProperty("机构logo小图标")
    private String logoIconUrl;

    @ApiModelProperty("机构logo高清图")
    private String logoHighResUrl;

    @ApiModelProperty("创建时间")
    private Date createDt;

    @ApiModelProperty("修改时间")
    private Date modifyDt;

    @ApiModelProperty("创建用户")
    private Long createBy;

    @ApiModelProperty("创建用户")
    private Long modifyBy;


}
