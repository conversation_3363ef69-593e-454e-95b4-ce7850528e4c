package com.dl.dops.resourcecenter.sop.dto;

import com.dl.dops.resourcecenter.material.dto.RcMaterialDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-07 14:16
 */
@Data
@ApiModel("sop事件内容视图对象")
public class RcSopEventCntDTO implements Serializable {

    private static final long serialVersionUID = -4836747452922360576L;
    @ApiModelProperty("内容类型 1素材 2海报")
    private Integer contentType;

    @ApiModelProperty("内容id")
    private String contentId;

    @ApiModelProperty("素材")
    private RcMaterialDTO material;
}
