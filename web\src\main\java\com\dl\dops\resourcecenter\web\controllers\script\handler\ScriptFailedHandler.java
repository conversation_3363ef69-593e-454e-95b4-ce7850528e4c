package com.dl.dops.resourcecenter.web.controllers.script.handler;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;

/**
 * @describe: ScriptFailedHandler
 * @author: zhousx
 * @date: 2022/6/21 19:37
 */
public class ScriptFailedHandler implements RowWriteHandler {
    @Override
    public void afterRowDispose(RowWriteHandlerContext context) {
        if (context.getRowIndex() > 6) {
            Sheet sheet = context.getWriteSheetHolder().getSheet();
            for (int i = 0; i < 5; i++) {
                Cell cell = sheet.getRow(6).getCell(i);
                Font font = sheet.getWorkbook().createFont();
                font.setColor(Font.COLOR_RED);
                font.setFontHeightInPoints((short) 16);
                CellStyle cellStyle = sheet.getWorkbook().createCellStyle();
                cellStyle.setFont(font);
                cell.setCellStyle(cellStyle);
            }
            Cell cell = context.getRow().getCell(4);
            Font font = sheet.getWorkbook().createFont();
            font.setColor(Font.COLOR_RED);
            font.setFontHeightInPoints((short) 11);
            CellStyle cellStyle = sheet.getWorkbook().createCellStyle();
            cellStyle.setFont(font);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(cellStyle);
        }
    }
}
