package com.dl.dops.magicvideo.web.chartlet.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-12 19:04
 */
@Data
public class ChartletInfoAddParam {

    @NotBlank(message = "贴图名称不能为空")
    @ApiModelProperty("名称")
    private String name;

    @NotBlank(message = "内容url不能为空")
    @ApiModelProperty("内容url")
    private String contentUrl;

    @NotBlank(message = "分辨率不能为空")
    @ApiModelProperty("分辨率")
    private String resolutionRatio;

    @ApiModelProperty("封面图")
    private String coverImg;

    @ApiModelProperty("时长，毫秒")
    private Long duration;

    @NotNull(message = "分类不能为空")
    @ApiModelProperty("分类编码")
    private Integer category;
}
