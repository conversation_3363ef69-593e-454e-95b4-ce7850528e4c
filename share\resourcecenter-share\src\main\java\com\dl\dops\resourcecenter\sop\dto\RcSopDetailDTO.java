package com.dl.dops.resourcecenter.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 资源中心sop详情
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-11 11:46
 */
@Data
@ApiModel("资源中心sop详情")
public class RcSopDetailDTO extends RcSopDTO implements Serializable {

    @ApiModelProperty("sop事件详情列表")
    private List<RcSopEventDetailDTO> sopEventDetailList;

}
