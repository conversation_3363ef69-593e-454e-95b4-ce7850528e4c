package com.dl.dops.resourcecenter.tag.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-07 16:40
 */
@Data
public class TagGroupDetailParam {

    @NotBlank(message = "标签组id不能为空")
    @ApiModelProperty("标签组id")
    private String tagGroupId;

    @ApiModelProperty("是否需要查询标签关联数量，0-否，1-是，默认为0")
    private Integer needCountTagRela = 0;
}
