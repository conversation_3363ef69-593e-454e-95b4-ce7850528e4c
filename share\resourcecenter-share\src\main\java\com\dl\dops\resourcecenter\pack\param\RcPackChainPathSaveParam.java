package com.dl.dops.resourcecenter.pack.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 13:45
 */
@Data
@ApiModel("服务包链路保存对象")
public class RcPackChainPathSaveParam implements Serializable {

    private static final long serialVersionUID = 6375032837844257852L;
    @ApiModelProperty("链路id")
    private String chainPathId;

    @ApiModelProperty("链路名称")
    @NotBlank(message = "链路名称不能为空")
    private String name;

    @ApiModelProperty("排序")
    @NotNull(message = "链路排序不能为空")
    private Integer sort;
}
