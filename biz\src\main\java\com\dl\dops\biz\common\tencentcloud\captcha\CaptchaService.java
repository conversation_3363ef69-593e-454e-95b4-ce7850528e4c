package com.dl.dops.biz.common.tencentcloud.captcha;

import com.dl.dops.biz.common.tencentcloud.captcha.bo.CaptchaBO;
import com.dl.dops.biz.common.tencentcloud.captcha.dto.CaptchaDTO;
import com.dl.dops.biz.common.tencentcloud.captcha.properties.CaptchaProperties;
import com.dl.dops.biz.common.tencentcloud.properties.TencentCloudProperties;
import com.tencentcloudapi.captcha.v20190722.CaptchaClient;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultRequest;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultResponse;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CaptchaService implements InitializingBean {
    private CaptchaClient client = null;

    @Autowired
    private CaptchaProperties captchaProperties;
    @Autowired
    private TencentCloudProperties properties;

    @Override
    public void afterPropertiesSet() throws Exception {
        Credential cred = new Credential(properties.getApi().getSecretId(), properties.getApi().getSecretKey());
        this.client = new CaptchaClient(cred, "");
    }

    /**
     * 腾讯验证码校验
     *
     * @param randStr       前端回调函数返回的随机字符串
     * @param token(Ticket) 前端回调函数返回的用户验证票据
     *                      DescribeCaptchaResultRequest 腾讯云参数说明
     *                      Action	是	String	公共参数，本接口取值：DescribeCaptchaResult。
     *                      Version	是	String	公共参数，本接口取值：2019-07-22。
     *                      Region	否	String	公共参数，本接口不需要传递此参数。
     *                      CaptchaType	是	Integer	固定填值：9。可在控制台配置不同验证码类型。
     *                      Ticket	是	String	前端回调函数返回的用户验证票据
     *                      UserIp	是	String	透传业务侧获取到的验证码使用者的IP
     *                      Randstr	是	String	前端回调函数返回的随机字符串
     *                      CaptchaAppId	是	Integer	验证码应用ID
     *                      AppSecretKey	是	String	用于服务器端校验验证码票据的验证密钥，请妥善保密，请勿泄露给第三方
     *                      BusinessId	否	Integer	业务 ID，网站或应用在多个业务中使用此服务，通过此 ID 区分统计数据
     *                      SceneId	否	Integer	场景 ID，网站或应用的业务下有多个场景使用此服务，通过此 ID 区分统计数据
     *                      MacAddress	否	String	mac 地址或设备唯一标识
     *                      Imei	否	String	手机设备号
     *                      NeedGetCaptchaTime	否	Integer	是否返回前端获取验证码时间，取值1：需要返回
     * @return
     */
    public CaptchaDTO validateCaptcha(CaptchaBO captchaBO) throws TencentCloudSDKException {
        DescribeCaptchaResultRequest req = new DescribeCaptchaResultRequest();
        if ("web".equals(captchaBO.getType())) {
            // 验证码应用ID
            req.setCaptchaAppId(captchaProperties.getWeb().getCaptchaAppId());
            // 用于服务器端校验验证码票据的验证密钥
            req.setAppSecretKey(captchaProperties.getWeb().getAppSecretKey());
        } else if ("mini".equals(captchaBO.getType())) {
            // 验证码应用ID
            req.setCaptchaAppId(captchaProperties.getMini().getCaptchaAppId());
            // 用于服务器端校验验证码票据的验证密钥
            req.setAppSecretKey(captchaProperties.getMini().getAppSecretKey());
        }

        // 	固定填值：9。可在控制台配置不同验证码类型
        req.setCaptchaType(9L);
        // 	前端回调函数返回的用户验证票据
        req.setTicket(captchaBO.getTicket());
        // 透传业务侧获取到的验证码使用者的IP,请自己通过业务获取请求者的ip地址
        req.setUserIp(captchaBO.getIp());
        // 前端回调函数返回的随机字符串
        req.setRandstr(captchaBO.getRandStr());
        // 是否返回前端获取验证码时间，取值1：需要返回
        req.setNeedGetCaptchaTime(1L);
        // 场景 ID，网站或应用的业务下有多个场景使用此服务，通过此 ID 区分统计数据
        req.setSceneId(captchaBO.getSceneId());
        DescribeCaptchaResultResponse resp = client.DescribeCaptchaResult(req);

        CaptchaDTO captchaDTO = null;
        if (resp != null) {
            captchaDTO = new CaptchaDTO();
            captchaDTO.setCaptchaCode(resp.getCaptchaCode());
            captchaDTO.setCaptchaMsg(resp.getCaptchaMsg());
            captchaDTO.setGetCaptchaTime(resp.getGetCaptchaTime());
            captchaDTO.setEvilLevel(resp.getEvilLevel());
            captchaDTO.setRequestId(resp.getRequestId());
        }

        return captchaDTO;
    }
}