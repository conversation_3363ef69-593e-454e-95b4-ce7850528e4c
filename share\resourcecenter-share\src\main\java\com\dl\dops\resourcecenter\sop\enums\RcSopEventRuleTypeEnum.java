package com.dl.dops.resourcecenter.sop.enums;

import java.util.Objects;

/**
 * sop事件 时间规则类型
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-07 09:58
 */
public enum RcSopEventRuleTypeEnum {

    APPOINTED_TIME(1, "定时"),
    PERIOD(2, "周期");

    private Integer value;

    private String desc;

    RcSopEventRuleTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static RcSopEventRuleTypeEnum parse(Integer value) {
        if (Objects.isNull(value)) {
            return null;
        }
        for (RcSopEventRuleTypeEnum ruleTypeEnum : RcSopEventRuleTypeEnum.values()) {
            if (ruleTypeEnum.value.equals(value)) {
                return ruleTypeEnum;
            }
        }
        return null;
    }
}
