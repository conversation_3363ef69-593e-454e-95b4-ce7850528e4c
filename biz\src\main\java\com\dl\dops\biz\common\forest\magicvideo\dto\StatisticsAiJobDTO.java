package com.dl.dops.biz.common.forest.magicvideo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-14 10:29
 */
@Data
@ApiModel("ai任务统计对象")
public class StatisticsAiJobDTO implements Serializable {
    private static final long serialVersionUID = -536670224217298501L;

    @ApiModelProperty("记录id")
    private String recordId;

    @ApiModelProperty("租户编码")
    private String tenantCode;

    @ApiModelProperty("ai任务类型:1-数字人，2-TTS")
    private Integer aiJobType;

    @ApiModelProperty("ai任务数")
    private Integer jobCount;

    @ApiModelProperty("总的向上取整分钟数")
    private Long totalCeilingMinutes;
    
    @ApiModelProperty("总的毫秒")
    private Long totalTimeMillis;

    @ApiModelProperty("统计时间")
    private Date statisticsTime;

    @ApiModelProperty("创建时间")
    private Date createDt;

    @ApiModelProperty("修改时间")
    private Date modifyDt;

    @ApiModelProperty("租户名称")
    private String tenantName;

}
