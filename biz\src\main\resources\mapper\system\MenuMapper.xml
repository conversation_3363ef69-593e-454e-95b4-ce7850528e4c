<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.dops.biz.system.dal.menu.MenuMapper">
    <resultMap id="BaseResultMap" type="com.dl.dops.biz.system.dal.menu.po.MenuFunctionTempPO">
        <result column="menuId" property="menuId"/>
        <result column="functionId" property="functionId"/>
        <result column="name" property="name"/>
        <result column="icon" property="icon"/>
        <result column="functionCode" property="functionCode"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <select id="listMenuFunction" resultMap="BaseResultMap">
        SELECT mf.menu_id AS menuId,mf.function_id AS functionId,f.function_code AS functionCode,f.name,f.icon,f.sort
        FROM sys_menu m,sys_function f,sys_menu_function mf
        WHERE m.menu_id = mf.menu_id
          AND   f.function_id = mf.function_id
          AND   m.disable = 0
    </select>

</mapper>