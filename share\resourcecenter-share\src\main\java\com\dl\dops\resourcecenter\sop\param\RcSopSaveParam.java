package com.dl.dops.resourcecenter.sop.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-10-09 09:23
 */
@Data
@ApiModel("资源中心sop保存参数")
public class RcSopSaveParam {

    @ApiModelProperty("sopId")
    private String sopId;

    @ApiModelProperty("名称")
    @Length(min = 1, max = 50, message = "sop名称长度需要在1-50")
    private String name;

    @ApiModelProperty("备注")
    @Length(max = 40, message = "sop描述不能超过40个字")
    private String remark;

    @ApiModelProperty("一级分类id")
    private String category1;

    @ApiModelProperty("二级分类id")
    private String category2;

    @ApiModelProperty("sop类型")
    @NotNull(message = "sop类型不能为空")
    private Integer sopType;

    @ApiModelProperty("sop目标规则描述")
    @Length(max = 100, message = "sop目标规则描述不能超过100个字")
    @NotBlank(message = "sop目标规则描述不能为空")
    private String targetConfDesc;

}
