package com.dl.dops.biz.resourcecenter.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.resourcecenter.mq.dto.TenantStatusChangeMsgDTO;
import com.dl.dops.biz.resourcecenter.dal.tenant.po.TenantAuthPO;
import com.dl.dops.biz.resourcecenter.manager.tenantauth.TenantAuthManager;
import com.dl.dops.biz.resourcecenter.manager.tenantauth.enums.TenantAuthStatusEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 租户状态变更事件的消费者
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-30 10:31
 */
@Component
public class TenantStatusChangeConsumer {
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantStatusChangeConsumer.class);

    @Resource
    private TenantAuthManager tenantAuthManager;

    @StreamListener("tenantstatuschangeconsumer")
    public void consume(String msg) {
        LOGGER.info("收到租户状态变更的消息，input:{}", msg);
        TenantStatusChangeMsgDTO msgDTO = JSONUtil.toBean(msg, TenantStatusChangeMsgDTO.class);

        TenantAuthPO existPO = tenantAuthManager.getOne(Wrappers.lambdaQuery(TenantAuthPO.class)
                .eq(TenantAuthPO::getTenantCode, msgDTO.getTenantCode()));

        Integer authStatus = Const.ONE.equals(msgDTO.getStatus()) ?
                TenantAuthStatusEnum.ENABLE.getCode() :
                TenantAuthStatusEnum.DISABLE.getCode();
        //已有租户授权信息
        if (Objects.nonNull(existPO)) {
            tenantAuthManager.update(Wrappers.lambdaUpdate(TenantAuthPO.class)
                    .eq(TenantAuthPO::getTenantCode, existPO.getTenantCode()).set(TenantAuthPO::getStatus, authStatus)
                    .set(TenantAuthPO::getModifyDt, new Date()));
            return;
        }
        //没有租户授权信息则新增一条
        TenantAuthPO insertAuthPO = new TenantAuthPO();
        insertAuthPO.setTenantCode(msgDTO.getTenantCode());
        insertAuthPO.setStatus(authStatus);
        insertAuthPO.setCreateBy(Const.ONE_LONG);
        insertAuthPO.setModifyBy(Const.ONE_LONG);
        insertAuthPO.setCreateDt(new Date());
        insertAuthPO.setModifyDt(new Date());
        tenantAuthManager.save(insertAuthPO);
        LOGGER.info("租户状态变更消息处理完毕，msg:{}", msg);
    }
}
