package com.dl.dops.resourcecenter.tenantauth.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 资源中心租户授权token对象
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-08 15:40
 */
@Data
public class RcTenantAuthTokenDTO implements Serializable {
    private static final long serialVersionUID = 6046853545093945991L;

    /**
     * 租户号
     */
    private String tenantCode;

    /**
     * userId
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * token生成时间
     */
    private Date tokenCreateDt;
}
