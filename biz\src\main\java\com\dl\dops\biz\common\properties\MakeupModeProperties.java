package com.dl.dops.biz.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@ConfigurationProperties(prefix = "dl.makeup")
@Configuration
public class MakeupModeProperties {

    private List<AppProperties> apps;

    @Data
    public static class AppProperties {
        String appId;
        String licenseKey;
        String token;
    }
}
