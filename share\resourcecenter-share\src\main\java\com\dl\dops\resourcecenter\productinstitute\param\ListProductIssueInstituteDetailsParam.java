package com.dl.dops.resourcecenter.productinstitute.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel("查询产品机构请求参数")
public class ListProductIssueInstituteDetailsParam {

    @ApiModelProperty("机构ID")
    @NotEmpty
    private List<String> instituteIds;

}
