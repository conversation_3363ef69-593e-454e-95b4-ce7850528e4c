<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="20220607151256" author="wangtc">
        <!-- 预判断 -->
        <preConditions onFail="CONTINUE">
            <tableExists tableName="liquibase_test"/>
            <not>
                <columnExists tableName="liquibase_test" columnName="key"/>
            </not>

        </preConditions>
        <comment>增加key</comment>
        <addColumn tableName="liquibase_test">
            <column name="key" type="${string.type}(100)" defaultValue=""/>
        </addColumn>
        <rollback>
            <dropColumn tableName="liquibase_test" columnName="key"/>
        </rollback>
        <modifySql dbms="mysql">
            <replace replace="VARCHAR(100)" with="VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci"/>
        </modifySql>

    </changeSet>

</databaseChangeLog>