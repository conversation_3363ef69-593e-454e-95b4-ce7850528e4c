package com.dl.dops.web.common.util;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-28 10:00
 */
public class HttpUtil {

    private static final Pattern HTTP_PAT = Pattern
            .compile("^(http|https)(://)(\\w+(-\\w+)*)(\\.(\\w+(-\\w+)*))+((:\\d+)?)(/(\\w+(-\\w+)*))*(\\.?(\\w)*)",
                    Pattern.CASE_INSENSITIVE);

    /**
     * 校验url的真实性
     *
     * @param url
     * @return
     */
    public static boolean checkUrl(String url) {
        if (null != url) {
            Matcher m = HTTP_PAT.matcher(url);
            return m.find(0);
        }
        return false;
    }

    /**
     * 从request里取得真是ip
     *
     * @param request
     * @return
     */
    public static String getIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (ip == null) {
            ip = "";
        }
        if ("127.0.0.1".equals(ip) || "localhost".equalsIgnoreCase(ip) || "0:0:0:0:0:0:0:1".equals(ip)) {
            try {
                InetAddress addr = InetAddress.getLocalHost();
                ip = addr.getHostAddress();
            } catch (UnknownHostException e) {
                return "";
            }
        }

        // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ip != null && ip.length() > 15 && ip.indexOf(",") > 0) { // "***.***.***.***".length()
            // = 15
            ip = ip.substring(0, ip.indexOf(","));
        }
        return ip;
    }

    /**
     * 获取当前页面url
     *
     * @param request
     * @return String
     */
    public static String getRequestUrl(HttpServletRequest request) {
        StringBuffer urlBuilder = new StringBuffer(request.getRequestURI());
        String data = getQueryString(request);
        if (StringUtils.isNotBlank(data)) {
            urlBuilder.append("?").append(data);
        }
        return urlBuilder.toString();
    }

    /**
     * 获取请求参数  格式：keyx=valuex&keyy=valuey
     *
     * @param request
     * @return
     */
    public static String getQueryString(final HttpServletRequest request) {
        StringBuffer urlBuilder = new StringBuffer();
        Map<?, ?> params = request.getParameterMap();
        Iterator<?> it = params.entrySet().iterator();
        int i = 0;
        while (it.hasNext()) {
            Map.Entry<?, ?> pairs = (Map.Entry<?, ?>) it.next();
            if (i > 0) {
                urlBuilder.append("&");
            }
            String[] values = (String[]) pairs.getValue();
            String valueStr = "";
            for (int j = 0; j < values.length; j++) {
                valueStr = (j == values.length - 1) ? valueStr + values[j] : valueStr + values[j] + ",";
            }
            urlBuilder.append(pairs.getKey()).append("=").append(valueStr);
            i++;
        }
        return urlBuilder.toString();
    }

    /**
     * 对url编码
     *
     * @param msg
     * @param charset
     * @return
     */
    public static String urlEncode(String msg, String charset) {
        if (null == msg) {
            return null;
        }

        try {
            return URLEncoder.encode(msg, charset);
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }

    /**
     * 获取cookie
     *
     * @param request
     * @param key
     * @return
     */
    public static Cookie getCookie(HttpServletRequest request, String key) {
        if (null == request) {
            return null;
        }

        if (StringUtils.isBlank(key)) {
            return null;
        }

        Cookie[] cookies = request.getCookies();
        if (null == cookies) {
            return null;
        }

        for (Cookie cookie : cookies) {
            if (StringUtils.equals(key, cookie.getName())) {
                return cookie;
            }
        }

        return null;
    }

    /**
     * 获取header
     *
     * @param request
     * @param head
     * @return
     */
    public static String getHeader(HttpServletRequest request, String head) {
        if (StringUtils.isBlank(head)) {
            return "";
        }

        if (null == request) {
            return "";
        }

        return request.getHeader(head);
    }

    /**
     * 获取header 任何一个有值则返回
     *
     * @param request
     * @param heads
     * @return
     */
    public static String getAnyHeader(HttpServletRequest request, String[] heads) {
        if (null == request) {
            return "";
        }

        for (int i = 0; i < heads.length; i++) {
            String val = request.getHeader(heads[i]);
            if (StringUtils.isNotBlank(val)) {
                return val;
            }
        }

        return "";
    }

    /**
     * 获取子域名
     *
     * @param url
     * @param level
     * @return
     */
    public static String getSubDomain(String url, int level) {
        if (StringUtils.isBlank(url)) {
            return "";
        }

        if (Pattern.matches("\\d+\\.\\d+\\.\\d+\\.\\d+", url)) {
            return url;
        }

        String[] domains = url.split("\\.");
        if (domains.length <= level + 1) {
            return url;
        }

        return StringUtils.join(domains, '.', domains.length - level - 1, domains.length);
    }

    /**
     * 从url获取参数map
     *
     * @param url
     * @return
     */
    public static Map<String, String> getParamPairsFromUrl(String url) {
        Map<String, String> result = new HashMap<>();
        if (StringUtils.isBlank(url)) {
            return result;
        }

        String[] urlSplit = url.split("\\?");
        //拼接原参数
        for (int urlIndex = 1; urlIndex < urlSplit.length; urlIndex++) {
            String[] origin = urlSplit[urlIndex].split("&");
            for (int originIndex = 0; originIndex < origin.length; originIndex++) {
                String[] singleParam = origin[originIndex].split("=");
                if (singleParam.length != 2) {
                    continue;
                }

                result.put(singleParam[0], singleParam[1]);
            }
        }

        return result;
    }

}
