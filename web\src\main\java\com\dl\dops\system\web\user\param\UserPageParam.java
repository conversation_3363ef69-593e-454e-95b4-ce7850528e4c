package com.dl.dops.system.web.user.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * @describe: SysUserPageParam
 * @author: zhousx
 * @date: 2022/7/5 14:20
 */
@Data
@ApiModel("员工搜索条件")
public class UserPageParam extends AbstractPageParam {
    @ApiModelProperty("账号")
    @Size(max = 50)
    private String account;

    @ApiModelProperty("姓名")
    @Size(max = 50)
    private String userName;

    private Integer status;
}
