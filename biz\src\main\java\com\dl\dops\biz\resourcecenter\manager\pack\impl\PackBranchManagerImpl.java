package com.dl.dops.biz.resourcecenter.manager.pack.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.resourcecenter.dal.pack.PackBranchMapper;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackBranchBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackBranchSaveBO;
import com.dl.dops.biz.resourcecenter.manager.pack.helper.PackHelper;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackBranchPO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.resourcecenter.manager.pack.PackBranchManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 10:06
 */
@Component
public class PackBranchManagerImpl extends ServiceImpl<PackBranchMapper, PackBranchPO> implements PackBranchManager {

    @Resource
    private HostTimeIdg hostTimeIdg;

    @Override
    public PackBranchBO save(PackBranchSaveBO saveBO) {
        Assert.notNull(saveBO.getChainPathId(), "链路id不能为空");
        if (Objects.isNull(saveBO.getBranchId())) {
            PackBranchPO insertPO = new PackBranchPO();
            insertPO.setChainPathId(saveBO.getChainPathId());
            insertPO.setBranchId(hostTimeIdg.generateId().longValue());
            insertPO.setName(saveBO.getName());
            insertPO.setSort(saveBO.getSort());
            baseMapper.insert(insertPO);
            return PackHelper.cnvPackBranchPO2DTO(insertPO);
        }
        PackBranchPO existPO = baseMapper.selectOne(
                Wrappers.lambdaQuery(PackBranchPO.class).eq(PackBranchPO::getBranchId, saveBO.getBranchId())
                        .eq(PackBranchPO::getIsDeleted, Const.ZERO));
        Assert.notNull(existPO, "分支" + saveBO.getName() + "不存在");
        existPO.setSort(saveBO.getSort());
        existPO.setName(saveBO.getName());
        baseMapper.updateById(existPO);
        return PackHelper.cnvPackBranchPO2DTO(existPO);
    }

    @Override
    public List<PackBranchBO> listByChainPathId(Long chainPathId) {
        Assert.notNull(chainPathId, "链路id不能为空");
        List<PackBranchPO> branchPOList = baseMapper.selectList(
                Wrappers.lambdaQuery(PackBranchPO.class).eq(PackBranchPO::getChainPathId, chainPathId)
                        .eq(PackBranchPO::getIsDeleted, Const.ZERO));
        if (CollectionUtils.isEmpty(branchPOList)) {
            return Collections.emptyList();
        }

        return branchPOList.stream().map(PackHelper::cnvPackBranchPO2DTO).collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<PackBranchBO>> mapByChainPathIds(List<Long> chainPathIdList) {
        Assert.isTrue(CollectionUtils.isNotEmpty(chainPathIdList), "链路id不能为空");

        List<PackBranchPO> branchPOList = baseMapper.selectList(
                Wrappers.lambdaQuery(PackBranchPO.class).in(PackBranchPO::getChainPathId, chainPathIdList)
                        .eq(PackBranchPO::getIsDeleted, Const.ZERO));
        if (CollectionUtils.isEmpty(branchPOList)) {
            return Collections.emptyMap();
        }

        return branchPOList.stream().map(PackHelper::cnvPackBranchPO2DTO)
                .collect(Collectors.groupingBy(PackBranchBO::getChainPathId));
    }
}
