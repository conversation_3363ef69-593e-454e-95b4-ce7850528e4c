package com.dl.dops.biz.resourcecenter.manager.pack;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackBranchPO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackBranchBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackBranchSaveBO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 10:05
 */
public interface PackBranchManager extends IService<PackBranchPO> {

    PackBranchBO save(PackBranchSaveBO saveBO);

    List<PackBranchBO> listByChainPathId(Long chainPathId);

    /**
     * 根据链路id列表查询链路map
     *
     * @param chainPathIdList
     * @return key-链路id  value-分支id列表
     */
    Map<Long, List<PackBranchBO>> mapByChainPathIds(List<Long> chainPathIdList);
}
