package com.dl.dops.system.web.virtualman.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SceneUpdateParam {

    @NotNull(message = "数字人场景主键ID必填")
    @ApiModelProperty(value = "数字人场景主键ID", required = true)
    Long id;

    @NotNull(message = "数字人唯一标识必填")
    @ApiModelProperty(value = "数字人唯一标识bizId", required = true)
    Long vmBizId;

    @NotBlank(message = "数字人场景名称必填")
    @ApiModelProperty(value = "数字人场景名称", required = true)
    String sceneName;

    @NotNull(message = "数字人厂商必填")
    @ApiModelProperty(value = "数字人来源渠道", required = true)
    Integer channel;

    @NotBlank(message = "数字人来源ID必填")
    @ApiModelProperty(value = "数字人来源编码", required = true)
    String vmCode;

    @NotBlank(message = "数字人场景ID必填")
    @ApiModelProperty(value = "数字人场景ID", required = true)
    String sceneId;

    @ApiModelProperty("场景封面地址")
    String coverUrl;

    @ApiModelProperty("服装信息：0 个人服饰 1 黑衣服 2 蓝礼服")
    Integer cloth;

    @ApiModelProperty("姿态信息：1 坐姿; 2 半身站姿; 3 全身站姿")
    Integer pose;

    @ApiModelProperty("分辨率：1 1080x1920; 2 1920x1080")
    Integer resolution;

    @ApiModelProperty("场景样例视频地址")
    String exampleUrl;

    @ApiModelProperty("场景样例文本")
    String exampleText;

    @ApiModelProperty("场景样例时长，毫秒")
    Integer exampleDuration;

}
