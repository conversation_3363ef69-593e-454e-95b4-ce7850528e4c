package com.dl.dops.resourcecenter.pack.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 16:14
 */
@Data
@ApiModel("服务包链路内容保存参数")
public class RcPackChainPathContentSaveParam implements Serializable {

    private static final long serialVersionUID = 883939638740304500L;
    @ApiModelProperty("服务包id")
    @NotBlank(message = "服务包id不能为空")
    private String packId;

    @ApiModelProperty("链路id")
    @NotBlank(message = "链路id不能为空")
    private String chainPathId;

    @ApiModelProperty("分支列表")
    @NotEmpty(message = "分支列表不能为空")
    private List<RcPackBranchSaveParam> packBranchList;
}
