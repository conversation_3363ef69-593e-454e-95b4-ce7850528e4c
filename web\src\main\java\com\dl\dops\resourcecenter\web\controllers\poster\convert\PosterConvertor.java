package com.dl.dops.resourcecenter.web.controllers.poster.convert;

import com.dl.dops.biz.resourcecenter.manager.poster.bo.PosterBO;
import com.dl.dops.resourcecenter.poster.dto.RcPosterDTO;

import java.util.Objects;

/**
 * @ClassName PosterConvertor
 * @Description
 * <AUTHOR>
 * @Date 2022/4/12 19:00
 * @Version 1.0
 **/
public class PosterConvertor {

    public static RcPosterDTO convert(PosterBO input) {
        if (Objects.isNull(input)) {
            return null;
        }

        RcPosterDTO result = new RcPosterDTO();
        result.setBizId(input.getBizId());
        result.setName(input.getName());
        result.setCategoryId(input.getCategoryId());
        result.setLogoImgUrl(input.getLogoImgUrl());
        result.setImgUrl(input.getImgUrl());
        return result;
    }
}
