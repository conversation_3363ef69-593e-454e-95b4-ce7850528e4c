package com.dl.dops.biz.common.util;

import java.net.MalformedURLException;
import java.net.URL;

public class UrlUtils {

    /**
     * 从URL字符串表示中提取路径部分
     *
     * @param urlStr URL字符串
     * @return 如果是合法URL，则返回路径部分，否则返回null
     */
    public static String getPathFromUrlString(String urlStr) {
        URL url = null;
        try {
            url = new URL(urlStr);
        } catch (MalformedURLException e) {
            return null;
        }
        return url.getPath();
    }
}
