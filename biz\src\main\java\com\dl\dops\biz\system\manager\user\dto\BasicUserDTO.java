package com.dl.dops.biz.system.manager.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class BasicUserDTO {

    private static final long serialVersionUID = -617960309925460466L;
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("token")
    private String token;

}
