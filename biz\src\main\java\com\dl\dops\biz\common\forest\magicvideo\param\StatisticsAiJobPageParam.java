package com.dl.dops.biz.common.forest.magicvideo.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-14 10:32
 */
@Data
@ApiModel("ai任务数据统计分页查询参数")
public class StatisticsAiJobPageParam extends AbstractPageParam {

    @ApiModelProperty("租户号")
    private String tenantCode;

    @ApiModelProperty("最小时间")
    private Date minDt;

    @ApiModelProperty("最大时间")
    private Date maxDt;

    @ApiModelProperty("ai任务类型，1-数字人，2-TTS")
    private Integer aiJobType;

}
