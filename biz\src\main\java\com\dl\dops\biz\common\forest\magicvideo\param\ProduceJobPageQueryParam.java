package com.dl.dops.biz.common.forest.magicvideo.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @describe: ProduceJobPageQueryParam
 * @author: zhousx
 * @date: 2023/6/19 18:48
 */
@Data
public class ProduceJobPageQueryParam extends AbstractPageParam {
    @ApiModelProperty("批次id")
    @NotBlank
    private String batchId;

    @ApiModelProperty("任务状态：-1-未开始 0-就绪 1-合成中 2-合成成功 3-合成失败 4-已取消")
    private List<Integer> statusList;
}
