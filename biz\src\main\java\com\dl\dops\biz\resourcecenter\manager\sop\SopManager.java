package com.dl.dops.biz.resourcecenter.manager.sop;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.resourcecenter.dal.sop.po.SopPO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopAddBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopModifyBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopSearchBO;

import java.util.List;
import java.util.Map;

public interface SopManager extends IService<SopPO>, CommonService {
    /**
     * 新增SOP
     *
     * @param bo
     * @return
     */
    Long add(SopAddBO bo);

    /**
     * 修改SOP
     *
     * @param bo
     */
    void modify(SopModifyBO bo);

    /**
     * 分页查询
     *
     * @param bo
     * @return
     */
    IPage<SopBO> pageQuery(SopSearchBO bo);

    /**
     * 根据sopId查询信息
     *
     * @param sopId
     * @return
     */
    SopBO selectBySopId(Long sopId);

    /**
     * 根据sopid查询sopMap
     *
     * @param sopIds
     * @return key-sopId
     */
    Map<Long, SopBO> getSopMapBySopIds(List<Long> sopIds);

    /**
     * 逻辑删除
     *
     * @param sopId
     */
    void logicDelete(Long sopId);

    /**
     * 模板发布或取消发布
     *
     * @param sopId
     */
    void tplPublishOrCancel(Long sopId);

}
