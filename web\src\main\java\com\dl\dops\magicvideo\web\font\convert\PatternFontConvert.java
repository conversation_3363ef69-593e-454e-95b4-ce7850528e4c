package com.dl.dops.magicvideo.web.font.convert;

import com.dl.dops.magicvideo.font.dto.PatternFontDTO;
import com.dl.dops.magicvideo.web.font.vo.PatternFontVO;

import java.util.Objects;

public class PatternFontConvert {

    public static PatternFontVO cnvPatternFontDTO2VO(PatternFontDTO input){
        if (Objects.isNull(input)){
            return null;
        }
        PatternFontVO result = new PatternFontVO();
        result.setFontType(input.getFontType());
        result.setName(input.getName());
        result.setCoverImg(input.getCoverImg());
        result.setBizId(input.getBizId());
        result.setStyles(input.getStyles());
        result.setCreateDt(input.getCreateDt());
        result.setUserName(input.getUserName());
        result.setUserId(input.getUserId()+"");
        return result;
    }

}
