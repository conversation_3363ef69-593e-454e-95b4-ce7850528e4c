package com.dl.dops.biz.resourcecenter.manager.pack.impl;

import cn.easyes.core.biz.OrderByParam;
import cn.easyes.core.biz.PageInfo;
import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.easyes.core.toolkit.EsWrappers;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.resourcecenter.dal.pack.PackMapper;
import com.dl.dops.biz.resourcecenter.es.pack.EsIndexRcPackMapper;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackAddBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackSearchBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackUpdateBO;
import com.dl.dops.biz.resourcecenter.dal.pack.param.PackQuery;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackPO;
import com.dl.dops.biz.resourcecenter.es.pack.po.EsIndexRcPack;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.resourcecenter.manager.pack.PackManager;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackBO;
import com.dl.dops.biz.resourcecenter.manager.pack.helper.PackHelper;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class PackManagerImpl extends ServiceImpl<PackMapper, PackPO> implements PackManager {
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private EsIndexRcPackMapper esIndexRcPackMapper;
    @Resource
    private OperatorUtil operatorUtil;

    @Override
    public Long add(PackAddBO bo) {
        Long packId = Objects.isNull(bo.getPackId()) ? hostTimeIdg.generateId().longValue() : bo.getPackId();
        PackPO po = PackHelper.cnvPackAddBO2PO(bo, packId);
        po.setCreateBy(operatorUtil.getOperator());
        po.setModifyBy(operatorUtil.getOperator());
        po.setCreatorName(operatorUtil.getOperatorName());
        po.setModifyName(operatorUtil.getOperatorName());
        this.save(po);
        return packId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(PackUpdateBO bo) {
        Assert.isTrue(Objects.nonNull(bo.getPackId()), "服务包id不能为空");

        PackPO exist = this.getOne(Wrappers.lambdaQuery(PackPO.class).eq(PackPO::getPackId, bo.getPackId()));
        Assert.notNull(exist, "服务包不存在");

        // 更新服务包主体表
        LambdaUpdateWrapper<PackPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(PackPO::getPackId, bo.getPackId())
                .set(StringUtils.isNotBlank(bo.getTitle()), PackPO::getTitle, bo.getTitle())
                .set(StringUtils.isNotBlank(bo.getSceneOverview()), PackPO::getSceneOverview, bo.getSceneOverview())
                .set(StringUtils.isNotBlank(bo.getSuggest()), PackPO::getSuggest, bo.getSuggest())
                .set(StringUtils.isNotBlank(bo.getDetailedDescription()), PackPO::getDetailedDescription,
                        bo.getDetailedDescription())
                .set(Objects.nonNull(bo.getDomain()), PackPO::getDomain, bo.getDomain())
                .set(Objects.nonNull(bo.getCategory1()), PackPO::getCategory1, bo.getCategory1())
                .set(Objects.nonNull(bo.getCategory2()), PackPO::getCategory2, bo.getCategory2())
                .set(PackPO::getModifyDt, new Date()).set(PackPO::getModifyBy, operatorUtil.getOperator())
                .set(PackPO::getModifyName, operatorUtil.getOperatorName());
        this.update(updateWrapper);

        return Boolean.TRUE;
    }

    @Override
    public PackBO info(Long packId) {
        Assert.isTrue(Objects.nonNull(packId), "服务包id不能为空");
        PackPO pack = this.getOne(Wrappers.lambdaQuery(PackPO.class).eq(PackPO::getPackId, packId));

        return PackHelper.cnvPackPO2DTO(pack);
    }

    @Override
    public IPage<PackBO> pageQuery(PackSearchBO bo) {
        PackQuery queryPO = new PackQuery();
        if (Objects.nonNull(bo.getCreatorUserId())) {
            queryPO.setCreateBy(bo.getCreatorUserId());
        }
        queryPO.setTitle(bo.getTitle());
        queryPO.setDomain(bo.getDomain());
        queryPO.setStatus(bo.getStatus());
        queryPO.setCategory1(bo.getCategory1());
        queryPO.setCategory2(bo.getCategory2());
        queryPO.setScene(bo.getScene());
        queryPO.setPageIndex(bo.getPageIndex());
        queryPO.setPageSize(bo.getPageSize());
        Integer count = baseMapper.count(queryPO);
        IPage<PackBO> res = new Page<>(bo.getPageIndex(), bo.getPageSize(), count);
        if (count == 0) {
            return res;
        }
        List<PackPO> list = baseMapper.pageQuery(queryPO);
        if (CollectionUtils.isEmpty(list)) {
            return res;
        }

        res.setRecords(list.stream().map(PackHelper::cnvPackPO2DTO).collect(Collectors.toList()));
        return res;
    }

    @Override
    public PageInfo<EsIndexRcPack> pageFromEs(PackSearchBO bo) {
        List<OrderByParam> orderByParamList = new ArrayList<>();
        OrderByParam orderByParam1 = new OrderByParam();
        orderByParam1.setOrder("create_dt");
        orderByParam1.setSort("DESC");
        orderByParamList.add(orderByParam1);

        LambdaEsQueryWrapper<EsIndexRcPack> wrapper = EsWrappers.lambdaQuery(EsIndexRcPack.class);
        wrapper.eq(Objects.nonNull(bo.getScene()), EsIndexRcPack::getScene, bo.getScene())
                .like(StringUtil.isNotBlank(bo.getTitle()), "title.keyword", bo.getTitle())
                .eq(Objects.nonNull(bo.getDomain()), "domain", bo.getDomain())
                .eq(Objects.nonNull(bo.getCategory1()), "category1", bo.getCategory1())
                .eq(Objects.nonNull(bo.getCategory2()), "category2", bo.getCategory2())
                .eq(Objects.nonNull(bo.getStatus()), "status", bo.getStatus()).eq("is_deleted", Const.ZERO)
                .orderBy(orderByParamList);

        return esIndexRcPackMapper.pageQuery(wrapper, bo.getPageIndex(), bo.getPageSize());
    }

}
