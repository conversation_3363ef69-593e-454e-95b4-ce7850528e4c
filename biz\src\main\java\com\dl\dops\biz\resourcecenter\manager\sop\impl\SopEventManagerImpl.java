package com.dl.dops.biz.resourcecenter.manager.sop.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.resourcecenter.dal.sop.SopEventMapper;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventAddBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventModifyBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventSaveBO;
import com.dl.dops.biz.resourcecenter.dal.sop.po.SopEventFullPO;
import com.dl.dops.biz.resourcecenter.dal.sop.po.SopEventPO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.enums.SymbolE;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.resourcecenter.manager.sop.SopEventCntManager;
import com.dl.dops.biz.resourcecenter.manager.sop.SopEventManager;
import com.dl.dops.resourcecenter.sop.enums.RcSopEventRuleTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SopEventManagerImpl extends ServiceImpl<SopEventMapper, SopEventPO> implements SopEventManager {

    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private OperatorUtil operatorUtil;
    @Resource
    private SopEventCntManager sopEventCntManager;
    
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void batchSave(Long sopId, List<SopEventSaveBO> saveBOList) {
        Assert.isTrue(Objects.nonNull(sopId), "sopId不能为空");

        List<SopEventPO> exisitEventPOList = baseMapper.selectList(
                Wrappers.lambdaQuery(SopEventPO.class).eq(SopEventPO::getSopId, sopId)
                        .eq(SopEventPO::getIsDeleted, Const.ZERO));

        if (CollectionUtils.isEmpty(exisitEventPOList)) {
            List<SopEventPO> insertList = saveBOList.stream().map(bo -> {
                Long eventId = hostTimeIdg.generateId().longValue();
                //回填事件id
                bo.setEventId(eventId);

                SopEventPO po = new SopEventPO();
                po.setEventId(eventId);
                po.setSopId(bo.getSopId());
                po.setName(bo.getName());
                po.setReachType(bo.getReachType());
                po.setRuleType(bo.getRuleType());
                po.setRuleContent(bo.getRuleContent());
                po.setRemark(bo.getRemark());
                if (StringUtils.isBlank(bo.getContent())) {
                    po.setContent(SymbolE.BLANK.getValue());
                } else {
                    po.setContent(bo.getContent());
                }
                po.setValidityPeriod(bo.getValidityPeriod());
                return po;
            }).collect(Collectors.toList());
            //批量新增事件
            baseMapper.batchInsert(insertList);

            //批量新增事件内容
            sopEventCntManager.batchSave(saveBOList);
            return;
        }

        //需要更新的事件,即有事件id的
        List<SopEventSaveBO> needToUpdateList = saveBOList.stream().filter(s -> Objects.nonNull(s.getEventId()))
                .collect(Collectors.toList());
        //需要新增的事件，即无事件id的
        List<SopEventSaveBO> needToAddList = saveBOList.stream().filter(s -> Objects.isNull(s.getEventId()))
                .collect(Collectors.toList());

        //需要逻辑删除的id列表
        List<Long> needToDeleteIdList = calNeedToDeleteIdList(saveBOList, exisitEventPOList);

        //批量新增
        if (CollectionUtils.isNotEmpty(needToAddList)) {
            baseMapper.batchInsert(this.buildAddSopEventList(sopId, needToAddList));
        }
        //批量修改
        if (CollectionUtils.isNotEmpty(needToUpdateList)) {
            baseMapper.batchUpdate(this.buildUptSopEventList(needToUpdateList));
        }
        //批量逻辑删除
        if (CollectionUtils.isNotEmpty(needToDeleteIdList)) {
            baseMapper.batchLogicDeleteByIds(needToDeleteIdList, operatorUtil.getOperator());
        }

        //批量新增事件内容
        sopEventCntManager.batchSave(saveBOList);
    }

    @Override
    public Long add(SopEventAddBO bo) {
        Assert.isTrue(Objects.nonNull(bo), "新增事件不能为空");
        Assert.isTrue(Objects.nonNull(bo.getSopId()), "sopId不能为空");
        Assert.isTrue(Objects.nonNull(bo.getReachType()), "触达形式不能为空");
        Assert.isTrue(Objects.nonNull(RcSopEventRuleTypeEnum.parse(bo.getRuleType())), "时间规则类型有误");

        Long eventId = hostTimeIdg.generateId().longValue();
        SopEventPO po = new SopEventPO();
        po.setEventId(eventId);
        po.setSopId(bo.getSopId());
        po.setName(bo.getName());
        po.setReachType(bo.getReachType());
        po.setRuleType(bo.getRuleType());
        po.setRuleContent(bo.getRuleContent());
        po.setRemark(bo.getRemark());
        po.setContent(bo.getContent());
        po.setValidityPeriod(bo.getValidityPeriod());
        baseMapper.insert(po);

        return eventId;
    }

    @Override
    public void modify(SopEventModifyBO bo) {
        SopEventPO po = new SopEventPO();
        po.setEventId(bo.getEventId());

        if (StringUtils.isNotBlank(bo.getName())) {
            po.setName(bo.getName());
        }
        if (Objects.nonNull(bo.getReachType())) {
            po.setReachType(bo.getReachType());
        }
        if (Objects.nonNull(bo.getRuleType())) {
            po.setRuleType(bo.getRuleType());
        }
        if (StringUtils.isNotBlank(bo.getRuleContent())) {
            po.setRuleContent(bo.getRuleContent());
        }
        if (StringUtils.isNotBlank(bo.getContent())) {
            po.setContent(bo.getContent());
        }
        if (StringUtils.isNotBlank(bo.getRemark())) {
            po.setRemark(bo.getRemark());
        }
        if (Objects.nonNull(bo.getValidityPeriod())) {
            po.setValidityPeriod(bo.getValidityPeriod());
        }

        baseMapper.update(po, Wrappers.lambdaUpdate(SopEventPO.class).eq(SopEventPO::getEventId, bo.getEventId()));
    }

    @Override
    public SopEventBO selectByEventId(Long eventId) {
        Assert.isTrue(Objects.nonNull(eventId), "eventId不能为空");
        SopEventPO sopEventPO = baseMapper.selectOne(
                Wrappers.lambdaQuery(SopEventPO.class).eq(SopEventPO::getEventId, eventId)
                        .eq(SopEventPO::getIsDeleted, 0));
        return cnvSopEventPO2DTO(sopEventPO);
    }

    @Override
    public List<SopEventBO> listBySopId(Long sopId) {
        Assert.isTrue(Objects.nonNull(sopId), "sopId不能为空");
        List<SopEventPO> list = baseMapper.selectList(
                Wrappers.lambdaQuery(SopEventPO.class).eq(SopEventPO::getSopId, sopId).eq(SopEventPO::getIsDeleted, 0));
        return list.stream().map(SopEventManagerImpl::cnvSopEventPO2DTO).collect(Collectors.toList());
    }

    @Override
    public void logicDeleteBySopId(Long sopId) {
        Assert.isTrue(Objects.nonNull(sopId), "sopId不能为空");
        SopEventPO updatePO = new SopEventPO();
        updatePO.setIsDeleted(1);
        baseMapper.update(updatePO, Wrappers.lambdaUpdate(SopEventPO.class).eq(SopEventPO::getSopId, sopId));
    }

    @Override
    public void logicDelete(Long eventId) {
        Assert.isTrue(Objects.nonNull(eventId), "eventId不能为空");
        SopEventPO updatePO = new SopEventPO();
        updatePO.setIsDeleted(1);
        baseMapper.update(updatePO, Wrappers.lambdaUpdate(SopEventPO.class).eq(SopEventPO::getEventId, eventId));
    }

    private SopEventBO cnvFullPO2DTO(SopEventFullPO po) {
        if (Objects.isNull(po)) {
            return null;
        }
        SopEventBO dto = new SopEventBO();
        dto.setEventId(po.getEventId());
        dto.setName(po.getName());
        dto.setContent(po.getContent());
        dto.setSopId(po.getSopId());
        dto.setReachType(po.getReachType());
        dto.setRuleContent(po.getRuleContent());
        dto.setRuleType(po.getRuleType());
        dto.setBeginDate(po.getBeginDate());
        dto.setName(po.getName());
        return dto;
    }

    private static SopEventBO cnvSopEventPO2DTO(SopEventPO input) {
        if (Objects.isNull(input)) {
            return null;
        }
        SopEventBO result = new SopEventBO();
        result.setEventId(input.getEventId());
        result.setSopId(input.getSopId());
        result.setContent(input.getContent());
        result.setReachType(input.getReachType());
        result.setRuleContent(input.getRuleContent());
        result.setRuleType(input.getRuleType());
        result.setName(input.getName());
        result.setRemark(input.getRemark());
        result.setValidityPeriod(input.getValidityPeriod());
        return result;
    }

    /**
     * 计算需要逻辑删除的id列表
     *
     * @param list
     * @param exisitEventPOList
     * @return
     */
    private static List<Long> calNeedToDeleteIdList(List<SopEventSaveBO> list, List<SopEventPO> exisitEventPOList) {
        Set<Long> saveIdSet = list.stream().map(SopEventSaveBO::getEventId).collect(Collectors.toSet());
        List<Long> needToDeleteIdList = exisitEventPOList.stream().map(po -> {
            if (!saveIdSet.contains(po.getEventId())) {
                return po.getId();
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return needToDeleteIdList;
    }

    private List<SopEventPO> buildAddSopEventList(Long sopId, List<SopEventSaveBO> saveBOList) {
        List<SopEventPO> sopEventPOS = saveBOList.stream().map(saveBO -> {
            Long eventId = hostTimeIdg.generateId().longValue();
            //回填事件id
            saveBO.setEventId(eventId);

            SopEventPO po = new SopEventPO();
            po.setSopId(sopId);
            po.setEventId(eventId);
            po.setName(saveBO.getName());
            po.setRemark(saveBO.getRemark());
            po.setContent(saveBO.getContent());
            po.setValidityPeriod(saveBO.getValidityPeriod());
            po.setRuleType(saveBO.getRuleType());
            po.setReachType(saveBO.getReachType());
            po.setRuleContent(saveBO.getRuleContent());
            return po;
        }).collect(Collectors.toList());
        return sopEventPOS;
    }

    private List<SopEventPO> buildUptSopEventList(List<SopEventSaveBO> saveBOList) {
        List<SopEventPO> sopEventPOS = saveBOList.stream().map(saveBO -> {
            SopEventPO po = new SopEventPO();
            po.setEventId(saveBO.getEventId());
            po.setName(saveBO.getName());
            po.setRemark(saveBO.getRemark());
            po.setValidityPeriod(saveBO.getValidityPeriod());
            po.setContent(saveBO.getContent());
            po.setRuleType(saveBO.getRuleType());
            po.setReachType(saveBO.getReachType());
            po.setRuleContent(saveBO.getRuleContent());
            return po;
        }).collect(Collectors.toList());
        return sopEventPOS;
    }

}
