package com.dl.dops.system.web.voicetrain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-07 11:35
 */
@Data
public class AudioTrainParam {

    /**
     * 录音人名称，如“韩梅梅”
     */
    @ApiModelProperty(value = "录音人名称")
    private String speaker;

    @ApiModelProperty(value = "第三方训练人模型编号", required = true)
    private String extModelCode;

    @ApiModelProperty(value = "厂商", required = true)
    private Integer channel;

    @NotBlank(message = "录音音频链接不能为空")
    @ApiModelProperty(value = "录音音频链接", required = true)
    private String link;

}
