package com.dl.dops.resourcecenter.category.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.Max;
import java.io.Serializable;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RcCategoryParam implements Serializable {

    private static final long serialVersionUID = -7917960211360672987L;
    @ApiModelProperty("内容分类ID")
    String id;

    @ApiModelProperty("内容分类名称")
    String name;

    @ApiModelProperty("分类：0 默认分类")
    Integer categoryType;

    @Max(value = 1, message = "请选择正确的分类级别")
    @ApiModelProperty("内容分类级别： 0 一级（默认）； 1 二级")
    Integer categoryLevel;

    @ApiModelProperty("内容分类父级ID")
    String parentId;
}
