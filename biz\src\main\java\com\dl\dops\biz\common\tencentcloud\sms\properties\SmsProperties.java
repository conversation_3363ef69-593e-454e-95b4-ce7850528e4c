package com.dl.dops.biz.common.tencentcloud.sms.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 腾讯云授权信息配置
 */
@ConfigurationProperties(prefix = "dl.tencentcloud.sms")
@Configuration
@Data
public class SmsProperties {
    //短信 SdkAppId
    private String smsSdkAppId;

    //短信地域列表
    // 华北地区(北京)	ap-beijing
    //华南地区(广州)	ap-guangzhou
    //华东地区(南京)	ap-nanjing
    private String smsRegion;

}
