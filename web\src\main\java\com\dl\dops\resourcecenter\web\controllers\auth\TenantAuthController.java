package com.dl.dops.resourcecenter.web.controllers.auth;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dl.dops.biz.common.annotation.NotLogin;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.enums.CommonCode;
import com.dl.dops.web.common.util.EnvUtil;
import com.dl.dops.web.common.util.HttpUtil;
import com.dl.dops.web.common.util.RequestUtils;
import com.dl.dops.resourcecenter.common.RcConst;
import com.dl.dops.resourcecenter.tenantauth.dto.RcTenantOperateAuthDTO;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 这块代码还不能删掉，因为本地老版本还会调用
 * 待本地老版本都更新后，这块代码就可以删了。————2023.3.4
 *
 *
 * 因为B端资源中心页面尚未改造，还是走的老页面老逻辑。
 * 而dops后台只做了通用素材的页面，服务包、sop的都还没做，也没有资源投入了（因为没有直接经济效益吧）
 * 所以dops里做了兼容，允许B端DL租户操作资源中心。————2023.7.26
 *
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-28 09:36
 */
@Api("资源中心-租户权限")
@RestController
//@Deprecated
@RequestMapping("/resource/tenantauth")
public class TenantAuthController {
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantAuthController.class);

    @Autowired
    private HttpServletRequest request;

    @Resource
    private EnvUtil envUtil;

    private static final String PROD_CLOUD_HOST = "wealth.dinglitec.com";

    private static final String TEST_CLOUD_HOST = "test.dinglitec.com";

    @NotLogin
    @PostMapping("/operateauth")
    public ResultModel<RcTenantOperateAuthDTO> operateAuth() {
        String requestURL = RequestUtils.getReferer(request);
        String host = RequestUtils.getHost(requestURL);
        if (StringUtils.isBlank(host)) {
            LOGGER.warn("当前请求URL的域名为空:requestURL:{}", requestURL);
            return ResultModel.error(CommonCode.INVALID_AUTH.getCode(), "当前请求URL的域名为空");
        }
        String tenantCode = HttpUtil.getHeader(request, RcConst.TENANT_CODE_NAME);
        if (StringUtils.isBlank(tenantCode)) {
            return ResultModel.error(CommonCode.INVALID_AUTH.getCode(), "租户为空");
        }
        RcTenantOperateAuthDTO authDTO = new RcTenantOperateAuthDTO();
        authDTO.setCanEdit(Const.ZERO);
        if (!RcConst.DL.equals(tenantCode)) {
            return ResultModel.success(authDTO);
        }
        if (envUtil.isProd() && host.contains(PROD_CLOUD_HOST)) {
            authDTO.setCanEdit(Const.ONE);
            return ResultModel.success(authDTO);
        }
        if (envUtil.isTest() && host.contains(TEST_CLOUD_HOST)) {
            authDTO.setCanEdit(Const.ONE);
            return ResultModel.success(authDTO);
        }
        if (envUtil.isCdev() || envUtil.isDev()) {
            authDTO.setCanEdit(Const.ONE);
            return ResultModel.success(authDTO);
        }
        return ResultModel.success(authDTO);
    }

}
