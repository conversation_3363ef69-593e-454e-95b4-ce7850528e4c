package com.dl.dops.biz.common.manager.storepolicy.impl;

import com.dl.dops.biz.common.tencentcloud.ApiManager;
import com.dl.dops.biz.common.tencentcloud.cos.CosFileUploadManager;
import com.dl.dops.biz.common.tencentcloud.cos.CosTempCredentialDTO;
import com.dl.dops.biz.common.tencentcloud.cos.GetTempCredentialBO;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.common.manager.storepolicy.StorePolicyManager;
import com.dl.dops.biz.common.manager.storepolicy.dto.BaseTempCredentialDTO;
import com.dl.dops.biz.common.manager.storepolicy.dto.CosResultDTO;
import com.dl.dops.biz.common.manager.storepolicy.enums.StorePolicyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;

/**
 * @ClassName StorePolicyManagerImpl
 * @Description 存储策略类，根据storePolicy确定使用腾讯云存储还是其他存储
 * <AUTHOR>
 * @Date 2022/10/10 9:16
 * @Version 1.0
 **/
@Slf4j
@Service
public class StorePolicyManagerImpl implements StorePolicyManager {

    @Value("${dl.store.policy:}")
    private String storePolicy;
    @Value("${dl.minio.assignTenantCode:}")
    private String assignTenantCode;
    @Resource
    private OperatorUtil operatorUtil;
    @Resource
    private CosFileUploadManager cosFileUploadManager;
    @Resource
    private ApiManager apiManager;

    @Override
    public String uploadFile(File file, String tenantCode, String type, String path) {
        return cosFileUploadManager.uploadFile(file, tenantCode, type, path);
    }

    @Override
    public BaseTempCredentialDTO getTempCredential(GetTempCredentialBO param) {
        CosResultDTO cos = convert(apiManager.getCosTempCredential(param));
        cos.setStorage(this.getStorePolicy());
        return cos;
    }

    private CosResultDTO convert(CosTempCredentialDTO source) {
        CosResultDTO cosResultDTO = new CosResultDTO();
        cosResultDTO.setTmpSecretId(source.getTmpSecretId());
        cosResultDTO.setTmpSecretKey(source.getTmpSecretKey());
        cosResultDTO.setSessionToken(source.getSessionToken());
        cosResultDTO.setStartTime(source.getStartTime());
        cosResultDTO.setExpiredTime(source.getExpiredTime());
        cosResultDTO.setBucketId(source.getBucketId());
        cosResultDTO.setRegion(source.getRegion());
        return cosResultDTO;
    }

    @Override
    public String getStorePolicy() {
        return StorePolicyEnum.DEFAULT.getCode();
    }

    private boolean isDefaultStorage() {
        return StringUtils.equals(getStorePolicy(), StorePolicyEnum.DEFAULT.getCode());
    }
}
