package com.dl.dops.resourcecenter.pack.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 20:44
 */
@Data
@ApiModel("服务包元素")
public class RcPackElementDTO implements Serializable {

    private static final long serialVersionUID = -56606378755722394L;
    @ApiModelProperty("元素id")
    private String elementId;

    @ApiModelProperty("链路id")
    private String chainPathId;

    @ApiModelProperty("分支id")
    private String branchId;

    /**
     * 元素类型
     *
     * @see：PackElementTypeEnum
     */
    @ApiModelProperty("元素类型")
    private Integer type;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private int sort;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 文字内容
     */
    @ApiModelProperty("文字内容")
    private String content;

    @ApiModelProperty("附件列表")
    private List<RcPackElementAttachmentDTO> attachmentList;

    /**
     * 扩展数据
     * 存储json数据
     * 当type是10时，存储：{"productList":[{"productId":"111","sort":1},{"productId":"222","sort":2},{"productId":"333","sort":3}]}
     */
    @ApiModelProperty("扩展数据")
    private String extData;

    @ApiModelProperty("摘要")
    private String remark;
}
