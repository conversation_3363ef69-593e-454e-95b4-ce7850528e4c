package com.dl.dops.biz.resourcecenter.manager.sop;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.resourcecenter.dal.sop.po.SopEventPO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventAddBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventModifyBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventSaveBO;

import java.util.List;

public interface SopEventManager extends IService<SopEventPO>, CommonService {

    void batchSave(Long sopId, List<SopEventSaveBO> saveBOList);

    Long add(SopEventAddBO bo);

    void modify(SopEventModifyBO bo);

    SopEventBO selectByEventId(Long eventId);

    List<SopEventBO> listBySopId(Long sopId);

    void logicDeleteBySopId(Long sopId);

    void logicDelete(Long eventId);
}
