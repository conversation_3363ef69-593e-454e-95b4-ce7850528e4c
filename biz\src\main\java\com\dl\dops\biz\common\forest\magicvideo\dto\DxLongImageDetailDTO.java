package com.dl.dops.biz.common.forest.magicvideo.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description：TODO
 * @author： Pelot
 * @create： 2024/11/7 14:27
 */
@Data
public class DxLongImageDetailDTO {

    /**
     * 业务id
     */
    private String bizId;
    /**
     * 项目名称
     */
    private String name;
    /**
     * 项目标题
     */
    private String title;
    /**
     * 项目描述
     */
    private String description;
    /**
     * 短连接
     */
    private String shortUrl;

    /**
     * 访问url
     */
    private String url;
    /**
     * 创建时间
     */
    private Date createDt;
    /**
     * 修改时间
     */
    private Date modifyDt;

    /**
     * 背景图片列表
     */
    private List<String> bgmUrlList;

    /**
     * 选区信息
     */
    private List<ElectoralDistrictDTO> electoralDistrictList;
}
