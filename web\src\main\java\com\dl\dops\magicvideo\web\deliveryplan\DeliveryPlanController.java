package com.dl.dops.magicvideo.web.deliveryplan;

import cn.easyes.common.utils.CollectionUtils;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.magicvideo.manager.MagicVideoManager;
import com.dl.dops.magicvideo.deliveryplan.dto.DeliveryPlanDTO;
import com.dl.dops.magicvideo.deliveryplan.dto.VisualTemplateInternalDTO;
import com.dl.dops.magicvideo.deliveryplan.param.AddDeliveryPlanBO;
import com.dl.dops.magicvideo.web.deliveryplan.param.AddDeliveryPlanParam;
import com.dl.dops.magicvideo.deliveryplan.param.DeliveryPlanPageQueryBO;
import com.dl.dops.magicvideo.deliveryplan.param.TemplateInternalPageQueryBO;
import com.dl.dops.magicvideo.deliveryplan.param.UpdateDeliveryPlanBO;
import com.dl.dops.magicvideo.web.deliveryplan.param.DeliveryPlanPageQueryParam;
import com.dl.dops.magicvideo.web.deliveryplan.param.TemplateInternalPageQueryParam;
import com.dl.dops.magicvideo.web.deliveryplan.param.UpdateDeliveryPlanParam;
import com.dl.dops.magicvideo.web.deliveryplan.vo.DeliveryPlanVO;
import com.dl.dops.magicvideo.web.deliveryplan.vo.VisualTemplateInternalVO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.stream.Collectors;

/**
 * @describe: 交付计划
 * @author: zhousx
 * @date: 2023/9/6 9:34
 */
@Slf4j
@RestController
@RequestMapping("/dops/magicvideo/deliveryplan")
@Api("交付计划")
public class DeliveryPlanController {
    @Autowired
    private MagicVideoManager magicVideoManager;
    @Autowired
    private OperatorUtil operatorUtil;

    @PostMapping("/add")
    @ApiOperation("创建交付计划")
    public ResultModel<Void> addDeliveryPlan(@RequestBody @Validated AddDeliveryPlanParam param) {
        AddDeliveryPlanBO addDeliveryPlanBO = new AddDeliveryPlanBO();
        addDeliveryPlanBO.setName(param.getName());
        addDeliveryPlanBO.setDesc(param.getDesc());
        addDeliveryPlanBO.setTenantCode(param.getTenantCode());
        addDeliveryPlanBO.setTenantName(param.getTenantName());
        addDeliveryPlanBO.setTemplateId(Long.valueOf(param.getTemplateId()));
        addDeliveryPlanBO.setDirector(param.getDirector());
        addDeliveryPlanBO.setMobile(param.getMobile());
        addDeliveryPlanBO.setLimit(param.getLimit());
        addDeliveryPlanBO.setPeriod(param.getPeriod());
        addDeliveryPlanBO.setIsNotify(param.getIsNotify());
        addDeliveryPlanBO.setNotifyUrl(param.getNotifyUrl());
        addDeliveryPlanBO.setCallbackUrl(param.getCallbackUrl());
        addDeliveryPlanBO.setProduceWay(param.getProduceWay());
        addDeliveryPlanBO.setCreateBy(operatorUtil.getOperator());
        addDeliveryPlanBO.setHasPeriod(param.getHasPeriod());
        magicVideoManager.addDeliveryPlan(addDeliveryPlanBO);
        return ResultModel.success(null);
    }

    @PostMapping("/update")
    @ApiOperation("更新交付计划")
    public ResultModel<Void> updateDeliveryPlan(@RequestBody @Validated UpdateDeliveryPlanParam param) {
        UpdateDeliveryPlanBO updateDeliveryPlanBO = new UpdateDeliveryPlanBO();
        updateDeliveryPlanBO.setPlanId(param.getPlanId());
        updateDeliveryPlanBO.setName(param.getName());
        updateDeliveryPlanBO.setDesc(param.getDesc());
        updateDeliveryPlanBO.setDirector(param.getDirector());
        updateDeliveryPlanBO.setMobile(param.getMobile());
        updateDeliveryPlanBO.setLimit(param.getLimit());
        updateDeliveryPlanBO.setPeriod(param.getPeriod());
        updateDeliveryPlanBO.setStatus(param.getStatus());
        updateDeliveryPlanBO.setIsNotify(param.getIsNotify());
        updateDeliveryPlanBO.setNotifyUrl(param.getNotifyUrl());
        updateDeliveryPlanBO.setCallbackUrl(param.getCallbackUrl());
        updateDeliveryPlanBO.setProduceWay(param.getProduceWay());
        updateDeliveryPlanBO.setHasPeriod(param.getHasPeriod());
        magicVideoManager.updateDeliveryPlan(updateDeliveryPlanBO);
        return ResultModel.success(null);
    }

    @PostMapping("/list")
    @ApiOperation("查询交付计划列表")
    public ResultPageModel<DeliveryPlanVO> list(@RequestBody @Validated DeliveryPlanPageQueryParam param) {
        DeliveryPlanPageQueryBO bo = new DeliveryPlanPageQueryBO();
        bo.setName(param.getName());
        bo.setTenantName(param.getTenantName());
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        ResultPageModel<DeliveryPlanDTO> resultDTO = magicVideoManager.listDeliveryPlan(bo);
        if(CollectionUtils.isEmpty(resultDTO.getDataResult())) {
            return new ResultPageModel<>();
        }
        ResultPageModel<DeliveryPlanVO> resultVO = new ResultPageModel<>();
        resultVO.setTotal(resultDTO.getTotal());
        resultVO.setPageSize(resultDTO.getPageSize());
        resultVO.setPageIndex(resultDTO.getPageIndex());
        resultVO.setTotalPage(resultDTO.getTotalPage());
        resultVO.setDataResult(resultDTO.getDataResult().stream().map(dto -> {
            DeliveryPlanVO deliveryPlanVO = new DeliveryPlanVO();
            deliveryPlanVO.setPlanId(dto.getPlanId() + "");
            deliveryPlanVO.setName(dto.getName());
            deliveryPlanVO.setDesc(dto.getDesc());
            deliveryPlanVO.setTenantCode(dto.getTenantCode());
            deliveryPlanVO.setTenantName(dto.getTenantName());
            deliveryPlanVO.setTemplateId(dto.getTemplateId() + "");
            deliveryPlanVO.setTemplateName(dto.getTemplateName());
            deliveryPlanVO.setTemplateCoverUrl(dto.getTemplateCoverUrl());
            deliveryPlanVO.setDirector(dto.getDirector());
            deliveryPlanVO.setMobile(dto.getMobile());
            deliveryPlanVO.setLimit(dto.getLimit());
            deliveryPlanVO.setPeriod(dto.getPeriod());
            deliveryPlanVO.setCreateDt(dto.getCreateDt());
            deliveryPlanVO.setStatus(dto.getStatus());
            deliveryPlanVO.setProduceWay(dto.getProduceWay());
            deliveryPlanVO.setNotifyUrl(dto.getNotifyUrl());
            deliveryPlanVO.setIsNotify(dto.getIsNotify());
            deliveryPlanVO.setCallbackUrl(dto.getCallbackUrl());
            deliveryPlanVO.setHasPeriod(dto.getHasPeriod());
            return deliveryPlanVO;
        }).collect(Collectors.toList()));
        return resultVO;
    }

    @PostMapping("/templatelist")
    @ApiOperation("查询租户模板列表")
    public ResultPageModel<VisualTemplateInternalVO> templateList(@RequestBody @Validated TemplateInternalPageQueryParam param) {
        TemplateInternalPageQueryBO bo = new TemplateInternalPageQueryBO();
        bo.setTenantCode(param.getTenantCode());
        bo.setName(param.getName());
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        ResultPageModel<VisualTemplateInternalDTO> resultDTO = magicVideoManager.templateList(bo);
        if(CollectionUtils.isEmpty(resultDTO.getDataResult())) {
            return new ResultPageModel<>();
        }
        ResultPageModel<VisualTemplateInternalVO> resultVO = new ResultPageModel<>();
        resultVO.setTotal(resultDTO.getTotal());
        resultVO.setPageSize(resultDTO.getPageSize());
        resultVO.setPageIndex(resultDTO.getPageIndex());
        resultVO.setTotalPage(resultDTO.getTotalPage());
        resultVO.setDataResult(resultDTO.getDataResult().stream().map(dto -> {
            VisualTemplateInternalVO vo = new VisualTemplateInternalVO();
            vo.setName(dto.getName());
            vo.setTemplateId(dto.getTemplateId() + "");
            return vo;
        }).collect(Collectors.toList()));
        return resultVO;
    }
}
