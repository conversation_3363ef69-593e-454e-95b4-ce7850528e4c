package com.dl.dops.system.web.virtualvoice.vo;

import com.dl.dops.system.web.virtualman.vo.DaVirtualVoiceLinkVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 数字资产-数字声音信息表
 *
 * @TableName da_virtual_voice
 */
@Data
public class DaVirtualVoiceVO implements Serializable {

    private static final long serialVersionUID = -2838101388899876437L;
    @ApiModelProperty(value = "数字声音唯一标识")
    private String bizId;

    /**
     * 租户代码
     */
    @ApiModelProperty(value = "租户代码")
    private String tenantCode;

    /**
     * 渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云
     */
    @ApiModelProperty(value = "渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云")
    private Integer channel;

    /**
     * 外部厂商声音唯一标识
     */
    @ApiModelProperty(value = "外部厂商声音唯一标识")
    private String voiceKey;

    /**
     * 声音名称
     */
    @ApiModelProperty(value = "声音名称")
    private String voiceName;

    /**
     * 性别：1 男 ；2 女
     */
    @ApiModelProperty(value = "性别：1 男 ；2 女")
    private Integer gender;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String voiceDesc;

    /**
     * 1 克隆音；2 合成音
     */
    @ApiModelProperty(value = "1 克隆音；2 合成音")
    private Integer voiceType;

    /**
     * 默认：通用
     */
    @ApiModelProperty(value = "默认：通用")
    private String voiceCategory;

    /**
     * 试听链接
     */
    @ApiModelProperty(value = "试听链接")
    private String sampleLink;

    /**
     * 生效日期
     */
    @ApiModelProperty(value = "生效日期")
    private Date effectDt;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    private Date expiryDt;

    /**
     * 最大声音试听链接
     */
    @ApiModelProperty(value = "最大声音试听链接")
    private String maxVoiceLink;

    /**
     * 建议音量
     */
    @ApiModelProperty(value = "建议音量")
    private String volume;

    /**
     * 建议语速
     */
    @ApiModelProperty(value = "建议语速")
    private String speed;

    /**
     * 语音头像
     */
    @ApiModelProperty(value = "语音头像")
    private String headImg;

    /**
     * 是否启用 0：否，1：是
     */
    @ApiModelProperty(value = "是否启用 0：否，1：是")
    private Integer isEnabled;

    /**
     * 授权集合
     */
    @ApiModelProperty(value = "授权集合")
    private List<String> tenantCodeList;

    /**
     * 音频时长(单位：毫秒)
     */
    @ApiModelProperty(value = "音频时长(单位：毫秒)")
    private Long duration;

    /**
     * 多试听链接
     */
    @ApiModelProperty(value = "多试听链接")
    private List<DaVirtualVoiceLinkVO> voiceLinks;
}