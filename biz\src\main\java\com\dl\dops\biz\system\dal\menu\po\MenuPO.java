package com.dl.dops.biz.system.dal.menu.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-02-28 17:42
 */
@Data
@TableName("sys_menu")
public class MenuPO extends BasePO {
    private static final long serialVersionUID = -8535086555045261655L;

    @TableId
    private Long id;

    /**
     * 菜单id
     */
    @TableField("menu_id")
    private Long menuId;

    /**
     * 上级菜单,0表示一级菜单
     */
    @TableField("parent_id")
    private Long parentId;
    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 菜单地址(静态url)
     */
    @TableField("url")
    private String url;

    /**
     * 菜单级别 1：一级 2：二级 3：三级
     */
    @TableField("menu_level")
    private Integer menuLevel;

    /**
     * 显示顺序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 是否禁用【0-开启1-禁用】
     */
    @TableField("disable")
    private Integer disable;
}
