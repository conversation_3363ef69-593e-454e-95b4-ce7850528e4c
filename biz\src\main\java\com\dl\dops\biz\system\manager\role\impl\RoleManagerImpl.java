package com.dl.dops.biz.system.manager.role.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.system.dal.role.RoleMapper;
import com.dl.dops.biz.system.dal.role.UserRoleMapper;
import com.dl.dops.biz.system.dal.role.po.RolePO;
import com.dl.dops.biz.system.dal.role.po.UserRolePO;
import com.dl.dops.biz.system.manager.role.RoleManager;
import com.dl.dops.biz.system.manager.role.bo.RoleParamBO;
import com.dl.dops.biz.system.manager.role.bo.RoleSearchParamBO;
import com.dl.dops.biz.system.manager.role.dto.RoleDTO;
import com.dl.dops.biz.system.manager.user.impl.UserRoleMenuRedisCache;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 09:26
 */
@DS("dops")
@Component
public class RoleManagerImpl extends ServiceImpl<RoleMapper, RolePO> implements RoleManager {

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private UserRoleMenuRedisCache userRoleMenuRedisCache;

    @Override
    public Set<Long> findUserRoleByUserId(Long userId) {
        return userRoleMenuRedisCache.getUserRoleIds(userId);
    }

    @Override
    public boolean belongUsers(Long roleId) {
        Assert.notNull(roleId, "角色ID不能为空");
        Integer integer = userRoleMapper
                .selectCount(Wrappers.<UserRolePO>lambdaQuery().eq(UserRolePO::getRoleId, roleId));
        return integer > 0;
    }

    @Override
    public IPage<RoleDTO> findRoles(RoleSearchParamBO roleParamBO) {
        LambdaQueryWrapper<RolePO> wrapper = Wrappers.lambdaQuery();
        String roleName = roleParamBO.getRoleName();
        List<Long> roleIds;
        if (StringUtils.isNoneBlank(roleName)) {
            wrapper.like(RolePO::getName, roleName);
        }
        //按角色id查询，如果角色相关的角色-菜单为空，则返回空
        if (roleParamBO.getUserId() != null) {
            LambdaQueryWrapper<UserRolePO> userRoleWrapper = Wrappers.lambdaQuery();
            userRoleWrapper.eq(UserRolePO::getUserId, roleParamBO.getUserId());
            roleIds = userRoleMapper.selectList(userRoleWrapper).stream().map(UserRolePO::getRoleId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(roleIds)) {
                return new Page<>(roleParamBO.getPageIndex(), roleParamBO.getPageSize());
            }
            wrapper.in(RolePO::getRoleId, roleIds);
        }
        IPage<RolePO> page = baseMapper.selectPage(convert(roleParamBO), wrapper);
        return page.convert(
                po -> RoleDTO.builder().roleName(po.getName()).roleId(po.getRoleId()).roleType(po.getRoleType())
                        .createBy(po.getCreateBy()).createDt(po.createDt).modifyBy(po.modifyBy).modifyDt(po.modifyDt)
                        .build());
    }

    @Override
    public void deleteRole(Long roleId) {
        Assert.notNull(roleId, "角色ID不能为空");
        LambdaQueryWrapper<RolePO> wrappers = Wrappers.lambdaQuery();
        wrappers.eq(RolePO::getRoleId, roleId);
        baseMapper.delete(wrappers);
    }

    @Override
    public void update(RoleParamBO roleParamBO) {
        LambdaQueryWrapper<RolePO> wrappers = Wrappers.lambdaQuery();
        wrappers.eq(RolePO::getRoleId, roleParamBO.getRoleId());
        RolePO po = baseMapper.selectOne(wrappers);

        boolean f = false;
        if (StringUtils.isNotEmpty(roleParamBO.getRoleName())) {
            po.setName(roleParamBO.getRoleName());
            f = true;
        }
        if (StringUtils.isNotEmpty(roleParamBO.getRoleType())) {
            po.setRoleType(roleParamBO.getRoleType());
            f = true;
        }

        if (f) {
            baseMapper.update(po, wrappers);
        }
    }

}
