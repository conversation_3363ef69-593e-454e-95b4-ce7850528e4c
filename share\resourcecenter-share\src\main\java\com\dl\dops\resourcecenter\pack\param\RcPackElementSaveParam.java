package com.dl.dops.resourcecenter.pack.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 16:12
 */
@Data
@ApiModel("服务包元素保存参数")
public class RcPackElementSaveParam implements Serializable {

    private static final long serialVersionUID = 4800528123832301338L;
    @ApiModelProperty("元素id")
    private String elementId;

    /**
     * 元素类型
     *
     * @see：PackElementTypeEnum
     */
    @ApiModelProperty("元素类型")
    private Integer type;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 文字内容
     */
    @ApiModelProperty("文字内容")
    private String content;

    @ApiModelProperty("附件列表")
    private List<RcPackElementAttachmentSaveParam> attachmentList;

    /**
     * 扩展数据
     * 存储json数据。
     * 当type是10时，存储：{"productList":[{"productCode":"111","sort":1},{"productCode":"222","sort":2},{"productCode":"333","sort":3}]}
     * 当type是12时，存储：{"vaDynamicTemplateId":""}
     */
    @ApiModelProperty("扩展数据")
    private String extData;

    @ApiModelProperty("摘要")
    private String remark;

}
