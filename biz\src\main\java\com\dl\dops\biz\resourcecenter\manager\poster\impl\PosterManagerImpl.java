package com.dl.dops.biz.resourcecenter.manager.poster.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.resourcecenter.dal.category.CategoryMapper;
import com.dl.dops.biz.resourcecenter.dal.poster.PosterDrawMapper;
import com.dl.dops.biz.resourcecenter.dal.poster.PosterMapper;
import com.dl.dops.biz.resourcecenter.dal.poster.po.PosterPO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.resourcecenter.manager.poster.PosterDrawManager;
import com.dl.dops.biz.resourcecenter.manager.poster.PosterManager;
import com.dl.dops.biz.resourcecenter.manager.poster.bo.PosterBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PosterManagerImpl extends ServiceImpl<PosterMapper, PosterPO> implements PosterManager {

    private static final int MAX_LENGTH = 30;

    @Autowired
    private PosterDrawMapper posterDrawMapper;
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private OperatorUtil operatorUtil;
    @Autowired
    private CategoryMapper categoryMapper;
    @Autowired
    private PosterDrawManager posterDrawManager;

    @Override
    public List<PosterBO> batchDetail(List<Long> bizIds) {
        Assert.notEmpty(bizIds, "入参bizIds不能为空");

        List<PosterPO> posterPOList = baseMapper.selectList(
                Wrappers.lambdaQuery(PosterPO.class).in(PosterPO::getBizId, bizIds)
                        .eq(PosterPO::getIsDeleted, Const.ZERO));

        return posterPOList.stream().map(po -> PosterBO.builder().bizId(po.getBizId().toString()).name(po.getName())
                .categoryId(po.getCategoryId().toString()).logoImgUrl(getWholeCosPath(po.getLogoImgUrl()))
                .imgUrl(getWholeCosPath(po.getImgUrl())).build()).collect(Collectors.toList());
    }

    private String getWholeCosPath(String relativeUrl) {
        if (StringUtils.isBlank(relativeUrl)) {
            return StringUtils.EMPTY;
        }

        if (StringUtils.startsWith(relativeUrl, Const.HTTP_PREFIX)) {
            return relativeUrl;
        }

        StringBuffer buffer = new StringBuffer();
        if (!StringUtils.startsWith(relativeUrl, Const.SLASH)) {
            buffer.append(Const.SLASH);
        }
        buffer.append(relativeUrl);
        return buffer.toString();
    }
}
