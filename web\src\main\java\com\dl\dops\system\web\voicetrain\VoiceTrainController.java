package com.dl.dops.system.web.voicetrain;

import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainSourceDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainJobDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainJobPageQueryDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultPageQueryDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultUpdateNameParamDTO;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.forest.aiservice.AiServiceManager;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.system.web.voicetrain.helper.VoiceTrainHelper;
import com.dl.dops.system.web.voicetrain.param.AudioTrainParam;
import com.dl.dops.system.web.voicetrain.param.VoiceTrainJobPageQueryParam;
import com.dl.dops.system.web.voicetrain.param.VoiceTrainResultPageQueryParam;
import com.dl.dops.system.web.voicetrain.param.VoiceTrainResultUpdateNameParam;
import com.dl.dops.system.web.voicetrain.vo.VoiceTrainJobVO;
import com.dl.dops.system.web.voicetrain.vo.VoiceTrainResultVO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-07 09:55
 */
@RestController
@RequestMapping("/dops/voice/train")
public class VoiceTrainController extends AbstractController {

    @Resource
    private AiServiceManager aiServiceManager;

    @ApiOperation("分页查询声音训练结果")
    @PostMapping("/trainresult/page")
    public ResultPageModel<VoiceTrainResultVO> pageTrainResult(@RequestBody VoiceTrainResultPageQueryParam queryParam) {
        VoiceTrainResultPageQueryDTO queryDTO = VoiceTrainHelper.cnvVoiceTrainResultPageQueryParam2DTO(queryParam);
        ResultPageModel<VoiceTrainResultDTO> dtoResultModel = aiServiceManager.pageTrainResult(queryDTO);
        if (!dtoResultModel.isSuccess()) {
            throw BusinessServiceException.getInstance(dtoResultModel.getCode(), dtoResultModel.getMessage());
        }

        return pageQueryModel(dtoResultModel,
                dtoResultModel.getDataResult().stream().map(VoiceTrainHelper::cnvTrainResultDTO2VO)
                        .collect(Collectors.toList()));
    }

    @ApiOperation("修改声音训练结果的训练名")
    @PostMapping("/trainresult/updatename")
    public ResultModel<Void> updateTrainResultName(@RequestBody @Validated VoiceTrainResultUpdateNameParam param) {
        VoiceTrainResultUpdateNameParamDTO paramDTO = new VoiceTrainResultUpdateNameParamDTO();
        paramDTO.setName(param.getName());
        paramDTO.setChannel(param.getChannel());
        paramDTO.setExtModelCode(param.getExtModelCode());
        paramDTO.setTrainType(param.getTrainType());

        return ResultModel.success(aiServiceManager.updateTrainResultName(paramDTO));
    }

    @ApiOperation("分页查询声音训练结果下的训练任务")
    @PostMapping("/trainresult/job/page")
    public ResultPageModel<VoiceTrainJobVO> pageTrainResultJob(
            @RequestBody @Validated VoiceTrainJobPageQueryParam queryParam) {
        VoiceTrainJobPageQueryDTO queryDTO = new VoiceTrainJobPageQueryDTO();
        queryDTO.setChannel(queryParam.getChannel());
        queryDTO.setExtModelCode(queryParam.getExtModelCode());
        queryDTO.setTrainType(queryParam.getTrainType());
        queryDTO.setPageIndex(queryParam.getPageIndex());
        queryDTO.setPageSize(queryParam.getPageSize());

        ResultPageModel<VoiceTrainJobDTO> dtoResultModel = aiServiceManager.pageTrainResultJob(queryDTO);
        if (!dtoResultModel.isSuccess()) {
            throw BusinessServiceException.getInstance(dtoResultModel.getCode(), dtoResultModel.getMessage());
        }
        return pageQueryModel(dtoResultModel,
                dtoResultModel.getDataResult().stream().map(VoiceTrainHelper::cnvVoiceTrainJobDTO2VO)
                        .collect(Collectors.toList()));
    }

    @ApiOperation("提交声音训练")
    @PostMapping("/submit")
    public ResultModel<Void> voiceTrain(@RequestBody @Validated AudioTrainParam param) {
        AudioTrainParamDTO paramDTO = new AudioTrainParamDTO();
        paramDTO.setSpeaker(param.getSpeaker());
        paramDTO.setGender(0);
        paramDTO.setLanguage("zh");
        paramDTO.setSource(Const.TWO);
        paramDTO.setExtModelCode(param.getExtModelCode());
        AudioTrainSourceDTO audioTrainSourceDTO = new AudioTrainSourceDTO();
        audioTrainSourceDTO.setLink(param.getLink());
        paramDTO.setSources(Lists.newArrayList(audioTrainSourceDTO));

        aiServiceManager.audioTrain(param.getChannel(), paramDTO);
        return ResultModel.success(null);
    }

}
