package com.dl.dops.biz.resourcecenter.manager.tag.helper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagGroupPO;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagPO;
import com.dl.dops.biz.resourcecenter.es.tag.po.EsIndexRcTag;
import com.dl.dops.resourcecenter.tag.dto.TagDetailDTO;
import com.dl.dops.resourcecenter.tag.dto.TagGroupDetailDTO;
import com.dl.dops.resourcecenter.tag.dto.TagGroupPageDTO;
import com.dl.dops.resourcecenter.tag.dto.TagInfoDTO;
import com.dl.dops.resourcecenter.tag.dto.TagSearchRespDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class TagHelper {

    public static Page<TagGroupPageDTO> buildPagePo2PageDTO(IPage<TagGroupPO> source,
            Map<Long, List<TagPO>> tagListMap) {
        if (Objects.isNull(source)) {
            return null;
        }
        List<TagGroupPageDTO> tagGroupPageDTOList = source.getRecords().stream().map(item -> {
            TagGroupPageDTO tagGroupPageDTO = new TagGroupPageDTO();
            tagGroupPageDTO.setTagGroupName(item.getName());
            tagGroupPageDTO.setOrder(item.getOrdered());
            tagGroupPageDTO.setTagGroupId(item.getTagGroupId().toString());

            if (MapUtils.isNotEmpty(tagListMap)) {
                List<TagPO> tagPOList = tagListMap.get(item.getTagGroupId());
                if (CollectionUtils.isNotEmpty(tagPOList)) {
                    tagGroupPageDTO.setTagInfoDTOList(
                            tagPOList.stream().map(TagHelper::cnvTagPO2DTO).collect(Collectors.toList()));
                }
            }

            return tagGroupPageDTO;
        }).collect(Collectors.toList());

        Page<TagGroupPageDTO> resp = new Page<>();
        resp.setSize(source.getSize());
        resp.setPages(source.getPages());
        resp.setCurrent(source.getCurrent());
        resp.setTotal(source.getTotal());
        resp.setRecords(tagGroupPageDTOList);
        return resp;
    }

    public static TagGroupDetailDTO buildTagGroupDetailDTO(TagGroupPO source, List<TagPO> tagPOList,
            Map<Long, Integer> tagRelaCountMap) {
        if (Objects.isNull(source)) {
            return null;
        }
        TagGroupDetailDTO dto = new TagGroupDetailDTO();
        dto.setName(source.getName());
        dto.setOrder(dto.getOrder());
        dto.setTagGroupId(String.valueOf(source.getTagGroupId()));
        if (CollectionUtils.isNotEmpty(tagPOList)) {
            List<TagDetailDTO> tagInfoDTOS = tagPOList.stream().map(item -> {
                TagDetailDTO tagDTO = new TagDetailDTO();
                tagDTO.setTagName(item.getName());
                tagDTO.setTagId(item.getTagId().toString());
                tagDTO.setTagGroupId(String.valueOf(item.getTagGroupId()));
                tagDTO.setOrder(item.getOrdered());
                Integer relaCount = tagRelaCountMap.get(item.getTagId());
                tagDTO.setRelaCount(Objects.nonNull(relaCount) ? relaCount : Const.ZERO);
                return tagDTO;
            }).collect(Collectors.toList());
            dto.setTagList(tagInfoDTOS);
        }
        return dto;
    }

    public static TagSearchRespDTO cnvTagDetailPO2RespDTO(EsIndexRcTag input) {
        TagSearchRespDTO result = new TagSearchRespDTO();
        result.setTagId(String.valueOf(input.getTagId()));
        result.setOrder(input.getOrdered());
        result.setTagGroupName(input.getGroupName());
        result.setTagGroupId(String.valueOf(input.getTagGroupId()));
        result.setTagName(input.getName());
        return result;
    }

    public static TagInfoDTO cnvTagPO2DTO(TagPO input) {
        TagInfoDTO result = new TagInfoDTO();
        result.setTagId(input.getTagId().toString());
        result.setTagName(input.getName());
        result.setOrder(input.getOrdered());
        result.setTagGroupId(String.valueOf(input.getTagGroupId()));
        return result;
    }

}
