package com.dl.dops.biz.common.forest.basicservice.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-15 14:24
 */
@Data
public class BasicServiceTenantMenuSaveParamDTO {

    @NotBlank(message = "租户编码不能为空")
    @ApiModelProperty("租户code")
    private String tenantCode;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("菜单主键数组")
    private List<String> menuIds;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("功能编码数组")
    private List<String> functionIds;

    @ApiModelProperty(value = "操作人id", hidden = true)
    private Long operatorId;

    @ApiModelProperty(value = "操作人名", hidden = true)
    private String operatorName;
}
