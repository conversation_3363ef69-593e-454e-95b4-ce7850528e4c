package com.dl.dops.biz.resourcecenter.manager.category.convert;

import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryDetailBO;
import com.dl.dops.biz.resourcecenter.dal.category.po.CategoryPO;
import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryBO;
import com.google.common.collect.Lists;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-10 14:11
 */
public class CategoryConvert {

    public static CategoryBO convert(CategoryPO category) {
        if (Objects.isNull(category)) {
            return null;
        }
        CategoryBO categoryBO = new CategoryBO();
        categoryBO.setId(category.getBizId().toString());
        categoryBO.setCategoryType(category.getCategoryType());
        categoryBO.setName(category.getName());
        categoryBO.setCategoryLevel(category.getCategoryLevel());
        categoryBO.setParentId(category.getParentId().toString());
        categoryBO.setLink(category.getLink());
        return categoryBO;
    }

    public static CategoryDetailBO cnvCategoryPO2DetailBO(CategoryPO category) {
        if (Objects.isNull(category)) {
            return null;
        }
        CategoryDetailBO categoryBO = new CategoryDetailBO();
        categoryBO.setId(category.getBizId().toString());
        categoryBO.setCategoryType(category.getCategoryType());
        categoryBO.setName(category.getName());
        categoryBO.setCategoryLevel(category.getCategoryLevel());
        categoryBO.setParentId(category.getParentId().toString());
        categoryBO.setChildList(Lists.newArrayList());
        categoryBO.setLink(category.getLink());
        return categoryBO;
    }
}
