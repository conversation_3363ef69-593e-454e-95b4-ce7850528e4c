package com.dl.dops.system.web.virtualman.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Date;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SceneDetailInfoVO {

    @ApiModelProperty("数字人场景主键Id")
    String id;

    @ApiModelProperty("数字人唯一标识bizId")
    String vmBizId;

    @ApiModelProperty("数字人场景ID")
    String sceneId;

    @ApiModelProperty("数字人场景名称")
    String sceneName;

    @ApiModelProperty("场景封面地址")
    String coverUrl;

    @ApiModelProperty("数字人来源渠道")
    Integer channel;

    @ApiModelProperty("数字人来源编码")
    String vmCode;

    @ApiModelProperty("数字人启用状态： 0 否 ；1 启用")
    Integer enableState;

    @ApiModelProperty("服装信息：0 个人服饰 1 黑衣服 2 蓝礼服")
    private Integer cloth;

    @ApiModelProperty("姿态信息：1 坐姿; 2 半身站姿; 3 全身站姿")
    private Integer pose;

    @ApiModelProperty("分辨率：1 1080x1920; 2 1920x1080")
    private Integer resolution;

    @ApiModelProperty("场景样例视频地址")
    private String exampleUrl;

    @ApiModelProperty("场景样例文本")
    private String exampleText;

    @ApiModelProperty("场景样例时长，毫秒")
    private Integer exampleDuration;
}
