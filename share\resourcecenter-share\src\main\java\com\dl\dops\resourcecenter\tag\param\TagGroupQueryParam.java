package com.dl.dops.resourcecenter.tag.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-07 15:26
 */
@Data
public class TagGroupQueryParam implements Serializable {

    private static final long serialVersionUID = -5372894999331259593L;
    @NotNull(message = "标签组类型不能为空")
    private Integer type;

    @ApiModelProperty("是否需要标签信息，0-否，1-是")
    private Integer needTags = 0;

    @ApiModelProperty("是否需要关联数量，0-否，1-是")
    private Integer needRelCount = 0;
}
