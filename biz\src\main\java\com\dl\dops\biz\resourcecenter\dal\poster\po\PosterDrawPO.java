package com.dl.dops.biz.resourcecenter.dal.poster.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@TableName("poster_draw")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PosterDrawPO {

    private Long id;

    private Long posterBizId;

    private String optDetail;
}