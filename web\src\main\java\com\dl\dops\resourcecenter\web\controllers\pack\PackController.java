package com.dl.dops.resourcecenter.web.controllers.pack;

import com.dl.dops.resourcecenter.pack.dto.RcPackDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackDetailDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackDomainDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackPageDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackSaveRespDTO;
import com.dl.dops.resourcecenter.pack.dto.RcRcPackChainPathDetailDTO;
import com.dl.dops.resourcecenter.pack.param.RcPackAddParam;
import com.dl.dops.resourcecenter.pack.param.RcPackChainPathContentSaveParam;
import com.dl.dops.resourcecenter.pack.param.RcPackPageQueryParam;
import com.dl.dops.resourcecenter.pack.param.RcPackUpdateParam;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-11 15:20
 */
@Slf4j
@RestController
@RequestMapping({ "/resource/pack", "/dops/resource/pack" })
@Api("资源中心 - 服务包")
public class PackController {

    @Autowired
    private PackProcess packProcess;

    @PostMapping("/add")
    @ApiOperation("新增服务包")
    public ResultModel<RcPackSaveRespDTO> add(@RequestBody @Validated RcPackAddParam param) {
        return packProcess.add(param);
    }

    @PostMapping("/edit")
    @ApiOperation("编辑服务包")
    public ResultModel<RcPackSaveRespDTO> edit(@RequestBody @Validated RcPackUpdateParam param) {
        return packProcess.edit(param);
    }

    @PostMapping({ "/page", "/tenantauth/page" })
    @ApiOperation("查询服务包列表")
    public ResultPageModel<RcPackPageDTO> page(@RequestBody @Validated RcPackPageQueryParam param) {
        return packProcess.page(param);
    }

    @GetMapping({ "/info", "/tenantauth/info" })
    @ApiOperation("服务包信息")
    public ResultModel<RcPackDTO> info(@RequestParam("packId") String packId) {
        Assert.isTrue(StringUtils.isNumeric(packId), "服务包id不能为空");
        return packProcess.info(Long.valueOf(packId));
    }

    @GetMapping("/delete")
    @ApiOperation("删除服务包")
    public ResultModel<Boolean> delete(@RequestParam("packId") String packId) {
        return packProcess.delete(Long.valueOf(packId));
    }

    @PostMapping("/savechainpathcontent")
    @ApiOperation("保存链路内容")
    public ResultModel<RcRcPackChainPathDetailDTO> saveChainPathContent(
            @RequestBody @Validated RcPackChainPathContentSaveParam saveParam) {
        return packProcess.saveChainPathContent(saveParam);
    }

    @GetMapping({ "/chainpathdetaillist", "/tenantauth/chainpathdetaillist" })
    @ApiOperation("查询链路详情列表")
    public ResultModel<List<RcRcPackChainPathDetailDTO>> chainPathDetailList(@RequestParam("packId") String packId) {
        return packProcess.chainPathDetailList(Long.valueOf(packId));
    }

    @GetMapping({ "/detail", "/tenantauth/detail" })
    @ApiOperation("服务包详情")
    public ResultModel<RcPackDetailDTO> detail(@RequestParam("packId") String packId){
        return packProcess.detail(Long.valueOf(packId));
    }

    @GetMapping({ "/listdomain", "/tenantauth/listdomain" })
    @ApiOperation("查询适用行业列表")
    public ResultModel<List<RcPackDomainDTO>> listDomain() {
        return packProcess.listDomain();
    }

    @Deprecated
    @GetMapping("/publishorcancel")
    @ApiOperation("模板发布或取消发布")
    public ResultModel publishOrCancel(@RequestParam("packId") String packId) {
        return packProcess.publishOrCancel(Long.valueOf(packId));
    }
}
