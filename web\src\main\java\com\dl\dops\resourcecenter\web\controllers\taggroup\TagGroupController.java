package com.dl.dops.resourcecenter.web.controllers.taggroup;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.resourcecenter.dal.material.po.MaterialPO;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagGroupPO;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagPO;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagRelaPO;
import com.dl.dops.biz.resourcecenter.manager.material.MaterialManager;
import com.dl.dops.biz.resourcecenter.manager.tag.TagGroupManager;
import com.dl.dops.biz.resourcecenter.manager.tag.TagManager;
import com.dl.dops.biz.resourcecenter.manager.tag.TagRelaManager;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagAddBO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagGroupAddBO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagGroupDeleteBO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagGroupEditBO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagPageBO;
import com.dl.dops.resourcecenter.tag.dto.TagGroupDTO;
import com.dl.dops.resourcecenter.tag.dto.TagGroupDetailDTO;
import com.dl.dops.resourcecenter.tag.dto.TagGroupListDTO;
import com.dl.dops.resourcecenter.tag.dto.TagGroupPageDTO;
import com.dl.dops.resourcecenter.tag.param.TagGroupAddParam;
import com.dl.dops.resourcecenter.tag.param.TagGroupDeleteParam;
import com.dl.dops.resourcecenter.tag.param.TagGroupDetailParam;
import com.dl.dops.resourcecenter.tag.param.TagGroupEditParam;
import com.dl.dops.resourcecenter.tag.param.TagGroupPageParam;
import com.dl.dops.resourcecenter.tag.param.TagGroupQueryParam;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.resourcecenter.web.controllers.taggroup.convert.TagGroupHelper;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/dops/taggroup")
public class TagGroupController extends AbstractController {

    @Autowired
    private TagGroupManager tagGroupManager;

    @Autowired
    private TagRelaManager tagRelaManager;

    @Autowired
    private MaterialManager materialManager;

    @Autowired
    private TagManager tagManager;

    @PostMapping("/add")
    @ApiOperation("新增标签组")
    public ResultModel<String> add(@RequestBody TagGroupAddParam param) {
        //参数转换
        TagGroupAddBO tagGroupAddBO = new TagGroupAddBO();
        tagGroupAddBO.setTagGroupName(param.getTagGroupName());
        tagGroupAddBO.setType(param.getType());
        List<TagAddBO> tagAddBOList = param.getTagAddParamList().stream().map(item -> {
            TagAddBO tagAddBO = new TagAddBO();
            tagAddBO.setTagName(item.getTagName());
            tagAddBO.setOrder(item.getOrder());
            return tagAddBO;
        }).collect(Collectors.toList());
        tagGroupAddBO.setTagAddBOList(tagAddBOList);
        //添加
        return ResultModel.success(tagGroupManager.add(tagGroupAddBO).toString());
    }

    @PostMapping("/edit")
    @ApiOperation("修改标签组")
    public ResultModel<String> edit(@RequestBody TagGroupEditParam param){
        TagGroupEditBO tagEditBO = new TagGroupEditBO();
        tagEditBO.setTagGroupId(param.getTagGroupId());
        tagEditBO.setName(param.getName());
        tagEditBO.setOrder(param.getOrder());
        return ResultModel.success( tagGroupManager.edit(tagEditBO).toString());
    }

    @PostMapping("/page")
    @ApiOperation("标签组分页")
    public ResultPageModel<TagGroupPageDTO> page(@RequestBody TagGroupPageParam param) {
        TagPageBO listBO = new TagPageBO();
        listBO.setType(param.getType());
        listBO.setPageIndex(param.getPageIndex());
        listBO.setPageSize(param.getPageSize());
        IPage<TagGroupPageDTO> page = tagGroupManager.page(listBO);
        return pageQueryModel(page);
    }

    @PostMapping("/list")
    @ApiOperation("标签组列表")
    public ResultModel<TagGroupListDTO> listAll(@RequestBody @Validated TagGroupQueryParam param) {
        //查询所有标签组
        List<TagGroupPO> tagGroupPOList = tagGroupManager
                .list(Wrappers.lambdaQuery(TagGroupPO.class).eq(TagGroupPO::getType, param.getType())
                        .eq(TagGroupPO::getIsDelete, Const.ZERO));
        if (CollectionUtils.isEmpty(tagGroupPOList)) {
            return ResultModel.success(null);
        }

        Map<Long, List<TagPO>> tagListMap = this.queryTagListMap(param, tagGroupPOList);
        List<TagGroupDTO> tagGroupDTOList = tagGroupPOList.stream()
                .map(po -> TagGroupHelper.buildTagGroupDTO(po, tagListMap))
                .sorted(Comparator.comparing(TagGroupDTO::getOrder)).collect(Collectors.toList());

        TagGroupListDTO result = new TagGroupListDTO();
        result.setTagGroupList(tagGroupDTOList);

        if (Const.ONE.equals(param.getNeedRelCount())) {
            //查询记录总数
            Integer relaCount = tagRelaManager
                    .count(Wrappers.lambdaQuery(TagRelaPO.class).eq(TagRelaPO::getType, param.getType())
                            .eq(TagRelaPO::getIsDelete, Const.ZERO));
            //查询类型对应的总数 todo: 当前只处理了素材，后续再加sop和服务包
            Integer bizCount = materialManager
                    .count(Wrappers.lambdaQuery(MaterialPO.class).eq(MaterialPO::getIsDeleted, Const.ZERO));
            result.setBizCount(bizCount);
            result.setUnRelaCount(bizCount - relaCount);
        }

        return ResultModel.success(result);
    }

    @PostMapping("/delete")
    @ApiOperation("删除标签组")
    public ResultModel<String> delete(@RequestBody TagGroupDeleteParam param) {
        TagGroupDeleteBO deleteBO = new TagGroupDeleteBO();
        deleteBO.setTagGroupId(param.getTagGroupId());
        deleteBO.setOperatorId(getUserId().toString());
        return ResultModel.success(tagGroupManager.delete(deleteBO));
    }

    @PostMapping("/detail")
    @ApiOperation("标签组详情")
    public ResultModel<TagGroupDetailDTO> detail(@RequestBody TagGroupDetailParam param) {
        return ResultModel
                .success(tagGroupManager.detail(Long.valueOf(param.getTagGroupId()), param.getNeedCountTagRela()));
    }

    private Map<Long, List<TagPO>> queryTagListMap(TagGroupQueryParam param, List<TagGroupPO> tagGroupPOList) {
        if (Const.ZERO.equals(param.getNeedTags())) {
            return null;
        }
        //查询标签数据
        List<TagPO> tagPOList = tagManager.list(Wrappers.lambdaQuery(TagPO.class).in(TagPO::getTagGroupId,
                tagGroupPOList.stream().map(TagGroupPO::getTagGroupId).collect(Collectors.toList()))
                .eq(TagPO::getIsDelete, Const.ZERO));
        //key-tagGroupId
        return tagPOList.stream().collect(Collectors.groupingBy(TagPO::getTagGroupId));
    }

}
