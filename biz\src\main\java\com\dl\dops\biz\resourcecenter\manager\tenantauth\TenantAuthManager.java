package com.dl.dops.biz.resourcecenter.manager.tenantauth;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.resourcecenter.dal.tenant.po.TenantAuthPO;
import com.dl.framework.core.interceptor.expdto.CertificateException;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.resourcecenter.tenantauth.dto.RcTenantAuthTokenDTO;

public interface TenantAuthManager extends IService<TenantAuthPO>, CommonService {

    /**
     * 解密并校验token
     *
     * @param token
     * @param tenantCode
     * @return
     */
    RcTenantAuthTokenDTO encrptAndValidToken(String token, String tenantCode) throws CertificateException;
}
