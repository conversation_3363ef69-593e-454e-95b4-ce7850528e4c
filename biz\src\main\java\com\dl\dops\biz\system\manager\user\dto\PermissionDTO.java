package com.dl.dops.biz.system.manager.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@ApiModel("权限结果")
@Builder
public class PermissionDTO {

    private static final long serialVersionUID = -2798611939217129386L;
    @ApiModelProperty("权限编码")
    private String functionCode;

    @ApiModelProperty("结果，0-无权限，1-有权限")
    private String permission;
}

