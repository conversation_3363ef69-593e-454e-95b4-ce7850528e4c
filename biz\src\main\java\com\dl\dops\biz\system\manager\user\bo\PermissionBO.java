package com.dl.dops.biz.system.manager.user.bo;

import com.dl.dops.biz.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * 功能
 */
@Data
@ApiModel("权限代码")
@NoArgsConstructor
@AllArgsConstructor
public class PermissionBO extends BaseDTO {

    private static final long serialVersionUID = -9112276261639492544L;
    /**
     * 权限编码
     */
    @ApiModelProperty("权限代码list")
    @Size(min = 1, max = 200)
    private List<String> functionCodes;

}
