package com.dl.dops.resourcecenter.sop.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-07 11:15
 */
@Data
@ApiModel("sop事件保存参数")
public class RcSopEventSaveParam implements Serializable {

    private static final long serialVersionUID = 1413354059069816498L;
    @ApiModelProperty("eventId")
    private String eventId;

    /**
     * 1群发 2任务下发 3纯提醒 4企微朋友圈群发 5修改旅程阶段 6一对一私聊
     */
    @ApiModelProperty("触达形式")
    @NotNull
    private Integer reachWay;

    @ApiModelProperty("事项名称")
    @NotBlank(message = "任务标题不能为空")
    @Length(min = 1, max = 20)
    private String name;

    /**
     * 1定时推送   2周期推送
     */
    @ApiModelProperty("时间规则类型")
    @NotNull
    private Integer ruleType;

    @ApiModelProperty("规则内容，周期推送时为cron表达式")
    @NotNull
    private String ruleContent;

    @ApiModelProperty("发送内容")
    @Length(max = 500, message = "发送内容长度最大为500")
    private String content;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("有效期")
    @NotNull(message = "有效期不能为空")
    private Integer validityPeriod;

    @ApiModelProperty("内容保存列表")
    private List<RcSopEventCntSaveParam> sopEventCntList;

}
