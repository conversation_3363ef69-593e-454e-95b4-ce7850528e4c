package com.dl.dops.resourcecenter.material.enums;

import java.util.Objects;

/**
 * @describe: 素材类型
 * @author: zhousx
 * @date: 2022/5/16 15:51
 */
public enum RcMaterialTypeEnum {
    ARTICLE(1, "文章"),
    WEBPAGE(2, "网页"),
    VIDEO(3, "视频"),
    FILE(4, "文件"),
    TEXT(5, "文本"),
    IMAGE(6, "图片"),
    ;

    private Integer code;

    private String desc;

    RcMaterialTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RcMaterialTypeEnum parse(Integer value) {
        if (Objects.isNull(value)) {
            return null;
        }
        for (RcMaterialTypeEnum type : RcMaterialTypeEnum.values()) {
            if (type.code.equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 素材类型的内容是否存放于对象存储；目前包括视频、文件、图片
     *
     * @param targetTypeCode 素材类型代码
     * @return 是否存放于对象存储
     */
    public static boolean isMaterialContentStoredInObjectStorage(Integer targetTypeCode) {
        return VIDEO.code.equals(targetTypeCode) || FILE.code.equals(targetTypeCode) || IMAGE.code
                .equals(targetTypeCode);
    }

}
