package com.dl.dops.biz.resourcecenter.manager.tenantauth.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-08 17:06
 */
public enum TenantAuthStatusEnum {

    ENABLE(1, "已启用"),
    DISABLE(2, "已停用");

    private Integer code;

    private String desc;

    TenantAuthStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TenantAuthStatusEnum parse(Integer status) {
        if (Objects.isNull(status)) {
            return null;
        }
        for (TenantAuthStatusEnum statusEnum : TenantAuthStatusEnum.values()) {
            if (statusEnum.getCode().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
