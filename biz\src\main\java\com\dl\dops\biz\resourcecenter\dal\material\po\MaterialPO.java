package com.dl.dops.biz.resourcecenter.dal.material.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

@Data
@TableName("material")
public class MaterialPO extends BasePO {
    private static final long serialVersionUID = 6633066125932225328L;
    @TableId
    public Long id;

    @TableField("biz_id")
    private Long bizId;

    @TableField("title")
    private String title;

    @TableField("remark")
    private String remark;

    @TableField("logo_img")
    private String logoImg;

    @TableField("is_deleted")
    private Integer isDeleted;

    @TableField("content")
    private String content;

    @TableField("material_type")
    private Integer materialType;

    @TableField("article_type")
    private Integer articleType;

    @TableField("source_url")
    private String sourceUrl;

    @TableField("size")
    private Integer size;

    @TableField("category_ids")
    @Deprecated
    private String categoryIds;

    @TableField("publish_status")
    private Integer publishStatus;

    @TableField("source")
    private String source;

    /**
     * 添加来源：0资源中心，1本地上传
     */
    @TableField("create_from")
    private Integer createFrom = 0;

    @TableField("creator_name")
    private String creatorName;

    @TableField("modify_name")
    private String modifyName;
}
