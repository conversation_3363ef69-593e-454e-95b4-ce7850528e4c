package com.dl.dops.biz.common.forest.magicvideo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @describe: TemplateAuthVO
 * @author: zhousx
 * @date: 2023/6/18 23:37
 */
@Data
public class TenantTemplateAuthDTO {
    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("模板封面")
    private String coverUrl;

    @ApiModelProperty("预览视频")
    private String previewVideoUrl;

    @ApiModelProperty("状态 0-启用 1-禁用")
    private Integer status;

    @ApiModelProperty("创建时间")
    private Date createDt;
}
