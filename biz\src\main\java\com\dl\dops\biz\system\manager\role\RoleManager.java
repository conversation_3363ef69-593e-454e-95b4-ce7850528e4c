package com.dl.dops.biz.system.manager.role;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.system.dal.role.po.RolePO;
import com.dl.dops.biz.system.manager.role.bo.RoleParamBO;
import com.dl.dops.biz.system.manager.role.bo.RoleSearchParamBO;
import com.dl.dops.biz.system.manager.role.dto.RoleDTO;

import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 09:26
 */
public interface RoleManager extends IService<RolePO>, CommonService {

    /**
     * 查询员工拥有的角色id
     *
     * @param userId
     * @return
     */
    Set<Long> findUserRoleByUserId(Long userId);

    /**
     * 员工是否拥有该角色
     *
     * @param roleId
     * @return
     */
    boolean belongUsers(Long roleId);

    IPage<RoleDTO> findRoles(RoleSearchParamBO roleParamBO);

    void deleteRole(Long roleId);

    void update(RoleParamBO roleParamBO);

}
