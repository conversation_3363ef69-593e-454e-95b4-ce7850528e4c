package com.dl.dops.biz.common.manager.storepolicy;

import com.dl.dops.biz.common.tencentcloud.cos.GetTempCredentialBO;
import com.dl.dops.biz.common.manager.storepolicy.dto.BaseTempCredentialDTO;

import java.io.File;

/**
 * @ClassName StorePolicyManager
 * @Description
 * <AUTHOR>
 * @Date 2022/10/10 9:16
 * @Version 1.0
 **/
public interface StorePolicyManager {

    /**
     * 上传文件到存储
     *
     * @param file
     * @param tenantCode
     * @param type
     * @param path
     * @return
     */
    String uploadFile(File file, String tenantCode, String type, String path);

    /**
     * 获取临时访问token
     *
     * @param param
     * @return
     */
    BaseTempCredentialDTO getTempCredential(GetTempCredentialBO param);

    /**
     * 获取当前配置的存储策略
     *
     * @return
     */
    String getStorePolicy();
}
