package com.dl.dops.resourcecenter.web.controllers;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.system.manager.user.dto.BasicUserDTO;
import com.dl.dops.biz.system.manager.user.dto.UserDTO;
import com.dl.dops.system.web.util.AccountCompent;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.controller.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Set;
import java.util.function.Function;

@Slf4j
@Validated
public class AbstractController extends BaseController {

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private OperatorUtil operatorUtil;

    @Resource
    private AccountCompent accountCompent;

    protected BasicUserDTO getCurrentUser() {
        return accountCompent.getUser();
    }

    protected Long getUserId() {
        return operatorUtil.getOperator();
    }

    protected String getUserName() {
        return operatorUtil.getOperatorName();
    }

    protected Set<Long> getUserRoleids() {
        return accountCompent.getRoleIds();
    }

    protected UserDTO getCurrentDetail() {
        return accountCompent.getCurrentDetail();
    }

    protected <T> ResponsePageQueryDO<T> pageQueryDO(IPage<T> page) {
        ResponsePageQueryDO qd = new ResponsePageQueryDO();
        qd.setPageIndex(page.getCurrent());
        qd.setPageSize(page.getSize());
        qd.setTotal(page.getTotal());
        qd.setDataResult(page.getRecords());
        return qd;
    }

    protected <T> ResultPageModel<T> pageQueryModel(IPage<T> page) {
        ResultPageModel<T> model = new ResultPageModel<>();
        model.setPageIndex(page.getCurrent());
        model.setPageSize(page.getSize());
        model.setTotalPage(page.getPages());
        model.setTotal(page.getTotal());
        model.setDataResult(page.getRecords());
        return model;
    }

    protected <T, R> ResultPageModel<R> pageQueryModel(IPage<T> page, Function<T, R> function) {
        ResultPageModel<R> model = new ResultPageModel<>();
        model.setPageIndex(page.getCurrent());
        model.setPageSize(page.getSize());
        model.setTotalPage(page.getPages());
        model.setTotal(page.getTotal());
        model.setDataResult(page.convert(t -> function.apply(t)).getRecords());
        return model;
    }

    protected <T> ResultPageModel<T> pageQueryModel(ResponsePageQueryDO page, Collection<T> records) {
        ResultPageModel<T> model = new ResultPageModel<>();
        model.setPageIndex(page.getPageIndex());
        model.setPageSize(page.getPageSize());
        model.setTotalPage(page.getTotalPage());
        model.setTotal(page.getTotal());
        model.setDataResult(records);
        return model;
    }

    protected <T> ResultPageModel<T> pageQueryModel(IPage page, Collection<T> records) {
        ResultPageModel<T> model = new ResultPageModel<>();
        model.setPageIndex(page.getCurrent());
        model.setPageSize(page.getSize());
        model.setTotalPage(page.getPages());
        model.setTotal(page.getTotal());
        model.setDataResult(records);
        return model;
    }

    protected <T> ResultPageModel<T> pageQueryModel(ResultPageModel page, Collection<T> records) {
        ResultPageModel<T> model = new ResultPageModel<>();
        model.setPageIndex(page.getPageIndex());
        model.setPageSize(page.getPageSize());
        model.setTotalPage(page.getTotalPage());
        model.setTotal(page.getTotal());
        model.setDataResult(records);
        return model;
    }

}
