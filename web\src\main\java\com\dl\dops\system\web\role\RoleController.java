/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dl.dops.system.web.role;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dl.dops.biz.common.annotation.Permission;
import com.dl.dops.biz.common.validation.ValidateStrategy;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.biz.system.dal.role.po.RolePO;
import com.dl.dops.biz.system.manager.role.RoleManager;
import com.dl.dops.biz.system.manager.role.RoleMenuManager;
import com.dl.dops.biz.system.manager.role.bo.RoleParamBO;
import com.dl.dops.biz.system.manager.role.bo.RoleSearchParamBO;
import com.dl.dops.biz.system.manager.role.dto.RoleDTO;
import com.dl.dops.biz.system.manager.role.enums.RoleTypeEnum;
import com.dl.dops.system.web.role.param.RoleParam;
import com.dl.dops.system.web.role.param.RoleSearchParam;
import com.dl.dops.system.web.role.vo.RoleVO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Api(tags = "系统：角色管理")
@RequestMapping("/dops/role")
public class RoleController extends AbstractController {

    @Autowired
    private RoleManager roleManager;
    @Autowired
    private RoleMenuManager roleMenuManager;
    @Autowired
    private HostTimeIdg hostTimeIdg;

    @Permission("adm:role:query")
    @PostMapping("/page")
    @ApiOperation("查询角色列表")
    public ResultPageModel<RoleVO> list(@Validated @RequestBody RoleSearchParam roleSearchParam) {
        RoleSearchParamBO roleParamBO = new RoleSearchParamBO();
        roleParamBO.setRoleName(roleSearchParam.getRoleName());
        roleParamBO.setUserId(roleSearchParam.getUserId());
        roleParamBO.setPageIndex(roleSearchParam.getPageIndex());
        roleParamBO.setPageSize(roleSearchParam.getPageSize());

        IPage<RoleDTO> page = roleManager.findRoles(roleParamBO);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return pageQueryModel(page, Collections.emptyList());
        }
        List<RoleVO> resultList = page.getRecords().stream().map(dto -> {
            RoleVO roleVO = new RoleVO();
            roleVO.setRoleId(String.valueOf(dto.getRoleId()));
            roleVO.setRoleName(dto.getRoleName());
            roleVO.setRoleType(dto.getRoleType());
            roleVO.setCreateBy(dto.getCreateBy());
            roleVO.setCreateDt(dto.getCreateDt());
            roleVO.setModifyBy(dto.getModifyBy());
            roleVO.setModifyDt(dto.getModifyDt());
            return roleVO;
        }).collect(Collectors.toList());
        return pageQueryModel(page, resultList);
    }

    @Permission("adm:role:add")
    @PostMapping("/add")
    @ApiOperation("新增角色")
    public ResultModel<Boolean> add(@Validated @RequestBody RoleParam roleParam) {
        RolePO po = new RolePO();
        po.setRoleType(RoleTypeEnum.NORMAL.getCode());
        po.setName(roleParam.getRoleName());
        po.setCreateBy(getUserId());
        po.setRoleId(hostTimeIdg.generateId().longValue());
        roleManager.save(po);
        return ResultModel.success(true);
    }

    @Permission("adm:role:update")
    @PostMapping("/update")
    @ApiOperation("修改角色基本信息")
    public ResultModel<Boolean> update(@Validated(ValidateStrategy.update.class) @RequestBody RoleParam roleParam) {
        RoleParamBO roleParamBO = new RoleParamBO();
        roleParamBO.setRoleId(roleParam.getRoleId());
        roleParamBO.setRoleName(roleParam.getRoleName());
        roleManager.update(roleParamBO);
        return ResultModel.success(true);
    }

    @Permission("adm:role:delete")
    @PostMapping("/delete")
    @ApiOperation("删除角色信息")
    public ResultModel<Boolean> delete(@Validated @RequestBody RoleParam roleParam) {
        if (roleManager.belongUsers(roleParam.getRoleId())) {
            return ResultModel.error("1", "用户拥有此角色，不能删除。请先解除用户与此角色的关联");
        }
        if (roleMenuManager.hasMenu(roleParam.getRoleId())) {
            return ResultModel.error("1", "该角色拥有部分菜单权限，不能删除。请先解除角色拥有的菜单权限");
        }
        roleManager.deleteRole(roleParam.getRoleId());
        return ResultModel.success(true);
    }

}
