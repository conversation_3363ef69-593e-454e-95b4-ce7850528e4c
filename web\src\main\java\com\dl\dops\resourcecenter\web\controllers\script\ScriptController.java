package com.dl.dops.resourcecenter.web.controllers.script;

import com.dl.dops.resourcecenter.script.dto.RcScriptDTO;
import com.dl.dops.resourcecenter.script.dto.RcScriptUploadResultDTO;
import com.dl.dops.resourcecenter.script.param.RcScriptAddParam;
import com.dl.dops.resourcecenter.script.param.RcScriptEditParam;
import com.dl.dops.resourcecenter.script.param.RcScriptPageQueryParam;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * @describe: CntScriptController
 * @author: zhousx
 * @date: 2022/6/14 17:04
 */
@Slf4j
@RestController
@RequestMapping({ "/resource/script", "/dops/resource/script" })
@Api("资源中心 - 话术库")
public class ScriptController {
    @Autowired
    private ScriptProcess scriptProcess;

    @PostMapping("/add")
    @ApiOperation("新增话术")
    public ResultModel<RcScriptDTO> add(@RequestBody @Validated RcScriptAddParam param) {
        return scriptProcess.add(param);
    }
    
    @PostMapping("/page")
    @ApiOperation("分页查询话术")
    public ResultPageModel<RcScriptDTO> page(@RequestBody @Validated RcScriptPageQueryParam param) {
        return scriptProcess.page(param);
    }

    @PostMapping("/detail/{scriptId}")
    @ApiOperation("话术详情")
    public ResultModel<RcScriptDTO> detail(@PathVariable Long scriptId) {
        return scriptProcess.detail(scriptId);
    }

    @PostMapping("/delete/{scriptId}")
    @ApiOperation("删除话术")
    public ResultModel<Void> delete(@PathVariable Long scriptId) {
        return scriptProcess.delete(scriptId);
    }

    @PostMapping("/edit")
    @ApiOperation("编辑话术")
    public ResultModel<Void> edit(@RequestBody @Validated RcScriptEditParam param) {
        return scriptProcess.edit(param);
    }

    @PostMapping("/upload")
    @ApiOperation("批量上传话术")
    public ResultModel<RcScriptUploadResultDTO> upload(MultipartFile file) {
        return scriptProcess.upload(file);
    }

    @RequestMapping("/template/download")
    @ApiOperation("话术模板下载")
    public void templateDownload(HttpServletResponse response) {
        scriptProcess.templateDownload(response);
    }

    @RequestMapping("/downloadfaillist/{batchId}")
    @ApiOperation("失败列表下载")
    public void downloadFailedList(@PathVariable String batchId, HttpServletResponse response) {
        scriptProcess.downloadFailedList(batchId, response);
    }

    @PostMapping("/publishorcancel/{scriptId}")
    @ApiOperation("话术发布或取消发布")
    public ResultModel publishOrCancel(@PathVariable Long scriptId) {
        return scriptProcess.publishOrCancel(scriptId);
    }
}
