package com.dl.dops.biz.system.manager.user.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.properties.session.AbstractSession;
import com.dl.dops.biz.common.util.JwtUtil;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.common.util.RedisUtil;
import com.dl.dops.biz.system.dal.function.po.FunctionPO;
import com.dl.dops.biz.system.dal.user.UserMapper;
import com.dl.dops.biz.system.dal.user.po.UserPO;
import com.dl.dops.biz.system.manager.user.UserManager;
import com.dl.dops.biz.system.manager.user.bo.ResetUserPasswordBO;
import com.dl.dops.biz.system.manager.user.bo.UpdatePwdParamBO;
import com.dl.dops.biz.system.manager.user.bo.UpdateUserSelfBO;
import com.dl.dops.biz.system.manager.user.bo.UserSaveBO;
import com.dl.dops.biz.system.manager.user.bo.UserSearchParamBO;
import com.dl.dops.biz.system.manager.user.dto.BasicUserDTO;
import com.dl.dops.biz.system.manager.user.dto.PermissionDTO;
import com.dl.dops.biz.system.manager.user.dto.PermissionsDTO;
import com.dl.dops.biz.system.manager.user.dto.UserDTO;
import com.dl.dops.biz.system.manager.user.enums.SysUserStatusEnum;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import io.jsonwebtoken.Claims;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 09:26
 */
@Component
@Transactional
@RefreshScope
@DS("dops")
public class UserManagerImpl extends ServiceImpl<UserMapper, UserPO> implements UserManager {

    private static final SecureRandom secureRandom = new SecureRandom();

    private final String subject = "dl-dops";

    @Autowired
    private HostTimeIdg hostTimeIdg;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private UserRoleMenuRedisCache userRoleMenuRedisCache;

    @Autowired
    private OperatorUtil operatorUtil;

    private static BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(BCryptPasswordEncoder.BCryptVersion.$2A, secureRandom);
    }

    private boolean pwdMatch(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = passwordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    private String generatePwd(String rawPassword) {
        BCryptPasswordEncoder passwordEncoder = passwordEncoder();
        return passwordEncoder.encode(rawPassword);
    }

    @Override
    public BasicUserDTO login(UserPO po) {
        UserPO user = baseMapper.selectOne(Wrappers.<UserPO>lambdaQuery().eq(UserPO::getAccount, po.getAccount()));
        if (user != null) {
            Assert.isTrue(Objects.equals(user.getStatus(), Const.ONE), "您已被禁用，请联系管理员");
            if (pwdMatch(po.getPassword(), user.getPassword())) {

                return genUserDTO(user);
            }
        }
        return null;
    }

    private BasicUserDTO genUserDTO(UserPO user) {
        BasicUserDTO dto = BasicUserDTO.builder().account(user.getAccount()).userId(user.getUserId()).build();
        //生成用户登录token
        String token = this.createJwtToken(dto);
        //将token放入缓存
        this.setLoginSession(dto.getUserId(), token);
        dto.setToken(token);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePassword(UpdatePwdParamBO bo) {
        String oldPwd = bo.getPassword().trim();
        String newPwd = bo.getNewPwd().trim();
        Long userId = operatorUtil.getOperator();
        LambdaUpdateWrapper<UserPO> useridWrapper = Wrappers.lambdaUpdate();
        useridWrapper.eq(UserPO::getUserId, userId);
        UserPO po = baseMapper.selectOne(useridWrapper);
        if (pwdMatch(oldPwd, po.getPassword())) {
            po.setPassword(generatePwd(newPwd));
            baseMapper.updateById(po);
            return true;
        } else {
            throw BusinessServiceException.getInstance("原密码错误");
        }
    }

    @Override
    public UserDTO addUser(UserSaveBO saveBO) {
        UserPO sysAdmUserPO = new UserPO();
        sysAdmUserPO.setUserId(hostTimeIdg.generateId().longValue());
        sysAdmUserPO.setIsSuperAdm(0);
        sysAdmUserPO.setAccount(saveBO.getAccount());
        sysAdmUserPO.setStatus(SysUserStatusEnum.REG.getCode());
        sysAdmUserPO.setUserName(saveBO.getUserName());
        sysAdmUserPO.setPassword(getDefaultPwd(saveBO.getPassword()));
        baseMapper.insert(sysAdmUserPO);
        return convert(sysAdmUserPO);
    }

    private String getSessionKey(Long userId) {
        return TOKEN_KEY_PREFIX + userId;
    }

    @Override
    public String formatToken(String token) {
        return token.substring(token.indexOf(" ") + 1);
    }

    @Override
    public void logoutJwtToken(String token) {
        token = formatToken(token);
        if (JwtUtil.isVerify(token)) {
            BasicUserDTO dto = parseJwtToken(token);
            removeLoginSession(dto.getUserId());
        }
    }

    private void removeLoginSession(Long id) {
        redisUtil.del(getSessionKey(id));
    }

    private void setLoginSession(Long id, String token) {
        redisUtil.set(getSessionKey(id), token, AbstractSession.getExpire());
    }

    private String getTokenBySession(Long id) {
        return redisUtil.get(getSessionKey(id));
    }

    @Override
    public String createJwtToken(BasicUserDTO dto) {
        //创建payload的私有声明（根据特定的业务需要添加，如果要拿这个做验证，一般是需要和jwt的接收方提前沟通好验证方式的）
        Map<String, Object> claims = new HashMap<String, Object>();
        claims.put("account", dto.getAccount());
        claims.put("id", dto.getUserId());
        return JwtUtil.createJWT(claims, subject);
    }

    @Override
    public BasicUserDTO parseJwtToken(String token) {
        token = formatToken(token);
        Claims claims = JwtUtil.parseJWT(token);
        Long id = claims.get("id", Long.class);
        //        String sessionToken = getTokenBySession(id);
        //        //判断是否被踢
        //        if (!token.equals(sessionToken)) {
        //            return null;
        //        }
        String account = claims.get("account", String.class);
        return BasicUserDTO.builder().userId(id).account(account).build();
    }

    @Override
    public UserDTO findUserDetail(Long userId) {
        UserDTO dto = userRoleMenuRedisCache.getUserDetail(userId);
        if (dto != null) {
            return dto;
        }
        return this.refreshUserDetailCache(userId);
    }

    @Override
    public UserDTO refreshUserDetailCache(Long userId) {
        UserPO po = baseMapper.selectOne(Wrappers.lambdaQuery(UserPO.class).eq(UserPO::getUserId, userId));
        Assert.notNull(po, "未找到指定系统用户，请联系管理员");

        UserDTO dto = this.convert(po);
        userRoleMenuRedisCache.setUserDetailCache(dto);
        return dto;
    }

    @Override
    public String getSessionToken(Long userId) {
        return redisUtil.get(getSessionKey(userId));
    }

    @Override
    public void refreshToken(Long userId) {
        redisUtil.expire(getSessionKey(userId), AbstractSession.getExpire());
    }

    @Override
    public IPage<UserDTO> findUsers(UserSearchParamBO bo) {
        LambdaQueryWrapper<UserPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.like(StringUtils.isNotEmpty(bo.getAccount()), UserPO::getAccount, bo.getAccount());
        lambdaQueryWrapper.like(StringUtils.isNotBlank(bo.getUserName()), UserPO::getUserName, bo.getUserName());
        lambdaQueryWrapper.orderByAsc(UserPO::getId);
        lambdaQueryWrapper.eq(Objects.nonNull(bo.getStatus()), UserPO::getStatus, bo.getStatus());
        IPage<UserPO> page = baseMapper.selectPage(convert(bo), lambdaQueryWrapper);
        return page.convert(t -> convert(t));
    }

    private UserDTO convert(UserPO t) {
        UserDTO dto = UserDTO.builder().isSuperAdm(t.getIsSuperAdm()).userId(t.getUserId()).account(t.getAccount())
                .userName(t.getUserName()).status(t.getStatus()).createBy(t.getCreateBy()).modifyBy(t.getModifyBy())
                .build();
        return dto;
    }

    @Override
    public boolean hasPermission(Collection<Long> roleIds, String functionCode) {
        return userRoleMenuRedisCache.hasPermission(roleIds, functionCode);
    }

    @Override
    public PermissionsDTO hasPermission(Collection<Long> roleIds, Collection<String> functionCodes) {
        PermissionsDTO dto = new PermissionsDTO();
        List<PermissionDTO> permissionList = new ArrayList<>();
        dto.setPermissionList(permissionList);

        if (CollectionUtils.isNotEmpty(functionCodes)) {
            functionCodes.forEach(f -> {
                boolean h = hasPermission(roleIds, f);
                PermissionDTO permissionDTO = PermissionDTO.builder().permission(h ? "1" : "0").functionCode(f).build();
                permissionList.add(permissionDTO);
            });
        }
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserDetail(UserPO po) {
        Assert.notNull(po.getUserId());

        //1 更新用户信息
        LambdaUpdateWrapper<UserPO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        if (Objects.nonNull(po.getIsSuperAdm())) {
            lambdaUpdateWrapper.set(UserPO::getIsSuperAdm, po.getIsSuperAdm());
        }
        if (StringUtils.isNotBlank(po.getAccount())) {
            UserPO existPO = baseMapper
                    .selectOne(Wrappers.lambdaQuery(UserPO.class).eq(UserPO::getAccount, po.getAccount()));
            if (Objects.nonNull(existPO) && !existPO.getUserId().equals(po.getUserId())) {
                throw BusinessServiceException.getInstance("登录账号已存在，请重新输入");
            } else {
                //修改用户名
                lambdaUpdateWrapper.set(UserPO::getAccount, po.getAccount().trim());
            }
        }
        if (Objects.nonNull(po.getStatus())) {
            lambdaUpdateWrapper.set(UserPO::getStatus, po.getStatus());
        }
        lambdaUpdateWrapper.set(UserPO::getUserName, po.getUserName());
        lambdaUpdateWrapper.eq(UserPO::getUserId, po.getUserId());
        int cnt = baseMapper.update(null, lambdaUpdateWrapper);

        //2 更新用户详情基本信息缓存
        if (cnt > 0) {
            UserPO user = baseMapper.selectOne(Wrappers.<UserPO>lambdaQuery().eq(UserPO::getUserId, po.getUserId()));
            UserDTO cache = userRoleMenuRedisCache.getUserDetail(po.getUserId());
            //仅当有缓存时做更新缓存操作
            if (cache != null) {
                userRoleMenuRedisCache.setUserDetailCache(convert(user));
            }
        }
    }

    @Override
    public Set<FunctionPO> getUserFunctions(Long userId) {
        return userRoleMenuRedisCache.getFunction(userId);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean updateUserSelfDetail(UpdateUserSelfBO bo) {
        Assert.notNull(bo.getUserId(), "userId不能为空");
        //查询用户
        UserPO user = baseMapper.selectOne(Wrappers.<UserPO>lambdaQuery().eq(UserPO::getUserId, bo.getUserId()));

        Boolean userResult = true;

        if (Objects.isNull(user)) {
            log.error("未在系统中找到要修改的用户 userId =" + bo.getUserId());
            throw BusinessServiceException.getInstance("未在系统中找到要修改的用户,请联系管理员");
        }

        if (StringUtils.isNotBlank(bo.getAccount())) {
            //判断用户名是否存在
            UserPO userPOTmp = baseMapper
                    .selectOne(Wrappers.<UserPO>lambdaQuery().eq(UserPO::getAccount, bo.getAccount()));
            if (Objects.nonNull(userPOTmp) && !bo.getUserId().equals(userPOTmp.getUserId())) {
                throw BusinessServiceException.getInstance("登录账号已存在，请重新输入");
            }
            user.setAccount(bo.getAccount().trim());
            user.setUserName(bo.getUserName());
            userResult = this.updateById(user);
        }

        if (Objects.nonNull(bo.getStatus())) {
            user.setStatus(bo.getStatus());
            userResult = this.updateById(user);
        }

        //更新缓存
        if (userResult) {
            //刷新用户详情缓存
            this.refreshUserDetailCache(bo.getUserId());
        }
        return userResult;
    }

    @Override
    public boolean resetUserPassword(ResetUserPasswordBO bo) {
        Assert.notNull(bo.getPassword(), "新密码不能为空");
        //去除密码首尾空格
        String password = bo.getPassword().trim();
        LambdaQueryWrapper<UserPO> queryWrapper = Wrappers.lambdaQuery(UserPO.class);
        queryWrapper.eq(UserPO::getUserId, Long.valueOf(bo.getUserId()));

        UserPO admUserPO = this.getOne(queryWrapper);
        Assert.notNull(admUserPO, "当前用户不存在");
        admUserPO.setPassword(this.generatePwd(password));
        return this.updateById(admUserPO);
    }

    private String getDefaultPwd(String password) {
        String pwd;
        if (StringUtils.isNotEmpty(password)) {
            pwd = password;
        } else {
            pwd = Const.ADM_USER_DEFAULT_PWD;
        }

        return generatePwd(pwd);
    }

}
