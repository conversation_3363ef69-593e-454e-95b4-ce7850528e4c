Liquibase License Information
Version: 4.11.0

Liquibase ships with 3rd party components using the following licenses:

- Apache 2.0 License - https://opensource.org/licenses/Apache-2.0
- BSD 2-Clause License - https://opensource.org/licenses/BSD-2-Clause
- BSD 3-Clause License - https://opensource.org/licenses/BSD-3-Clause
- CDDL 1.1 License - https://opensource.org/licenses/cddl1.txt
- Eclipse Distribution 1.0 License - https://www.eclipse.org/org/documents/edl-v10.php
- Eclipse Public 1.0 License - https://opensource.org/licenses/epl-1.0
- Eclipse Public 2.0 License - https://opensource.org/licenses/epl-2.0
- GNU Affero General Public License version 3 https://opensource.org/licenses/AGPL-3.0
- IBM International Program License Agreement (IPLA) - https://www-40.ibm.com/software/sla/sladb.nsf/lilookup/1024954E51C94B03002587A4003CB520?OpenDocument
- LPGL 3.0 License - https://opensource.org/licenses/LGPL-3.0
- MIT License - https://opensource.org/licenses/MIT
- Oracle Free Use Terms and Conditions (FUTC) https://www.oracle.com/downloads/licenses/oracle-free-license.html

Libraries and their licenses:

Apache 2.0 License
- org.yaml:snakeyaml
- info.picocli:picocli
- org.xerial:sqlite-jdbc
- com.opencsv:opencsv
- commons-codec:commons-codec
- org.apache.commons:commons-lang3
- org.apache.commons:commons-text
- org.apache.commons:commons-collections4
- com.fasterxml.jackson.module:jackson-module-jaxb-annotations
- com.fasterxml.jackson.core:jackson-core
- com.fasterxml.jackson.core:jackson-databind
- com.fasterxml.jackson.core:jackson-annotations
- com.github.jsqlparser:jsqlparser (Dual licence: Apache 2.0, LGPL 2.1)

BSD 2 Clause License
- org.hsqldb:hsqldb
- org.postgresql:postgresql

BSD 3 Clause License
- org.firebirdsql.jdbc:jaybird (Dual License: LGPL, BSD-3 Clause)

CDDL 1.1 License
- javax.xml.bind:jaxb-api

Eclipse Distribution 1.0 License
- org.glassfish.jaxb/jaxb-core
- org.glassfish.jaxb/jaxb-runtime

Eclipse Public 1.0 License
- com.h2database:h2 (Dual license: MPL 2.0, EPL 1.0)

Eclipse Public 2.0 License
- Adoptium OpenJDK

IBM International Program License Agreement (IPLA)
- com.ibm.db2:jcc

GNU Affero General Public License (AGPL) version 3
- net.java.truelicense:truelicense

GNU Lesser General Public License (LGPL) 3.0
- org.mariadb.jdbc:mariadb-java-client (Dual license: LGPL 2.1, LGPL 3.0)

MIT License
- com.microsoft.sqlserver:mssql-jdbc

Oracle Free Use Terms and Conditions (FUTC)
- com.oracle.database.jdbc:ojdbc8
