package com.dl.dops.biz.common.forest.magicvideo.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @describe: AddTemplateAuthParam
 * @author: zhousx
 * @date: 2023/6/18 11:04
 */
@Data
public class AddTemplateAndAuthParam {
    @ApiModelProperty(value = "来源模板id", required = true)
    @NotBlank
    private String sourceTemplateId;

    @ApiModelProperty(value = "系统模板名称", required = true)
    @NotBlank
    private String name;

    @ApiModelProperty("系统模板封面")
    private String coverUrl;

    @ApiModelProperty("预览视频")
    private String previewVideoUrl;

    @ApiModelProperty(value = "授权租户列表", required = true)
    @NotEmpty
    private List<TenantInfoParam> tenants;

    @ApiModelProperty("是否包含理财经理信息 1 包含 0 不包含")
    private Integer isManager;

    @ApiModelProperty("一级分类 1.银行，2.证券，3.基金，4.理财子")
    private Integer firstCategory;

    @ApiModelProperty("二级分类")
    private Integer secondCategory;

    @ApiModelProperty("已存在的标签列表")
    List<Long> tagIds;

    @ApiModelProperty("新增的标签名")
    List<String> tagNames;
}
