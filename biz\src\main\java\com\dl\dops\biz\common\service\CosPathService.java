package com.dl.dops.biz.common.service;

import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialBO;
import com.dl.dops.resourcecenter.material.dto.RcMaterialDTO;
import com.dl.dops.resourcecenter.material.enums.RcMaterialTypeEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.function.Function;

@Service
public class CosPathService implements ObjectStoragePathService {

    @Value("${objectStorage.cos.resourcePrefix}")
    private String objectStorageResourcePrefix;

    @Override
    public String getObjectStorageResourcePrefix() {
        return objectStorageResourcePrefix;
    }

    public RcMaterialDTO postProcessObjectStorageMaterialDTO(RcMaterialDTO dto) {
        if (dto == null) {
            return null;
        }
        compositeObjectStoragePath(dto::getLogoImg, dto::setLogoImg);
        if (RcMaterialTypeEnum.isMaterialContentStoredInObjectStorage(dto.getMaterialType())) {
            compositeObjectStoragePath(dto::getContent, dto::setContent);
        }
        return dto;
    }

    public Function<MaterialBO, RcMaterialDTO> postProcessObjectStorageMaterialDTOFunction(
            Function<MaterialBO, RcMaterialDTO> original) {
        return original.andThen(this::postProcessObjectStorageMaterialDTO);
    }

}

