package com.dl.dops.biz.resourcecenter.manager.pack;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackChainPathPO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackChainPathBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackChainPathSaveBO;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 10:07
 */
public interface PackChainPathManager extends IService<PackChainPathPO> {

    /**
     * 保存
     *
     * @param
     * @return 链路id
     */
    List<PackChainPathBO> batchSave(Long packId, List<PackChainPathSaveBO> saveBOList);

    /**
     * 根据链路id查询
     *
     * @param chainPathId
     * @return
     */
    PackChainPathBO getByChainPathId(Long chainPathId);

    /**
     * 根据服务包id查询链路列表
     *
     * @param packId
     * @return
     */
    List<PackChainPathBO> listByPackId(Long packId);

    /**
     * 根据服务包id列表查询链路列表
     *
     * @param packIds
     * @return key-packId，value-链路列表
     */
    List<PackChainPathBO> listByPackIds(List<Long> packIds);

}
