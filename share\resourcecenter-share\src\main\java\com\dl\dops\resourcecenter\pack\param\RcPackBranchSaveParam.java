package com.dl.dops.resourcecenter.pack.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 16:12
 */
@Data
@ApiModel("服务包分支保存参数")
public class RcPackBranchSaveParam implements Serializable {

    private static final long serialVersionUID = -4035134016386635485L;
    @ApiModelProperty("分支id")
    private String branchId;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("排序")
    @NotNull(message = "分支排序不能为空")
    private Integer sort;

    @ApiModelProperty("服务包元素列表")
    private List<RcPackElementSaveParam> packElementList;
}
