package com.dl.dops.biz.system.manager.function.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.system.dal.function.FunctionMapper;
import com.dl.dops.biz.system.dal.function.po.FunctionPO;
import com.dl.dops.biz.system.manager.function.FunctionManager;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-06 16:38
 */
@Component
public class FunctionManagerImpl extends ServiceImpl<FunctionMapper, FunctionPO> implements FunctionManager {
}
