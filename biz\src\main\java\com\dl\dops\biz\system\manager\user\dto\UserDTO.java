package com.dl.dops.biz.system.manager.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class UserDTO {

    private static final long serialVersionUID = -5540085945257278467L;
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("token")
    private String token;

    @ApiModelProperty(hidden = true)
    private Integer isSuperAdm;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty(value = "状态", hidden = true)
    private Integer status;

    private String mobile;

    private Long createBy;

    private Long modifyBy;

}
