package com.dl.dops.biz.magicvideo.manager;

import cn.hutool.json.JSONUtil;
import com.dl.dops.biz.common.forest.magicvideo.MagicVideoClient;
import com.dl.dops.biz.common.forest.magicvideo.dto.SubjectMatterDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.SubjectMatterDetailDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.SubjectMatterEditDTO;
import com.dl.dops.biz.common.forest.magicvideo.param.XmindDTO;
import com.dl.dops.biz.common.forest.magicvideo.param.*;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-01 15:13
 */
@Component
public class MagicVideoSubjectMatterManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(MagicVideoSubjectMatterManager.class);

    @Resource
    private OperatorUtil operatorUtil;

    @Autowired
    private MagicVideoClient magicVideoClient;

    public Long save(SubjectMatterSaveParam param) {
        param.setOperatorId(operatorUtil.getOperator());
        param.setOperatorName(operatorUtil.getOperatorName());
        ResultModel<Long> resultModel = magicVideoClient.saveSubjectMatter(param);
        if (Objects.isNull(resultModel)) {
            LOGGER.error("保存表格题材失败，param:{}", JSONUtil.toJsonStr(param));
            throw BusinessServiceException.getInstance("保存表格题材失败");
        }
        if (!resultModel.isSuccess()) {
            LOGGER.error("保存表格题材失败，param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("保存表格题材失败,原因:" + resultModel.getMessage());
        }
        return resultModel.getDataResult();
    }

    public SubjectMatterDetailDTO detail(Long bizId) {
        ResultModel<SubjectMatterDetailDTO> resultModel = magicVideoClient.detailSubjectMatter(bizId);
        if (!resultModel.isSuccess()) {
            LOGGER.error("查询表格题材详情失败，param:{},,,resultModel:{}", bizId, JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询表格题材详情失败");
        }
        return resultModel.getDataResult();
    }

    public ResultPageModel<SubjectMatterDTO> page(SubjectMatterPageParam pageParam) {
        ResultPageModel<SubjectMatterDTO> resultPageModel = magicVideoClient.pageSubjectMatter(pageParam);
        if (!resultPageModel.isSuccess()) {
            LOGGER.error("分页查询题材详情失败，param:{},,,resultModel:{}", JSONUtil.toJsonStr(pageParam),
                    JSONUtil.toJsonStr(resultPageModel));
            throw BusinessServiceException.getInstance("分页查询题材详情失败");
        }
        return resultPageModel;
    }

    public void delete(Long bizId) {
        SubjectMatterDeleteParam param = new SubjectMatterDeleteParam();
        param.setBizId(bizId);
        param.setOperatorId(operatorUtil.getOperator());
        param.setOperatorName(operatorUtil.getOperatorName());

        ResultModel<Void> resultModel = magicVideoClient.deleteSubjectMatter(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("删除表格题材失败，param:{},,,resultModel:{}", bizId, JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("删除表格题材失败");
        }

    }

    public void deleteSubject(Long bizId) {
        SubjectDeleteParam param = new SubjectDeleteParam();
        param.setBizId(bizId);
        param.setOperatorId(operatorUtil.getOperator());
        param.setOperatorName(operatorUtil.getOperatorName());
        ResultModel<Void> resultModel = magicVideoClient.deleteSubject(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("删除题材失败，param:{},,,resultModel:{}", bizId, JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("删除题材失败");
        }
    }

    public ResultModel<List<SubjectMatterDTO>> listSons(Long parentId) {
        ResultModel<List<SubjectMatterDTO>> resultModel = magicVideoClient.listSons(parentId);
        if (!resultModel.isSuccess()) {
            LOGGER.error("查询子题材列表失败，parentId:{},,,resultModel:{}", parentId, JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询子题材列表失败");
        }
        return resultModel;
    }

    public Long add(SubjectAddDopsParam param) {

        String jsonStr = param.getXmindStr();
        XMindNewParam xMindNewParam = JSONUtil.toBean(jsonStr, XMindNewParam.class);
        XMindParam xMindParam = new XMindParam();
        XmindDTO root = xMindNewParam.getRoot();
        xMindParam.setName(root.getData().getText());
        xMindParam.setLevel(1);
        recursion(root, xMindParam);

        //转换
        SubjectAddParam subjectAddParam = cnvDopsParam2SubjectAddParam(param);
        subjectAddParam.setXmindStr(param.getXmindStr());
        subjectAddParam.setXmindParam(xMindParam);
        subjectAddParam.setOperatorId(operatorUtil.getOperator());
        subjectAddParam.setOperatorName(operatorUtil.getOperatorName());
        ResultModel<Long> resultModel = magicVideoClient.addSubjectMatter(subjectAddParam);
        if (Objects.isNull(resultModel)) {
            LOGGER.error("保存题材失败，param:{}", JSONUtil.toJsonStr(param));
            throw BusinessServiceException.getInstance("保存题材失败");
        }
        if (!resultModel.isSuccess()) {
            LOGGER.error("保存题材失败，param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("保存题材失败,原因:" + resultModel.getMessage());
        }
        return resultModel.getDataResult();
    }

    public SubjectMatterEditDTO edit(Long bizId) {
        ResultModel<SubjectMatterEditDTO> resultModel = magicVideoClient.editSubjectMatter(bizId);
        if (!resultModel.isSuccess()) {
            LOGGER.error("查询题材详情失败，param:{},,,resultModel:{}", bizId, JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询题材详情失败");
        }
        return resultModel.getDataResult();
    }

    public static void recursion(XmindDTO xmindDTO, XMindParam xMindParam) {
        if (CollectionUtils.isNotEmpty(xmindDTO.getChildren())) {
            xmindDTO.getChildren().forEach(xmindDTO1 -> {
                XMindParam xMindParam1 = new XMindParam();
                xMindParam1.setLevel(xMindParam.getLevel() + 1);
                xMindParam1.setName(xmindDTO1.getData().getText());
                xMindParam.getChildren().add(xMindParam1);
                recursion(xmindDTO1, xMindParam1);
            });
        }
    }

    private SubjectAddParam cnvDopsParam2SubjectAddParam(SubjectAddDopsParam param) {
        SubjectAddParam subjectAddParam = new SubjectAddParam();

        subjectAddParam.setBizId(param.getBizId());
        subjectAddParam.setTitle(param.getTitle());
        subjectAddParam.setName(param.getName());
        subjectAddParam.setIntro(param.getIntro());
        subjectAddParam.setImgUrl(param.getImgUrl());
        subjectAddParam.setXmindUrl(param.getXmindUrl());
        subjectAddParam.setParentId(param.getParentId());
        subjectAddParam.setPrompt(param.getPrompt());
        subjectAddParam.setOperatorName(param.getOperatorName());
        subjectAddParam.setOperatorId(param.getOperatorId());
        subjectAddParam.setMaterialList(param.getMaterialList());

        return subjectAddParam;
    }
}
