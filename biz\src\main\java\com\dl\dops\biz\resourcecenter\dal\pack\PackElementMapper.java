package com.dl.dops.biz.resourcecenter.dal.pack;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.dops.biz.common.annotation.BaseDao;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackElementPO;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 13:33
 */
@BaseDao
@DS("resourcecenter")
public interface PackElementMapper extends BaseMapper<PackElementPO> {

    void batchUpdate(List<PackElementPO> list);
}
