package com.dl.dops.resourcecenter.script.dto;

import com.dl.dops.resourcecenter.material.dto.RcMaterialDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @describe: CntScriptVO
 * @author: zhousx
 * @date: 2022/6/15 9:56
 */
@Data
public class RcScriptDTO implements Serializable {
    private static final long serialVersionUID = -3249904621916282843L;
    private String scriptId;

    private String question;

    private String content;

    private Date createTime;

    private RcScriptCategoryDTO category;

    private RcCreatorDTO creator;

    private List<RcMaterialDTO> materials;

    @ApiModelProperty("发布状态，1-已发布，2-取消发布")
    private Integer publishStatus;

    private String remark;
}
