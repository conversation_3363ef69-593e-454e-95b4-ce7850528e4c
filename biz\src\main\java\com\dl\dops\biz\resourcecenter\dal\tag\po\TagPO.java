package com.dl.dops.biz.resourcecenter.dal.tag.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

@Data
@TableName("tag")
public class TagPO extends BasePO {

    private static final long serialVersionUID = 4683475082240408894L;
    @TableId
    private Long id;

    @TableField("tag_id")
    private Long tagId;

    @TableField("name")
    private String name;

    @TableField("ordered")
    private Integer ordered;

    @TableField("tag_group_id")
    private Long tagGroupId;

    @TableField("is_deleted")
    private Integer isDelete;

}
