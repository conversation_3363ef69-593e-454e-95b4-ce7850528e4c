package com.dl.dops.biz.resourcecenter.manager.tag.impl;

import cn.easyes.common.utils.Assert;
import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.easyes.core.toolkit.EsWrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.resourcecenter.dal.tag.TagMapper;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagPO;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagRelaPO;
import com.dl.dops.biz.resourcecenter.es.tag.EsIndexRcTagMapper;
import com.dl.dops.biz.resourcecenter.es.tag.po.EsIndexRcTag;
import com.dl.dops.biz.resourcecenter.manager.tag.TagManager;
import com.dl.dops.biz.resourcecenter.manager.tag.TagRelaManager;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagAddSelfBO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagSelfBO;
import com.dl.dops.biz.resourcecenter.manager.tag.helper.TagHelper;
import com.dl.dops.resourcecenter.tag.dto.TagSearchRespDTO;
import com.dl.dops.resourcecenter.tag.param.TagRelaParam;
import com.dl.dops.resourcecenter.tag.param.TagSearchParam;
import com.dl.framework.common.idg.HostTimeIdg;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class TagManagerImpl extends ServiceImpl<TagMapper, TagPO> implements TagManager {

    @Autowired
    private HostTimeIdg hostTimeIdg;

    @Autowired
    private TagRelaManager tagRelaManager;

    @Autowired
    private EsIndexRcTagMapper esIndexRcTagMapper;

    @Override
    public String add(TagAddSelfBO tagAddSelfBO) {
        LambdaQueryWrapper<TagPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagPO::getTagGroupId, Long.valueOf(tagAddSelfBO.getTagGroupId()))
                .eq(TagPO::getIsDelete, Const.ZERO);
        List<TagPO> poList = this.list(queryWrapper);
        Assert.isTrue(poList.size() + tagAddSelfBO.getTagSelfBOS().size() <= 20, "一个标签组最多能有20个标签");
        List<TagPO> tagPOList = new ArrayList<>();
        tagAddSelfBO.getTagSelfBOS().forEach(item -> {
            TagPO tagPO = new TagPO();
            tagPO.setTagId(hostTimeIdg.generateId().longValue());
            tagPO.setName(item.getName());
            tagPO.setOrdered(item.getOrder());
            tagPO.setTagGroupId(Long.valueOf(tagAddSelfBO.getTagGroupId()));
            tagPO.setIsDelete(Const.ZERO);
            tagPOList.add(tagPO);
        });
        if (CollectionUtils.isEmpty(tagPOList)){
            return null;
        }
        this.saveBatch(tagPOList);
        return tagAddSelfBO.getTagGroupId();
    }

    @Override
    public String edit(TagSelfBO selfBO) {
        LambdaQueryWrapper<TagPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagPO::getTagId,Long.valueOf(selfBO.getTagId()))
                .eq(TagPO::getIsDelete,Const.ZERO);
        TagPO tagPO = this.getOne(queryWrapper);
        tagPO.setOrdered(selfBO.getOrder());
        tagPO.setName(selfBO.getName());
        this.updateById(tagPO);
        return tagPO.getTagId().toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String delete(TagSelfBO selfBO) {
        LambdaQueryWrapper<TagPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagPO::getTagId, Long.valueOf(selfBO.getTagId())).eq(TagPO::getIsDelete, Const.ZERO);
        TagPO tagPO = this.getOne(queryWrapper);
        tagPO.setIsDelete(Const.ONE);
        this.updateById(tagPO);

        //删除关联关系
        LambdaQueryWrapper<TagRelaPO> relaQueryWrapper = new LambdaQueryWrapper<>();
        relaQueryWrapper.eq(TagRelaPO::getTagId, tagPO.getTagId()).eq(TagRelaPO::getIsDelete, Const.ZERO);
        List<TagRelaPO> tagRelaPOList = tagRelaManager.list(relaQueryWrapper);
        if (CollectionUtils.isEmpty(tagRelaPOList)) {
            return tagPO.getTagId().toString();
        }

        tagRelaPOList.forEach(item -> {
            item.setIsDelete(Const.ONE);
        });
        tagRelaManager.updateBatchById(tagRelaPOList);
        tagRelaManager.batchUpdateBizModifyDt(tagRelaPOList.get(0).getType(),
                tagRelaPOList.stream().map(TagRelaPO::getBizId).collect(Collectors.toList()));

        return tagPO.getTagId().toString();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void batchMarkTag(TagRelaParam bo) {
        List<Long> bizIds = bo.getBizIds().stream().map(item -> Long.valueOf(item)).collect(Collectors.toList());
        List<Long> tagIds = bo.getTagIds().stream().map(item -> Long.valueOf(item)).collect(Collectors.toList());
        //需要添加的关联关系
        List<TagRelaPO> needAddTagRela = new ArrayList<>();
        LambdaQueryWrapper<TagRelaPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TagRelaPO::getBizId,bizIds)
                .eq(TagRelaPO::getIsDelete,Const.ZERO);
        //已有的关联关系
        List<TagRelaPO> tagRelaPOList = tagRelaManager.list(queryWrapper);
        //如果为空则全部新增
        if (CollectionUtils.isEmpty(tagRelaPOList)){
            bizIds.forEach(item->{
                tagIds.forEach(x->{
                    TagRelaPO tagRelaPO = new TagRelaPO();
                    tagRelaPO.setBizId(item);
                    tagRelaPO.setTagId(x);
                    tagRelaPO.setType(bo.getType());
                    tagRelaPO.setIsDelete(Const.ZERO);
                    needAddTagRela.add(tagRelaPO);
                });
            });
            tagRelaManager.saveBatch(needAddTagRela);
            tagRelaManager.batchUpdateBizModifyDt(bo.getType(), bizIds);
            return;
        }
        //拼接请求的bizId+tagId
        List<String> bizTagIds = new ArrayList<>();
        bizIds.forEach(item->{
            tagIds.forEach(x->{
                bizTagIds.add(item + "-" + x);
            });
        });
        //已存在拼接bizId+tagId
        List<String> oldBizTagIds = new ArrayList<>();
        tagRelaPOList.forEach(item->{
            oldBizTagIds.add(item.getBizId()+ "-" + item.getTagId());
        });
        //找出需要添加的关联关系
        bizTagIds.forEach(item->{
            if (!oldBizTagIds.contains(item)){
                TagRelaPO tagRelaPO = new TagRelaPO();
                String bizId = item.substring(0,item.indexOf("-"));
                Long tagId = Long.valueOf(item.substring(bizId.length()+1),item.length());
                tagRelaPO.setBizId(Long.valueOf(bizId));
                tagRelaPO.setTagId(tagId);
                tagRelaPO.setType(bo.getType());
                needAddTagRela.add(tagRelaPO);
            }
        });
        if (CollectionUtils.isNotEmpty(needAddTagRela)) {
            tagRelaManager.saveBatch(needAddTagRela);

            tagRelaManager.batchUpdateBizModifyDt(bo.getType(),
                    needAddTagRela.stream().map(TagRelaPO::getBizId).collect(Collectors.toList()));
        }
        return;
    }

    @Override
    public List<TagSearchRespDTO> search(TagSearchParam param) {
        LambdaEsQueryWrapper<EsIndexRcTag> wrapper = EsWrappers.lambdaQuery(EsIndexRcTag.class);
        wrapper.eq(Objects.nonNull(param.getType()), EsIndexRcTag::getGroupType, param.getType())
                .like(StringUtil.isNotBlank(param.getTagName()), "name", param.getTagName())
                .orderByAsc(EsIndexRcTag::getOrdered);

        List<EsIndexRcTag> resultList = esIndexRcTagMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        return resultList.stream().map(TagHelper::cnvTagDetailPO2RespDTO).collect(Collectors.toList());
    }
}
