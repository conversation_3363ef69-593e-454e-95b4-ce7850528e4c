<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.dops.biz.resourcecenter.dal.productinstitute.ProductIssueInstituteMapper">

    <resultMap id="BaseResultMap" type="com.dl.dops.biz.resourcecenter.dal.productinstitute.po.ProductIssueInstitutePO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="instituteId" column="institute_id" jdbcType="BIGINT"/>
            <result property="extCode" column="ext_code" jdbcType="VARCHAR"/>
            <result property="fullName" column="full_name" jdbcType="VARCHAR"/>
            <result property="abbrName" column="abbr_name" jdbcType="VARCHAR"/>
            <result property="logoIconUrl" column="logo_icon_url" jdbcType="VARCHAR"/>
            <result property="logoHighResUrl" column="logo_high_res_url" jdbcType="VARCHAR"/>
            <result property="createDt" column="create_dt" jdbcType="TIMESTAMP"/>
            <result property="modifyDt" column="modify_dt" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="modifyBy" column="modify_by" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,institute_id,ext_code,
        full_name,abbr_name,logo_icon_url,
        logo_high_res_url,create_dt,modify_dt,
        create_by,modify_by
    </sql>
</mapper>
