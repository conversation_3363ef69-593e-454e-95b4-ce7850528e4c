package com.dl.dops.biz.system.manager.user.enums;

import java.util.Objects;

public enum SysUserStatusEnum {

    DEFAULT(0, "-"),
    REG(1, "正常"),
    FORBID(2, "已禁用"),
    UN_REG(4, "未激活");
    //QUIT(5, "退出企业");

    private Integer code;

    private String desc;

    SysUserStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SysUserStatusEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return SysUserStatusEnum.DEFAULT;
        }
        for (SysUserStatusEnum e : SysUserStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return SysUserStatusEnum.DEFAULT;
    }
}
