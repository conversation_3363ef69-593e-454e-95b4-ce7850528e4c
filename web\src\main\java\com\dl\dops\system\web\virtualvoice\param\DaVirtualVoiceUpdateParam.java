package com.dl.dops.system.web.virtualvoice.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
public class DaVirtualVoiceUpdateParam implements Serializable {

    private static final long serialVersionUID = 3435015445940055700L;

    @ApiModelProperty(value = "数字声音唯一标识")
    @NotEmpty(message = "数字声音唯一标识必填")
    private String bizId;

    @ApiModelProperty("启用状态： 0 否 ；1 启用")
    private Integer enableState;
}