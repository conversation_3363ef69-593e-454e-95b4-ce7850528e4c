package com.dl.dops.biz.resourcecenter.manager.rs.dto;

import com.dl.dops.resourcecenter.sop.enums.RcSopEventRuleTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-10-21 15:33
 */
@Data
public class RsSopEventDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long eventId;

    private Long sopId;

    private String content;

    private Integer reachType;

    /**
     * @see RcSopEventRuleTypeEnum
     */
    private Integer ruleType;

    /**
     * 规则内容，定时推送时为天-HH:mm:ss  周期推送为首次天-天-HH:mm:ss
     */
    private String ruleContent;

    private String name;

    private String remark;

    /**
     * 有效期
     */
    private Integer validityPeriod;

    /**
     * 事件内容列表
     */
    private List<RsSopEventCntDTO> eventCntList;
}
