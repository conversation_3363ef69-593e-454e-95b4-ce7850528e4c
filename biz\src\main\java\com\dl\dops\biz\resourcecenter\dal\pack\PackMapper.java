package com.dl.dops.biz.resourcecenter.dal.pack;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.dops.biz.common.annotation.BaseDao;
import com.dl.dops.biz.resourcecenter.dal.pack.param.PackQuery;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackPO;

import java.util.List;

@BaseDao
@DS("resourcecenter")
public interface PackMapper extends BaseMapper<PackPO> {
    List<PackPO> pageQuery(PackQuery bo);

    Integer count(PackQuery bo);
}
