package com.dl.dops.biz.common.forest.magicvideo.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-31 14:35
 */
@Data
public class SubjectMatterSaveParam {

    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 题材名称
     */
    @NotBlank(message = "题材名称不能为空")
    private String name;

    /**
     * 图片链接地址
     */
    @NotBlank(message = "图片链接地址不能为空")
    private String imgUrl;

    /**
     * excel链接地址
     */
    @NotBlank(message = "excel链接地址不能为空")
    private String excelUrl;

    /**
     * 题材级别，1-一级题材，2-二级题材
     */
    @NotNull(message = "题材级别不能为空")
    private Integer level;

    /**
     * 父级题材bizId
     */
    private Long parentId;

    /**
     * json文件地址
     */
    private String jsonUrl;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 股票参数列表
     */
    private List<SubjectMatterStockParam> stockParams;
}
