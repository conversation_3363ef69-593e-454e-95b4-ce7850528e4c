package com.dl.dops.biz.system.dal.user.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-01 10:41
 */
@Data
@TableName("sys_user")
public class UserPO extends BasePO {
    private static final long serialVersionUID = 1L;

    @TableId("id")
    public Long id;

    /**
     * userid
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 登录账号
     */
    @TableField("account")
    private String account;

    private String userName;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 账号状态
     * 0-启用
     * 1-锁定
     * 2-禁用
     * <p>
     * ADM_USER_STATUS
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否超管
     * 1-是
     * 0-否
     */
    @TableField("is_super_admin")
    private Integer isSuperAdm;

    private Integer isDeleted;

}
