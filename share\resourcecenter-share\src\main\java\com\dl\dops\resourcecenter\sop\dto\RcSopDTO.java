package com.dl.dops.resourcecenter.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-10-09 09:44
 */
@Data
@ApiModel("资源中心sop模板DTO")
public class RcSopDTO {

    @ApiModelProperty("sopId")
    private String sopId;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("sop类型,1个人sop 2群sop")
    private Integer sopType;

    @ApiModelProperty("所属一级分类id")
    private String category1;

    @ApiModelProperty("所属二级分类id")
    private String category2;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人名称")
    private String creatorName;

    @ApiModelProperty("一级分类名称")
    private String category1Name;

    @ApiModelProperty("二级分类名称")
    private String category2Name;

    @ApiModelProperty("sop目标规则描述")
    private String targetConfDesc;
}
