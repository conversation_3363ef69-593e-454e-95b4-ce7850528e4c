package com.dl.dops.web.common.controllers.storepolicy;

import com.dl.dops.biz.common.manager.storepolicy.StorePolicyManager;
import com.dl.dops.biz.common.manager.storepolicy.dto.BaseTempCredentialDTO;
import com.dl.dops.biz.common.tencentcloud.cos.GetTempCredentialBO;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @ClassName StoragePolicyController
 * @Description
 * <AUTHOR>
 * @Date 2022/10/9 18:47
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/dops/store/policy")
public class StorePolicyController {

    @Resource
    private StorePolicyManager storePolicyManager;

    @PostMapping("/sts/get")
    @ApiOperation("获取临时访问资质")
    public ResultModel<BaseTempCredentialDTO> stsGet(@RequestBody @Validated GetTempCredentialBO param) {
        return ResultModel.success(storePolicyManager.getTempCredential(param));
    }
}
