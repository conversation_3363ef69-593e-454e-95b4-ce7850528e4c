package com.dl.dops.resourcecenter.script.enums;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-30 18:02
 */
public enum RcScriptPublishStatusEnum {

    RELEASED(1, "已发布"),
    CANCEL_RELEASED(2, "取消发布");

    private Integer status;

    private String desc;

    RcScriptPublishStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
