package com.dl.dops.biz.common.forest.magicvideo;

import com.dl.dops.biz.common.forest.magicvideo.dto.*;
import com.dl.dops.biz.common.forest.magicvideo.param.*;
import com.dl.dops.magicvideo.deliveryplan.dto.DeliveryPlanDTO;
import com.dl.dops.magicvideo.deliveryplan.dto.VisualTemplateInternalDTO;
import com.dl.dops.magicvideo.deliveryplan.param.AddDeliveryPlanBO;
import com.dl.dops.magicvideo.deliveryplan.param.DeliveryPlanPageQueryBO;
import com.dl.dops.magicvideo.deliveryplan.param.ProduceBatchPageQueryBO;
import com.dl.dops.magicvideo.deliveryplan.param.TemplateInternalPageQueryBO;
import com.dl.dops.magicvideo.deliveryplan.param.UpdateDeliveryPlanBO;
import com.dl.dops.magicvideo.font.dto.PatternFontDTO;
import com.dl.dops.magicvideo.font.param.PatternFontPageParam;
import com.dl.dops.magicvideo.font.param.PatternFontParam;
import com.dl.dops.magicvideo.music.bo.BackgroundMusicBO;
import com.dl.dops.magicvideo.music.bo.BackgroundMusicPageBO;
import com.dl.dops.magicvideo.music.dto.BackGroundMusicInternalDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.DataParam;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;
import com.dtflys.forest.annotation.Var;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@BaseRequest(interceptor = MagicVideoInterceptor.class)
public interface MagicVideoClient {

    /**
     * 添加花字
     * @param param
     * @return
     */
    @Post("/patternfont/add")
    ResultModel<String> addPatternFont(@JSONBody PatternFontParam param);

    /**
     * 编辑花字
     * @param param
     * @return
     */
    @Post("/patternfont/update")
    ResultModel<Boolean> updatePatternFont(@JSONBody PatternFontParam param);

    /**
     * 分页查询花字
     * @param param
     * @return
     */
    @Post("/patternfont/page")
    ResultPageModel<PatternFontDTO> pageQueryPatternFont(@JSONBody PatternFontPageParam param);

    /**
     * 花字详情
     * @param param
     * @return
     */
    @Post("/patternfont/detail")
    ResultModel<PatternFontDTO> patternFontDetail(@JSONBody PatternFontParam param);

    @Post("/patternfont/delete")
    ResultModel<Boolean> deletePatternFont(@JSONBody PatternFontParam param);

    /**
     * 添加背景音乐
     * @param param
     * @return
     */
    @Post("/background/music/add")
    ResultModel<String> addBackgroundMusic(@JSONBody BackgroundMusicBO param);

    @Post("/deliveryplan/add")
    ResultModel<Void> addDeliveryPlan(@JSONBody AddDeliveryPlanBO param);

    @Post("/deliveryplan/update")
    ResultModel<Void> updateDeliveryPlan(@JSONBody UpdateDeliveryPlanBO param);

    @Post("/deliveryplan/list")
    ResultPageModel<DeliveryPlanDTO> listDeliveryPlan(@JSONBody DeliveryPlanPageQueryBO param);

    @Post("/deliveryplan/templatelist")
    ResultPageModel<VisualTemplateInternalDTO> templateList(@JSONBody TemplateInternalPageQueryBO param);

    /**
     * 分页查询背景音乐
     * @param param
     * @return
     */
    @Post("/background/music/page")
    ResultPageModel<BackGroundMusicInternalDTO> pageQueryBackgroundMusic(@JSONBody BackgroundMusicPageBO param);

    /**
     * 修改背景音乐
     * @param param
     * @return
     */
    @Post("/background/music/update")
    ResultModel<Boolean> updateBackgroundMusic(@JSONBody BackgroundMusicBO param);

    /**
     * 删除背景音乐
     * @param param
     * @return
     */
    @Post("/background/music/delete")
    ResultModel<Boolean> delBackgroundMusic(@JSONBody BackgroundMusicBO param);

    /**
     * 批次列表
     * @param param
     * @return
     */
    @Post("/produce/batchlist")
    ResultPageModel<ProduceBatchDTO> batchList(@JSONBody ProduceBatchPageQueryBO param);

    /**
     * 批次下作业列表
     * @param param
     * @return
     */
    @Post("/produce/joblistbybatchId")
    ResultPageModel<ProduceJobDTO> jobListByBatchId(@JSONBody ProduceJobPageQueryParam param);

    /**
     * 取消批次
     * @param batchId
     * @return
     */
    @Post("/produce/cancelbatch/{batchId}")
    ResultModel<Void> cancelBatch(@Var("batchId") Long batchId);

    /**
     * 作业详情
     * @param jobId
     * @return
     */
    @Post("/produce/jobdetail/{jobId}")
    ResultModel<ProduceJobDTO> jobDetail(@Var("jobId") Long jobId);

    /**
     * 当日生产统计
     * @return
     */
    @Post("/produce/dailystatistics")
    ResultModel<DailyProduceStatisticsDTO> dailyStatistics();

    /**
     * 删除作品
     * @param jobId
     * @return
     */
    @Post("/produce/deleteworks/{jobId}")
    ResultModel<Void> deleteWorks(@DataParam("jobId") Long jobId);

    /**
     * 生产数量统计
     * @param param
     * @return
     */
    @Post("/statistics/count")
    ResultModel<List<CountVO>> count(@JSONBody CountParam param);

    /**
     * 生产效率统计
     * @param param
     * @return
     */
    @Post("/statistics/efficiency")
    ResultModel<List<EfficiencyVO>> efficiency(@JSONBody EfficiencyParam param);

    /**
     * 系统模板列表
     * @param param
     * @return
     */
    @Post("/templateauth/list")
    ResultPageModel<TemplateAuthDTO> list(@JSONBody TemplateAuthPageQueryParam param);

    /**
     * 创建系统模板并授权
     * @param param
     * @return
     */
    @Post("/templateauth/addandauth")
    ResultModel<TemplateAuthDTO> addAndAuth(@JSONBody AddTemplateAndAuthParam param);

    /**
     * 修改授权
     * @param param
     * @return
     */
    @Post("/templateauth/auth")
    ResultModel<Void> auth(@JSONBody AuthParam param);

    /**
     * 系统模板启停
     *
     * @param param
     * @return
     */
    @Post("/templateauth/switchstatus")
    ResultModel<Void> switchStatus(@JSONBody SwitchStatusParam param);

    /**
     * 修改系统模板的来源模板
     *
     * @param param
     * @return
     */
    @Post("/templateauth/changesourcetemplate")
    ResultModel<Void> changeSourceTemplate(@JSONBody ChangeSourceTemplateParam param);

    /**
     * 标签详情
     *
     * @param param
     * @return
     */
    @Post("/template/tagList")
    ResultPageModel<TagVO> tagList(@JSONBody TagPageQueryParam param);

    /**
     * 模板详情
     *
     * @param param
     * @return
     */
    @Post("/template/detail")
    ResultModel<VisualTemplateInternalVO> detail(@JSONBody DetailParam param);

    /**
     * 删除模板
     *
     * @param templateId
     * @return
     */
    @Get("/template/delete/{templateId}")
    ResultModel<Void> delete(@DataParam("templateId") Long templateId);

    /**
     * 模板列表
     *
     * @param param
     * @return
     */
    @Post("/template/list")
    ResultPageModel<VisualTemplateInternalVO> list(@JSONBody TemplatePageQueryParam param);

    /**
     * 新增贴图
     * @param param
     * @return
     */
    @Post("/chartlet/add")
    ResultModel<Long> addChartlet(@JSONBody InternalChartletInfoAddParam param);

    /**
     * 修改贴图
     * @param param
     * @return
     */
    @Post("/chartlet/update")
    ResultModel<Void> updateChartlet(@JSONBody InternalChartletInfoUptParam param);

    /**
     * 分页查询贴图信息
     * @param param
     * @return
     */
    @Post("/chartlet/page")
    ResultPageModel<InternalChartletInfoDTO> pageChartlet(@JSONBody InternalChartletPageParam param);

    /**
     * 删除贴图
     *
     * @param param
     * @return
     */
    @Post("/chartlet/delete")
    ResultModel<Void> deleteChartlet(@JSONBody InternalChareletDeleteParam param);

    /**
     * 获取全部贴图分类
     *
     * @return
     */
    @Post("/chartlet/listallcategory")
    ResultModel<List<InternalChartletCategoryDTO>> listAllCategory();

    /**
     * 分页查询ai任务数据统计
     *
     * @param param
     * @return
     */
    @Post("/statistics/aijob/page")
    ResultPageModel<StatisticsAiJobDTO> pageAiJobStatistics(@JSONBody StatisticsAiJobPageParam param);

    /**
     * 查询前几名租户的ai任务数据统计
     *
     * @param param
     * @return
     */
    @Post("/statistics/aijob/querytoptenantstatistics")
    ResultModel<List<StatisticsAiJobTenantDTO>> queryTopTenantStatistics(
            @JSONBody StatisticsAiJobTopTenantQueryParam param);

    /**
     * 分页查询数字人任务信息
     *
     * @param param
     * @return
     */
    @Post("/aijob/pagedmjob")
    ResultPageModel<VisualDmJobInfoDTO> pageDmJob(@JSONBody VisualAiJobPageQueryParam param);

    /**
     * 生产失败数据统计
     *
     * @param param
     * @return
     */
    @Post("/statistics/failstatistics")
    ResultModel<List<FailJobStatisticsDTO>> failStatistics(@JSONBody FailJobStatisticsParam param);

    /**
     * 题材保存
     *
     * @param param
     * @return
     */
    @Post("/subjectmatter/save")
    ResultModel<Long> saveSubjectMatter(@JSONBody SubjectMatterSaveParam param);

    /**
     * 题材详情
     *
     * @param bizId
     * @return
     */
    @Get("/subjectmatter/detail")
    ResultModel<SubjectMatterDetailDTO> detailSubjectMatter(@Query("bizId") Long bizId);

    /**
     * 题材分页
     *
     * @param pageParam
     * @return
     */
    @Post("/subjectmatter/page")
    ResultPageModel<SubjectMatterDTO> pageSubjectMatter(@JSONBody SubjectMatterPageParam pageParam);

    /**
     * 图表题材删除
     *
     * @param param
     * @return
     */
    @Post("/subjectmatter/delete")
    ResultModel<Void> deleteSubjectMatter(@JSONBody SubjectMatterDeleteParam param);

    /**
     * @param parentId
     * @return
     */
    @Get("/subjectmatter/listsons")
    ResultModel<List<SubjectMatterDTO>> listSons(@Query("parentId") Long parentId);

    /**
     * 分页查询产品
     *
     * @param param
     * @return
     */
    @Post("/dict/pageassetprodinfo")
    ResultPageModel<AssetProdInfoVO> pageAssetProdInfo(@JSONBody AssetProdInfoPageParam param);

    /**
     * 查询产品列表
     *
     * @param param
     * @return
     */
    @Post("/dict/listassetprodinfo")
    ResultModel<List<AssetProdInfoVO>> listAssetProdInfo(@JSONBody AssetProdInfoListParam param);

    /**
     * 租户模板列表
     * @param param
     * @return
     */
    @Post("/tenanttemplateauth/list")
    ResultPageModel<TenantTemplateAuthDTO> list(@JSONBody TenantTemplateAuthPageQueryParam param);

    /**
     * 修改授权
     *
     * @param param
     * @return
     */
    @Post("/tenanttemplateauth/auth")
    ResultModel<Void> auth(@JSONBody TenantAuthParam param);

    /**
     * 创建租户试用账户
     *
     * @param param
     * @return
     */
    @Post("/accounttenanttrial/createaccount")
    ResultModel<Void> createTrialAccount(@JSONBody CreateAccountParam param);

    @Post("/subjectmatter/add")
    ResultModel<Long> addSubjectMatter(@JSONBody SubjectAddParam param);

    /**
     * 题材删除
     * @param param
     * @return
     */
    @Post("/subjectmatter/deletesubject")
    ResultModel<Void> deleteSubject(@JSONBody SubjectDeleteParam param);

    /**
     * 编辑题材
     *
     * @param bizId
     * @return
     */
    @Get("/subjectmatter/edit")
    ResultModel<SubjectMatterEditDTO> editSubjectMatter(@Query("bizId") Long bizId);

    /**
     * 指定租户的ai任务数据统计汇总
     *
     * @param param
     * @return
     */
    @Post("/statistics/aijob/specifictenantsummary")
    ResultModel<StatisticsAiJobTenantSummaryDTO> specificTenantSummary(
            @JSONBody StatisticsAiJobTenantSummaryQueryParamDTO param);

    /**
     * 分页查询TTS任务信息
     *
     * @param param
     * @return
     */
    @Post("/aijob/pagettsjob")
    ResultPageModel<VisualTtsJobInfoDTO> pageTtsJob(@JSONBody VisualAiJobPageQueryParam param);

    /**
     * 分页查询作品
     *
     * @param param
     * @return
     */
    @Post("/producejob/page")
    ResultPageModel<VisualProduceJobPageDTO> pageJob(@JSONBody VisualProduceJobInternalPageParamDTO param);

    /**
     * 指定租户的任务数据统计汇总
     *
     * @param param
     * @return
     */
    @Post("/statistics/specifictenantsummary")
    ResultModel<StatisticsJobTenantSummaryDTO> specificTenantSummary(
            @JSONBody StatisticsJobTenantSummaryQueryParam param);

    /**
     * 长图列表
     * @param param
     * @return
     */
    @Post("/dx/list")
    ResultPageModel<DxLongImageListDTO> dxLongImageList(@JSONBody DxLongImageListParam param);

    /**
     * 添加长图
     * @param param
     * @return
     */
    @Post("/dx/add")
    ResultModel<Void> dxLongImageAdd(@JSONBody DxLongImageAddParam param);

    /**
     * 长图详情
     * @param param
     * @return
     */
    @Post("/dx/detail")
    ResultModel<DxLongImageDetailDTO> dxLongImageDetail(@JSONBody DxLongImageDetailParam param);

    /**
     * 添加长图
     * @param param
     * @return
     */
    @Post("/dx/update")
    ResultModel<Void> dxLongImageUpdate(@JSONBody DxLongImageUpdateParam param);
}
