package com.dl.dops.biz.common.forest.magicvideo.dto;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-08-23 16:08
 */
@Data
public class VisualProduceJobInternalPageParamDTO extends AbstractPageParam {

    @ApiModelProperty(value = "租户编码")
    private String tenantCode;

    @ApiModelProperty(value = "状态列表,2-合成成功")
    private List<Integer> statusList;

    @ApiModelProperty(value = "开始时间，时间戳(毫秒)")
    private Long startTime;

    @ApiModelProperty(value = "结束时间，时间戳（毫秒）")
    private Long endTime;

    @ApiModelProperty(value = "是否包含删除的作品 0-不包含，1-包含")
    private Integer includeDeleted = 1;

}
