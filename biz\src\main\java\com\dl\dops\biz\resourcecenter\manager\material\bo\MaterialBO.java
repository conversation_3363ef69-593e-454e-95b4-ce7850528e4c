package com.dl.dops.biz.resourcecenter.manager.material.bo;

import com.dl.dops.biz.common.BaseBO;
import com.dl.dops.resourcecenter.tag.dto.TagInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("素材")
public class MaterialBO extends BaseBO {
    private static final long serialVersionUID = -2721822983645980352L;
    @ApiModelProperty("素材id")
    private String materialId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("摘要")
    private String remark;

    @ApiModelProperty("logo图地址")
    private String logoImg;

    @ApiModelProperty("分类Ids")
    private String categoryIds;

    @ApiModelProperty("素材大小")
    private Integer size;

    @ApiModelProperty("关联的标签")
    private List<TagInfoDTO> tagInfoDTOList;

    @ApiModelProperty("素材类型")
    private Integer materialType;

    private Integer articleType;

    @ApiModelProperty("素材内容")
    private String content;

    @ApiModelProperty("创建人")
    private String creator;

    private Long createBy;

    private Long modifyBy;

    @ApiModelProperty("微信公众号文章地址")
    private String mpArticleSourceUrl;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("发布状态，1-已发布，2-取消发布")
    private Integer publishStatus;

    private String modifyName;
}
