package com.dl.dops.resourcecenter.web.controllers.tag;

import com.dl.dops.biz.resourcecenter.manager.tag.TagManager;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagAddSelfBO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagSelfBO;
import com.dl.dops.resourcecenter.tag.dto.TagSearchRespDTO;
import com.dl.dops.resourcecenter.tag.param.TagRelaParam;
import com.dl.dops.resourcecenter.tag.param.TagSearchParam;
import com.dl.dops.resourcecenter.tag.param.TagSelfAddParam;
import com.dl.dops.resourcecenter.tag.param.TagSelfParam;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;
@Slf4j
@RestController
@RequestMapping("/dops/tag")
public class TagController extends AbstractController{

    @Autowired
    private TagManager tagManager;

    @Autowired
    private RedissonClient redissonClient;


    private static final String BATCH_TAG_REAL_LOCK_KEY = "dl-batch-tag-real:";

    @PostMapping("/add")
    @ApiOperation("新增标签")
    public ResultModel<String> add(@RequestBody TagSelfAddParam param){
        TagAddSelfBO tagAddSelfBO = new TagAddSelfBO();
        tagAddSelfBO.setTagGroupId(param.getTagGroupId());
        List<TagSelfBO> tagSelfBOList = param.getTagSelfParams().stream().map(item -> {
            TagSelfBO tagSelfBO = new TagSelfBO();
            tagSelfBO.setName(item.getName());
            tagSelfBO.setOrder(item.getOrder());
            return tagSelfBO;
        }).collect(Collectors.toList());
        tagAddSelfBO.setTagSelfBOS(tagSelfBOList);
        return ResultModel.success(tagManager.add(tagAddSelfBO));
    }

    @PostMapping("/edit")
    @ApiOperation("修改标签")
    public ResultModel<String> edit(@RequestBody TagSelfParam param){
        TagSelfBO selfBO = new TagSelfBO();
        selfBO.setTagId(param.getTagId());
        selfBO.setTagGroupId(param.getTagGroupId());
        selfBO.setOrder(param.getOrder());
        selfBO.setName(param.getName());
        return ResultModel.success(tagManager.edit(selfBO));
    }

    @PostMapping("/delete")
    @ApiOperation("删除标签")
    public ResultModel<String> delete(@RequestBody TagSelfParam param) {
        TagSelfBO selfBO = new TagSelfBO();
        selfBO.setTagId(param.getTagId());
        return ResultModel.success(tagManager.delete(selfBO));
    }

    @PostMapping("/batchmarktag")
    @ApiOperation("批量打标签")
    public ResultModel<Void> batchMarkTag(@RequestBody TagRelaParam tagRelaParam){
        String lockKey = getLockKey(tagRelaParam.getType());
        RLock lock = redissonClient.getLock(lockKey);
        try {
            Boolean flag = lock.tryLock();
            if (!flag) {
                log.error("批量打标签获取锁失败！lockKey={}", lockKey);
                throw BusinessServiceException.getInstance("正在操作中，请勿重复提交");
            }
            tagManager.batchMarkTag(tagRelaParam);
            return ResultModel.success(null);
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    @PostMapping("/search")
    @ApiOperation("搜索标签")
    public ResultModel<List<TagSearchRespDTO>> search(@RequestBody @Validated TagSearchParam param) {
        return ResultModel.success(tagManager.search(param));
    }

    private String getLockKey(Integer type){
        return BATCH_TAG_REAL_LOCK_KEY+type;
    }

}
