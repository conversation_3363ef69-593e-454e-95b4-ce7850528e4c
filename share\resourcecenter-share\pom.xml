<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dl.dops.web</groupId>
        <artifactId>dl-dops-web-share</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.dl.dops.web</groupId>
    <artifactId>dl-resourcecenter-share</artifactId>
    <version>1.0.2-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>dl-resourcecenter-web-share</name>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <!--easyexcel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>deeplinktech-wealthpartner-maven</id>
            <name>maven</name>
            <url>https://deeplinktech-maven.pkg.coding.net/repository/wealthpartner/maven/</url>
        </repository>
    </distributionManagement>
</project>