package com.dl.dops.resourcecenter.material.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RcArticleSpiderDTO implements Serializable {

    private static final long serialVersionUID = 6129854867246164986L;
    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("封面图地址")
    private String coverImg;

    @ApiModelProperty("素材内容")
    private String content;

    @ApiModelProperty("摘要内容")
    private String summary;
}
