package com.dl.dops.resourcecenter.web.controllers.pack.convert;

import cn.hutool.json.JSONUtil;
import com.dl.dops.biz.common.enums.SymbolE;
import com.dl.dops.biz.resourcecenter.es.pack.po.EsIndexRcPack;
import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackAddBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackBranchBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackChainPathBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackChainPathSaveBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackElementAttachmentBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackElementBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackElementSaveBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackUpdateBO;
import com.dl.dops.biz.resourcecenter.manager.poster.bo.PosterBO;
import com.dl.dops.resourcecenter.material.dto.RcMaterialDTO;
import com.dl.dops.resourcecenter.material.enums.RcMaterialTypeEnum;
import com.dl.dops.resourcecenter.pack.dto.RcPackBranchDetailDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackChainPathDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackElementAttachmentDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackElementDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackPageDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackSaveRespDTO;
import com.dl.dops.resourcecenter.pack.dto.RcRcPackChainPathDetailDTO;
import com.dl.dops.resourcecenter.pack.enums.RcPackDomainEnum;
import com.dl.dops.resourcecenter.pack.enums.RcPackStatusEnum;
import com.dl.dops.resourcecenter.pack.param.RcPackAddParam;
import com.dl.dops.resourcecenter.pack.param.RcPackChainPathSaveParam;
import com.dl.dops.resourcecenter.pack.param.RcPackElementSaveParam;
import com.dl.dops.resourcecenter.pack.param.RcPackUpdateParam;
import com.dl.dops.resourcecenter.web.controllers.poster.convert.PosterConvertor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 13:53
 */
public class PackConvert {

    public static PackAddBO cnvPackAddParam2BO(RcPackAddParam input) {
        PackAddBO result = new PackAddBO();
        if (StringUtils.isNotBlank(input.getCategory1())) {
            result.setCategory1(Long.valueOf(input.getCategory1()));
        }
        if (StringUtils.isNotBlank(input.getCategory2())) {
            result.setCategory2(Long.valueOf(input.getCategory2()));
        }
        result.setSceneOverview(input.getSceneOverview());
        result.setDetailedDescription(input.getDetailedDescription());
        result.setSuggest(input.getSuggest());
        result.setTitle(input.getTitle());
        result.setDomain(input.getDomain());
        result.setScene(input.getScene());
        return result;
    }

    public static PackUpdateBO cnvPackUpdateParam2BO(RcPackUpdateParam input) {
        PackUpdateBO result = new PackUpdateBO();
        result.setPackId(Long.valueOf(input.getPackId()));
        if (StringUtils.isNotBlank(input.getCategory1())) {
            result.setCategory1(Long.valueOf(input.getCategory1()));
        }
        if (StringUtils.isNotBlank(input.getCategory2())) {
            result.setCategory2(Long.valueOf(input.getCategory2()));
        }
        result.setDetailedDescription(input.getDetailedDescription());
        result.setSuggest(input.getSuggest());
        result.setTitle(input.getTitle());
        result.setSceneOverview(input.getSceneOverview());
        result.setDomain(input.getDomain());
        return result;
    }

    public static List<PackChainPathSaveBO> cnvPackChainPathSaveParam2BO(List<RcPackChainPathSaveParam> saveParamList) {
        return saveParamList.stream().map(saveParam -> {
            PackChainPathSaveBO saveBO = new PackChainPathSaveBO();
            if (StringUtils.isNotBlank(saveParam.getChainPathId())) {
                saveBO.setChainPathId(Long.valueOf(saveParam.getChainPathId()));
            }
            saveBO.setName(saveParam.getName());
            saveBO.setSort(saveParam.getSort());
            return saveBO;
        }).collect(Collectors.toList());
    }

    public static RcPackSaveRespDTO buildPackSaveRespDTO(Long packId, List<PackChainPathBO> chainPathDTOList) {
        RcPackSaveRespDTO vo = new RcPackSaveRespDTO();
        vo.setPackId(String.valueOf(packId));
        vo.setChainPathList(PackConvert.cnvPackChainPathBO2DTOList(chainPathDTOList));
        return vo;
    }

    public static List<RcPackChainPathDTO> cnvPackChainPathBO2DTOList(List<PackChainPathBO> chainPathDTOList) {
        return chainPathDTOList.stream().map(dto -> {
            RcPackChainPathDTO vo = new RcPackChainPathDTO();
            vo.setChainPathId(String.valueOf(dto.getChainPathId()));
            vo.setName(dto.getName());
            vo.setSort(dto.getSort());
            return vo;
        }).collect(Collectors.toList());
    }

    public static RcPackDTO buildPackDTO(PackBO input, Map<Long, String> categoryNameMap) {
        RcPackDTO result = new RcPackDTO();
        fillPackDTO(input,result,categoryNameMap);
        return result;
    }

    public static void fillPackDTO(PackBO input,RcPackDTO result, Map<Long, String> categoryNameMap) {
        result.setPackId(String.valueOf(input.getPackId()));
        result.setSceneOverview(input.getSceneOverview());
        result.setStatus(input.getStatus());
        result.setStatusDesc(RcPackStatusEnum.getDescByCode(input.getStatus()));
        result.setCreateTime(input.getCreateDt());
        result.setModifyTime(input.getModifyDt());
        result.setDetailedDescription(input.getDetailedDescription());
        result.setDomain(input.getDomain());
        result.setDomainName(RcPackDomainEnum.getNameByDomain(input.getDomain()));
        result.setSuggest(input.getSuggest());
        result.setTitle(input.getTitle());
        result.setScene(input.getScene());

        if (Objects.nonNull(input.getCategory1())) {
            result.setCategory1(String.valueOf(input.getCategory1()));
            result.setCategory1Name(categoryNameMap.get(input.getCategory1()));
        }
        if (Objects.nonNull(input.getCategory2())) {
            result.setCategory2(String.valueOf(input.getCategory2()));
            result.setCategory2Name(categoryNameMap.get(input.getCategory2()));
        }
    }

    public static RcPackPageDTO cnvPackBO2PageDTO(PackBO input) {
        RcPackPageDTO result = new RcPackPageDTO();
        result.setCreateTime(input.getCreateDt());
        result.setPackId(String.valueOf(input.getPackId()));
        result.setTitle(input.getTitle());
        result.setSceneOverview(input.getSceneOverview());
        result.setStatus(input.getStatus());
        result.setStatusDesc(RcPackStatusEnum.getDescByCode(input.getStatus()));
        result.setModifyTime(input.getModifyDt());
        result.setDomain(input.getDomain());
        result.setDomainName(RcPackDomainEnum.getNameByDomain(input.getDomain()));
        result.setScene(input.getScene());
        return result;
    }
    public static RcPackPageDTO cnvEsIndexPack2PackPageDTO(EsIndexRcPack indexRcPack){
        RcPackPageDTO pageDTO = new RcPackPageDTO();
        pageDTO.setCreateTime(indexRcPack.getCreateDt());
        pageDTO.setPackId(String.valueOf(indexRcPack.getPackId()));
        pageDTO.setTitle(indexRcPack.getTitle());
        pageDTO.setSceneOverview(indexRcPack.getSceneOverview());
        pageDTO.setStatus(indexRcPack.getStatus());
        pageDTO.setStatusDesc(RcPackStatusEnum.getDescByCode(indexRcPack.getStatus()));
        pageDTO.setModifyTime(indexRcPack.getModifyDt());
        pageDTO.setDomain(indexRcPack.getDomain());
        pageDTO.setDomainName(RcPackDomainEnum.getNameByDomain(indexRcPack.getDomain()));
        pageDTO.setScene(indexRcPack.getScene());
        return pageDTO;
    }

    public static List<PackElementSaveBO> cnvPackElementSaveParam2BOList(
            List<RcPackElementSaveParam> elementSaveParamList) {
        List<PackElementSaveBO> saveBOList = elementSaveParamList.stream().map(param -> {
            PackElementSaveBO bo = new PackElementSaveBO();
            if (StringUtils.isNotBlank(param.getElementId())) {
                bo.setElementId(Long.valueOf(param.getElementId()));
            }
            bo.setContent(param.getContent());
            bo.setExtData(param.getExtData());
            bo.setSort(param.getSort());
            bo.setTitle(param.getTitle());
            bo.setType(param.getType());
            bo.setRemark(param.getRemark());
            if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
                StringBuffer sbf = new StringBuffer();
                for (int i = 0; i < param.getAttachmentList().size(); i++) {
                    sbf.append(JSONUtil.toJsonStr(param.getAttachmentList().get(i)));
                    if (i != param.getAttachmentList().size() - 1) {
                        sbf.append(SymbolE.COMMA.getValue());
                    }
                }
                bo.setAttachments(sbf.toString());
            }
            return bo;
        }).collect(Collectors.toList());

        return saveBOList;
    }

    public static RcRcPackChainPathDetailDTO cnvPackChainPathDTO2DetailDTO(PackChainPathBO input) {
        RcRcPackChainPathDetailDTO result = new RcRcPackChainPathDetailDTO();
        fillPackChainPathDTO(input, result);
        return result;
    }

    public static RcPackChainPathDTO fillPackChainPathDTO(PackChainPathBO input, RcPackChainPathDTO result) {
        result.setChainPathId(String.valueOf(input.getChainPathId()));
        result.setName(input.getName());
        result.setSort(input.getSort());
        return result;
    }

    public static RcPackBranchDetailDTO buildPackBranchDetailDTO(PackBranchBO packBranchBO,
            List<PackElementBO> packElementBOList, Map<String, MaterialBO> materialBOMap,
            Map<String, PosterBO> posterBOMap) {
        RcPackBranchDetailDTO result = new RcPackBranchDetailDTO();
        //只有当存在分支时，才set分支的值
        if (Objects.nonNull(packBranchBO)) {
            result.setName(packBranchBO.getName());
            result.setSort(packBranchBO.getSort());
            result.setBranchId(String.valueOf(packBranchBO.getBranchId()));
        }

        List<RcPackElementDTO> packElementList = packElementBOList.stream().map(elementBO -> {
            RcPackElementDTO elementDTO = new RcPackElementDTO();
            elementDTO.setElementId(String.valueOf(elementBO.getElementId()));
            if (Objects.nonNull(elementBO.getChainPathId())) {
                elementDTO.setChainPathId(elementBO.getChainPathId() + "");
            }
            if (Objects.nonNull(elementBO.getBranchId())) {
                elementDTO.setBranchId(elementBO.getBranchId() + "");
            }
            elementDTO.setSort(elementBO.getSort());
            elementDTO.setContent(elementBO.getContent());
            elementDTO.setTitle(elementBO.getTitle());
            elementDTO.setType(elementBO.getType());
            elementDTO.setExtData(elementBO.getExtData());
            elementDTO.setRemark(elementBO.getRemark());
            if (CollectionUtils.isEmpty(elementBO.getAttachmentList())) {
                return elementDTO;
            }

            List<RcPackElementAttachmentDTO> attachmentList = new ArrayList<>();
            elementDTO.setAttachmentList(attachmentList);
            for (PackElementAttachmentBO attachmentBO : elementBO.getAttachmentList()) {
                RcPackElementAttachmentDTO attachmentDTO = new RcPackElementAttachmentDTO();

                if (Objects.nonNull(attachmentBO.getMaterialId())) {
                    attachmentDTO.setMaterialId(String.valueOf(attachmentBO.getMaterialId()));
                    attachmentList.add(attachmentDTO);
                    MaterialBO materialBO = materialBOMap.get(String.valueOf(attachmentBO.getMaterialId()));
                    if (Objects.nonNull(materialBO)) {
                        RcMaterialDTO materialDTO = new RcMaterialDTO();
                        materialDTO.setMaterialId(materialBO.getMaterialId());
                        materialDTO.setTitle(materialBO.getTitle());
                        materialDTO.setMaterialType(materialBO.getMaterialType());
                        materialDTO.setLogoImg(materialBO.getLogoImg());
                        if (!RcMaterialTypeEnum.ARTICLE.getCode().equals(materialBO.getMaterialType())) {
                            materialDTO.setContent(materialBO.getContent());
                        }
                        attachmentDTO.setMaterial(materialDTO);
                        continue;
                    }
                }

                if (Objects.nonNull(attachmentBO.getPosterId())) {
                    attachmentDTO.setPosterId(String.valueOf(attachmentBO.getPosterId()));
                    attachmentList.add(attachmentDTO);
                    PosterBO posterBO = posterBOMap.get(String.valueOf(attachmentBO.getPosterId()));
                    if (Objects.nonNull(posterBO)) {
                        attachmentDTO.setPoster(PosterConvertor.convert(posterBO));
                        continue;
                    }
                }
            }
            return elementDTO;
        }).sorted(Comparator.comparing(RcPackElementDTO::getSort)).collect(Collectors.toList());

        result.setPackElementList(packElementList);
        return result;
    }
}
