package com.dl.dops.biz.resourcecenter.dal.poster.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@TableName("poster")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PosterPO extends BasePO {

    private static final long serialVersionUID = -9105945430791241241L;

    private Long id;

    private Long bizId;

    private String name;

    private Long categoryId;

    private String logoImgUrl;

    private String imgUrl;

    private Integer isDeleted;

}