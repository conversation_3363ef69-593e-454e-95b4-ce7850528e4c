package com.dl.dops.biz.common.tencentcloud.cos.impl;

import cn.hutool.core.io.FileUtil;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.enums.SymbolE;
import com.dl.dops.biz.common.tencentcloud.cos.CosFileUploadManager;
import com.dl.dops.biz.common.tencentcloud.cos.properties.CosProperties;
import com.dl.dops.biz.common.tencentcloud.properties.ApiProperties;
import com.dl.dops.biz.common.tencentcloud.properties.TencentCloudProperties;
import com.dl.framework.common.idg.HostTimeIdg;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.util.Objects;

/**
 * @ClassName CosFileUploadManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/7/11 17:58
 * @Version 1.0
 **/
@Slf4j
@Service
public class CosFileUploadManagerImpl implements CosFileUploadManager {

    private final static String FILE_DIR_TEMPLATE = "material/%s/";
    private final static String FILE_URL = "https://%s.cos.%s.myqcloud.com/%s";

    @Autowired
    private TencentCloudProperties tencentCloudProperties;

    @Autowired
    private HostTimeIdg hostTimeIdg;

    /**
     * 资源中心图片上传到cos的相对路径
     * 目前只有图片会通过后端上传
     */
    private static final String COS_PATH_RESOURCE_IMAGE = "resource/images/";

    @Override
    public String uploadFile(File file, String type) {
        if (Objects.isNull(file)) {
            return null;
        }
        CosProperties cosProperties = tencentCloudProperties.getCosProperties();
        String bucketId = cosProperties.getBucketId();
        String fileName = file.getName();
        //处理文件类型
        if (StringUtils.hasLength(type) && !StringUtils.hasLength(FileUtil.extName(fileName))) {
            fileName = fileName + SymbolE.DOT.getValue() + type;
        }
        fileName = hostTimeIdg.generateId().longValue() + SymbolE.DOT.getValue() + FileUtil.extName(fileName);
        //目前只有图片会通过后端上传,故路径先固定用图片的
        fileName = COS_PATH_RESOURCE_IMAGE + fileName;
        COSClient cosClient = createCli();
        try {
            ObjectMetadata objectMetadata = new ObjectMetadata();
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketId, fileName, file);
            putObjectRequest.withMetadata(objectMetadata);
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            log.warn("cos upload requestId={}", putObjectResult.getRequestId());
            return String.format(FILE_URL, cosProperties.getBucketId(), cosProperties.getRegion(), fileName);
        } catch (Exception e) {
            log.error("", e);
        } finally {
            cosClient.shutdown();
        }
        return null;
    }

    @Override
    public String uploadFile(File file, String tenantCode, String type, String path) {
        if (Objects.isNull(file)) {
            return null;
        }
        CosProperties cosProperties = tencentCloudProperties.getCosProperties();
        String bucketId = cosProperties.getBucketId();
        String fileName = file.getName();
        //处理文件类型
        if (StringUtils.hasLength(type) && !StringUtils.hasLength(FileUtil.extName(fileName))) {
            fileName = fileName + SymbolE.DOT.getValue() + type;
        }
        fileName = hostTimeIdg.generateId().longValue() + SymbolE.MINUS.getValue() + fileName;
        if (StringUtils.hasLength(path)) {
            fileName = path + Const.SLASH + fileName;
        } else {
            fileName = String.format(FILE_DIR_TEMPLATE, tenantCode) + fileName;
        }
        COSClient cosClient = createCli();
        try {
            ObjectMetadata objectMetadata = new ObjectMetadata();
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketId, fileName, file);
            putObjectRequest.withMetadata(objectMetadata);
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            log.warn("cos upload requestId={}", putObjectResult.getRequestId());
            return String.format(FILE_URL, cosProperties.getBucketId(), cosProperties.getRegion(), fileName);
        } catch (Exception e) {
            log.error("", e);
        } finally {
            cosClient.shutdown();
        }
        return null;
    }

    private COSClient createCli() {
        ApiProperties api = tencentCloudProperties.getApi();
        // 初始化用户身份信息(secretId, secretKey)
        COSCredentials cred = new BasicCOSCredentials(api.getSecretId(), api.getSecretKey());
        // 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(tencentCloudProperties.getCosProperties().getRegion()));
        // 生成cos客户端
        return new COSClient(cred, clientConfig);
    }
}
