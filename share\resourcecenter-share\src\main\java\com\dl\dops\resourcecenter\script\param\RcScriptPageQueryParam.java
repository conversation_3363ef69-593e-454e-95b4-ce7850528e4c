package com.dl.dops.resourcecenter.script.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @describe: CntScriptPageQueryParam
 * @author: zhousx
 * @date: 2022/6/17 9:42
 */
@Data
public class RcScriptPageQueryParam extends AbstractPageParam implements Serializable {
    private static final long serialVersionUID = -5276830740792690802L;
    private Integer scope = 0;

    private Integer type = 0;

    private String keyword;

    private String category1;

    private String category2;

    @ApiModelProperty("发布状态，1-已发布，2-取消发布")
    private Integer publishStatus;

    @ApiModelProperty("是否需要查询文章内容")
    private Integer needArticleContent;
}
