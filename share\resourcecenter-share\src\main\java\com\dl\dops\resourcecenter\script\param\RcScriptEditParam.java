package com.dl.dops.resourcecenter.script.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @describe: CntScriptEditParam
 * @author: zhousx
 * @date: 2022/6/17 17:27
 */
@Data
public class RcScriptEditParam implements Serializable {
    private static final long serialVersionUID = -8178744364722983726L;
    @NotBlank
    private String scriptId;

    @NotBlank
    private String question;

    @NotBlank
    private String content;

    private String category1;

    private String category2;

    private String remark;

    private List<String> materialIds;
}
