package com.dl.dops.system.web.user.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @describe: ResetUserPasswordParam
 * @author: 韦建团
 * @date: 2022/12/7 10:50
 */
@Data
@ApiModel("用户管理-重置密码")
public class ResetUserPasswordParam {

    @NotBlank
    @ApiModelProperty("后台账号id")
    private String userId;

    @Size(min = 1, max = 50, message = "密码最长不超过50位")
    @ApiModelProperty("用户密码")
    private String password;

}
