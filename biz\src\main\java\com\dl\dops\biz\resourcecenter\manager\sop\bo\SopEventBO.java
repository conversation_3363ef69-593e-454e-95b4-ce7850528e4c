package com.dl.dops.biz.resourcecenter.manager.sop.bo;

import com.dl.dops.biz.common.BaseBO;
import com.dl.dops.resourcecenter.sop.enums.RcSopEventRuleTypeEnum;
import lombok.Data;

import java.util.Date;

@Data
public class SopEventBO extends BaseBO {
    private static final long serialVersionUID = 1602014745166134561L;
    private Long eventId;

    private Long sopId;

    private String content;

    private Integer reachType;

    /**
     * @see RcSopEventRuleTypeEnum
     */
    private Integer ruleType;

    /**
     * 规则内容，定时推送时为天-HH:mm:ss  周期推送为首次天-天-HH:mm:ss
     */
    private String ruleContent;

    private Date beginDate;

    private String name;

    private String remark;

    /**
     * 有效期
     */
    private Integer validityPeriod;
}
