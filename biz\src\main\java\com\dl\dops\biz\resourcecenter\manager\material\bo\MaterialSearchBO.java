package com.dl.dops.biz.resourcecenter.manager.material.bo;

import com.dl.dops.biz.common.SearchBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Data
@ApiModel("查询素材")
public class MaterialSearchBO extends SearchBO {

    private static final long serialVersionUID = 147882529038113020L;
    @ApiModelProperty("标题")
    @Length(max = 100)
    private String title;

    @ApiModelProperty("素材类型")
    private List<Integer> materialTypes;

    @ApiModelProperty("发布状态，1-已发布，2-取消发布")
    private Integer publishStatus;

    /**
     * @see:TagDefaultEnum
     */
    @ApiModelProperty("标签id，,-1表示查全部，0表示查无标签")
    private String tagId;

    @ApiModelProperty("分类id列表")
    private String categoryId;
}
