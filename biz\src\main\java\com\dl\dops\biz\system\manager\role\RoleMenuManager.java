package com.dl.dops.biz.system.manager.role;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.system.dal.role.po.RoleMenuPO;
import com.dl.dops.biz.system.manager.menu.bo.RoleIdsMenuParamBO;
import com.dl.dops.biz.system.manager.menu.bo.RoleMenuParamBO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 14:29
 */
public interface RoleMenuManager extends IService<RoleMenuPO> {

    boolean hasMenu(Long roleId);

    /**
     * 保存角色权限信息
     *
     * @param bo
     */
    Boolean saveRoleMenu(RoleMenuParamBO bo);

    /**
     * 删除角色权限信息
     *
     * @param bo
     */
    Boolean delRoleMenu(RoleIdsMenuParamBO bo);

}
