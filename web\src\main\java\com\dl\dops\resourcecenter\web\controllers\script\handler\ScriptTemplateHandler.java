package com.dl.dops.resourcecenter.web.controllers.script.handler;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Sheet;

/**
 * @describe: ScriptTemplateHandler
 * @author: zhousx
 * @date: 2022/6/20 15:06
 */
public class ScriptTemplateHandler implements RowWriteHandler {
    @Override
    public void afterRowDispose(RowWriteHandlerContext context) {
        if (context.getRowIndex() > 6) {
            Sheet sheet = context.getWriteSheetHolder().getSheet();
            for (int i = 0; i < 4; i++) {
                Cell cell = sheet.getRow(6).getCell(i);
                Font font = sheet.getWorkbook().createFont();
                font.setColor(Font.COLOR_RED);
                font.setFontHeightInPoints((short) 16);
                CellStyle cellStyle = sheet.getWorkbook().createCellStyle();
                cellStyle.setFont(font);
                cell.setCellStyle(cellStyle);
            }
        }
    }
}
