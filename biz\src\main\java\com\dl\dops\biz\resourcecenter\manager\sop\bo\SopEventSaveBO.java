package com.dl.dops.biz.resourcecenter.manager.sop.bo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-06-10 16:28
 */
@Data
public class SopEventSaveBO {

    /**
     * sopId
     */
    private Long sopId;

    /**
     * 事件id
     */
    private Long eventId;

    /**
     * 触达形式
     */
    private Integer reachType;

    /**
     * 事项名称
     */
    private String name;

    /**
     * 时间规则类型 1定时推送   2周期推送
     */
    private Integer ruleType;

    /**
     * 规则内容，定时推送时为天-HH:mm:ss  周期推送为首次天-天-HH:mm:ss
     */
    private String ruleContent;

    /**
     * 发送内容
     */
    private String content;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效期
     */
    private Integer validityPeriod;

    /**
     * 内容列表
     */
    private List<SopEventCntSaveBO> cntSaveBOList;

}
