package com.dl.dops.magicvideo.deliveryplan.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @describe: UpdateDeliveryPlanParam
 * @author: zhousx
 * @date: 2023/9/7 9:56
 */
@Data
public class UpdateDeliveryPlanBO {
    @ApiModelProperty(value = "计划id", required = true)
    @NotBlank
    private String planId;

    @ApiModelProperty(value = "计划名称", required = true)
    @NotBlank
    private String name;

    @ApiModelProperty("简介")
    private String desc;

    @ApiModelProperty(value = "负责人", required = true)
    @NotBlank
    private String director;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("触发次数限制")
    private Integer limit;

    @ApiModelProperty("触发次数限制周期，-1-无，0-天，1-周，2-月")
    private Integer period;

    @ApiModelProperty("状态，0-停止，1-运行中")
    private Integer status;

    @ApiModelProperty("是否打开消息通知，0-否 1-是")
    private Integer isNotify;

    @ApiModelProperty("消息通知地址")
    private String notifyUrl;

    @ApiModelProperty("客户回调地址")
    private String callbackUrl;

    @ApiModelProperty("生产方式 0-接口单次触发 1-批量触发 2-定时触发")
    private Integer produceWay;

    private Integer hasPeriod;
}
