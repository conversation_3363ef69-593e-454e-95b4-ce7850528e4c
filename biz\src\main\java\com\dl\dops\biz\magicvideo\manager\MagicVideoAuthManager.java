package com.dl.dops.biz.magicvideo.manager;

import cn.hutool.json.JSONUtil;
import com.dl.dops.biz.common.forest.magicvideo.MagicVideoClient;
import com.dl.dops.biz.common.forest.magicvideo.dto.TenantTemplateAuthDTO;
import com.dl.dops.biz.common.forest.magicvideo.param.TenantAuthParam;
import com.dl.dops.biz.common.forest.magicvideo.param.TenantTemplateAuthPageQueryParam;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MagicVideoAuthManager {
    @Autowired
    private MagicVideoClient magicVideoClient;
    public ResultPageModel<TenantTemplateAuthDTO> list(TenantTemplateAuthPageQueryParam param) {
        ResultPageModel<TenantTemplateAuthDTO> result = magicVideoClient.list(param);
        if (!result.isSuccess()) {
            log.error("获取模板列表失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("获取模板列表失败");
        }
        return result;
    }

    /**
     * 修改授权
     * @param param
     * @return
     */
    public ResultModel<Void> auth(TenantAuthParam param) {
        ResultModel<Void> result = magicVideoClient.auth(param);
        if (!result.isSuccess()){
            log.error("修改授权失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("修改授权失败");
        }
        return result;
    }
}
