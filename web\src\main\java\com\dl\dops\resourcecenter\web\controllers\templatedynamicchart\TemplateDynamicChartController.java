package com.dl.dops.resourcecenter.web.controllers.templatedynamicchart;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dl.dops.biz.common.annotation.NotLogin;
import com.dl.dops.biz.system.dal.templatedynamicchart.po.TemplateDynamicChartPO;
import com.dl.dops.biz.system.manager.dynamicchart.TemplateDynamicChartManager;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.resourcecenter.web.controllers.templatedynamicchart.convert.TemplateDynamicChartConverter;
import com.dl.dops.resourcecenter.web.controllers.templatedynamicchart.param.DynamicChartPageParam;
import com.dl.dops.resourcecenter.web.controllers.templatedynamicchart.vo.TemplateDynamicChartVO;
import com.dl.framework.common.bo.PageQueryDO;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping({ "/resource/dynamicchart/template", "/dops/resource/dynamicchart/template" })
@Api("动态图表")
public class TemplateDynamicChartController extends AbstractController {
    @Resource
    private TemplateDynamicChartManager templateDynamicChartManager;

    @PostMapping("/list")
    @ApiOperation("获取列表")
    @NotLogin
    public ResultPageModel<TemplateDynamicChartVO> list(@RequestBody DynamicChartPageParam param) {
        Assert.isTrue(Objects.nonNull(param), "查询信息不能为空");
        TemplateDynamicChartPO po = new TemplateDynamicChartPO();
        po.setTemplateId(param.getTemplateId());
        po.setType(param.getType());
        po.setName(param.getName());
        PageQueryDO pageQueryDO = new PageQueryDO();
        pageQueryDO.setPageIndex(param.getPageIndex());
        pageQueryDO.setPageSize(param.getPageSize());
        IPage<TemplateDynamicChartPO> iPage = templateDynamicChartManager.pagePO(po, pageQueryDO);
        List<TemplateDynamicChartVO> resultList = iPage.getRecords().stream().map(bo -> {
            TemplateDynamicChartVO vo = TemplateDynamicChartConverter.fillToDTO(bo);
            return vo;
        }).collect(Collectors.toList());
        return pageQueryModel(iPage, resultList);
    }
}
