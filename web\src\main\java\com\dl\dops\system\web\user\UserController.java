/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dl.dops.system.web.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dl.dops.biz.common.annotation.Permission;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.system.dal.function.po.FunctionPO;
import com.dl.dops.biz.system.dal.role.po.RolePO;
import com.dl.dops.biz.system.dal.role.po.UserRolePO;
import com.dl.dops.biz.system.dal.user.po.UserPO;
import com.dl.dops.biz.system.manager.menu.dto.MenuDTO;
import com.dl.dops.biz.system.manager.role.RoleManager;
import com.dl.dops.biz.system.manager.role.UserRoleManager;
import com.dl.dops.biz.system.manager.role.dto.RoleIdDTO;
import com.dl.dops.biz.system.manager.user.UserManager;
import com.dl.dops.biz.system.manager.user.bo.AdmUserBO;
import com.dl.dops.biz.system.manager.user.bo.ResetUserPasswordBO;
import com.dl.dops.biz.system.manager.user.bo.UpdateUserSelfBO;
import com.dl.dops.biz.system.manager.user.bo.UserRolesParamBO;
import com.dl.dops.biz.system.manager.user.bo.UserSaveBO;
import com.dl.dops.biz.system.manager.user.bo.UserSearchParamBO;
import com.dl.dops.biz.system.manager.user.dto.UserDTO;
import com.dl.dops.biz.system.manager.user.enums.SysUserStatusEnum;
import com.dl.dops.biz.system.manager.user.impl.UserRoleMenuRedisCache;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.system.web.menu.vo.FuncVO;
import com.dl.dops.system.web.role.vo.RoleVO;
import com.dl.dops.system.web.user.param.AddUserParam;
import com.dl.dops.system.web.user.param.ResetUserPasswordParam;
import com.dl.dops.system.web.user.param.UpdateSelfInfoParam;
import com.dl.dops.system.web.user.param.UserIdParam;
import com.dl.dops.system.web.user.param.UserPageParam;
import com.dl.dops.system.web.user.param.UserRolesParam;
import com.dl.dops.system.web.user.vo.UserMenuVO;
import com.dl.dops.system.web.user.vo.UserVO;
import com.dl.dops.system.web.util.AccountCompent;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Api(tags = "系统：用户管理")
@RestController
@RequestMapping("/dops/admusers")
public class UserController extends AbstractController {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private AccountCompent accountCompent;

    @Autowired
    private UserManager userManager;

    @Autowired
    private UserRoleManager userRoleManager;

    @Autowired
    private RoleManager roleManager;

    @Autowired
    private UserRoleMenuRedisCache userRoleMenuRedisCache;

    @GetMapping("/getAdmUserInfo")
    @ApiOperation("当前用户详情")
    public ResultModel<UserVO> getAdmUserInfo() {
        UserDTO dto = accountCompent.getCurrentDetail();
        Assert.isTrue(dto != null, "当前用户异常，请重新登录");

        UserVO admUserVO = new UserVO();
        admUserVO.setUserId(dto.getUserId() + "");
        admUserVO.setAccount(dto.getAccount());
        admUserVO.setUserName(dto.getUserName());
        admUserVO.setIsSuperAdm(dto.getIsSuperAdm() + "");
        admUserVO.setGender(dto.getGender());
        admUserVO.setToken(dto.getToken());
        admUserVO.setStatus(dto.getStatus() + "");
        admUserVO.setMobile(dto.getMobile());

        Set<FunctionPO> set = userManager.getUserFunctions(dto.getUserId());
        if (CollectionUtils.isNotEmpty(set)) {
            admUserVO.setFunctions(set.stream().map(f -> {
                if (Objects.isNull(f)) {
                    return null;
                }
                FuncVO fv = new FuncVO();
                fv.setFunctionId(f.getFunctionId() + "");
                fv.setFunctionCode(f.getFunctionCode());
                return fv;
            }).filter(Objects::nonNull).collect(Collectors.toSet()));
        }

        List<MenuDTO> list = userRoleMenuRedisCache.getMenus(getUserRoleids());
        if (CollectionUtils.isNotEmpty(list)) {
            admUserVO.setMenus(this.listWithTree(list));
        }

        return ResultModel.success(admUserVO);
    }

    @Permission("adm:user:query")
    @PostMapping("/page")
    @ApiOperation("查询用户列表")
    public ResultPageModel<UserVO> page(@RequestBody @Validated UserPageParam param) {
        UserSearchParamBO bo = new UserSearchParamBO();
        bo.setUserName(param.getUserName());
        bo.setAccount(param.getAccount());
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setStatus(param.getStatus());
        IPage<UserDTO> page = userManager.findUsers(bo);
        if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getRecords())) {
            return new ResultPageModel<>();
        }

        List<Long> userIds = page.getRecords().stream().map(UserDTO::getUserId).collect(Collectors.toList());

        List<UserRolePO> userRoles = userRoleManager.lambdaQuery().in(UserRolePO::getUserId, userIds).list();
        Map<Long, List<RolePO>> userRoleMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userRoles)) {
            List<RolePO> roles = roleManager.lambdaQuery().in(RolePO::getRoleId,
                    userRoles.stream().map(UserRolePO::getRoleId).distinct().collect(Collectors.toList())).list();
            Map<Long, RolePO> roleMap = roles.stream()
                    .collect(Collectors.toMap(RolePO::getRoleId, Function.identity()));
            userRoleMap.putAll(userRoles.stream().collect(Collectors.groupingBy(UserRolePO::getUserId,
                    Collectors.mapping(userRole -> roleMap.get(userRole.getRoleId()), Collectors.toList()))));
        }
        return pageQueryModel(page.convert(dto -> {
            UserVO vo = new UserVO();
            vo.setUserId(dto.getUserId() + "");
            vo.setUserName(dto.getUserName());
            vo.setAccount(dto.getAccount());
            vo.setIsSuperAdm(dto.getIsSuperAdm() + "");
            vo.setGender(dto.getGender());
            vo.setStatus(dto.getStatus() + "");
            vo.setStatusStr(SysUserStatusEnum.getByCode(dto.getStatus()).getDesc());
            List<RolePO> roles = userRoleMap.get(dto.getUserId());
            if (CollectionUtils.isNotEmpty(roles)) {
                vo.setRoles(roles.stream().map(r -> {
                    RoleVO roleVO = new RoleVO();
                    roleVO.setRoleId(r.getRoleId() + "");
                    roleVO.setRoleName(r.getName());
                    return roleVO;
                }).collect(Collectors.toList()));
            }
            return vo;
        }));
    }

    /**
     * 此接口为管理员在用户管理列表对用户信息进行修改
     */
    @Permission("adm:user:update")
    @PostMapping("/update")
    @ApiOperation("更新用户信息")
    public ResultModel<Void> update(@RequestBody @Validated AdmUserBO bo) {
        //禁止非超管修改用户为超管
        if (Objects.equals(getCurrentDetail().getIsSuperAdm(), 0) && "1".equals(bo.getIsSuperAdm())) {
            return ResultModel.error("2", "权限不足");
        }
        UserPO po = new UserPO();
        po.setUserId(bo.getUserId());
        po.setAccount(bo.getAccount());
        po.setIsSuperAdm(Objects.nonNull(bo.getIsSuperAdm()) ? Integer.valueOf(bo.getIsSuperAdm()) : null);
        po.setStatus(Objects.nonNull(bo.getStatus()) ? Integer.valueOf(bo.getStatus()) : null);
        po.setUserName(bo.getUserName());
        userManager.updateUserDetail(po);
        return ResultModel.success(null);
    }

    /**
     * 此接口为用户修改自己的用户信息，故无需增加权限点
     */
    @PostMapping("/updateselfinfo")
    @ApiOperation("用户修改个人信息")
    public ResultModel<Boolean> updateSelfInfo(@RequestBody @Validated UpdateSelfInfoParam param) {
        UpdateUserSelfBO bo = new UpdateUserSelfBO();
        bo.setUserId(Objects.isNull(param.getUserId()) ? getUserId() : param.getUserId());
        bo.setAccount(param.getAccount());
        bo.setUserName(param.getUserName());
        bo.setModifyBy(getUserId());
        bo.setStatus(param.getStatus());
        boolean request = userManager.updateUserSelfDetail(bo);
        //修改了账号则退出登录
        if (!StringUtils.isBlank(param.getAccount())) {
            userManager.logoutJwtToken(getToken());
        }
        return ResultModel.success(request);
    }

    //@Permission("adm:user:resetpassword")
    @PostMapping("/resetuserpassword")
    @ApiOperation("重置用户密码")
    public ResultModel<Boolean> resetUserPassword(@RequestBody @Validated ResetUserPasswordParam param) {
        ResetUserPasswordBO BO = new ResetUserPasswordBO();
        BO.setUserId(param.getUserId());
        BO.setPassword(param.getPassword());
        return ResultModel.success(userManager.resetUserPassword(BO));
    }

    @Permission("adm:user:add")
    @PostMapping("/add")
    @ApiOperation("新增用户")
    public ResultModel<UserVO> addUser(@RequestBody @Validated AddUserParam param) {
        LambdaQueryWrapper<UserPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPO::getAccount, param.getAccount().trim()).eq(UserPO::getIsDeleted, Const.ZERO);
        UserPO userPO = userManager.getOne(queryWrapper);
        Assert.isNull(userPO, "登录账号已存在");

        UserSaveBO saveBO = new UserSaveBO();
        saveBO.setPassword(param.getPassword());
        saveBO.setAccount(param.getAccount());
        saveBO.setUserName(param.getUserName());
        UserDTO dto = userManager.addUser(saveBO);
        Assert.notNull(dto, "创建用户失败！");

        if (CollectionUtils.isNotEmpty(param.getRoleIds())) {
            UserRolesParamBO userRolesParamBO = new UserRolesParamBO();
            userRolesParamBO.setUserId(dto.getUserId());
            userRolesParamBO.setRoleIds(param.getRoleIds());
            userRolesParamBO.setLoginUserId(getUserId());
            userRoleManager.saveUserRoles(userRolesParamBO);
        }

        UserVO vo = new UserVO();
        vo.setUserId(dto.getUserId() + "");
        vo.setUserName(dto.getUserName());
        vo.setAccount(dto.getAccount());
        vo.setIsSuperAdm(dto.getIsSuperAdm() + "");
        vo.setGender(dto.getGender());
        vo.setStatus(dto.getStatus() + "");
        return ResultModel.success(vo);
    }

    @Permission("adm:user_role:save")
    @PostMapping("/userrole/save")
    @ApiOperation("保存用户-角色配置")
    public ResultModel<Void> saveUserRole(@RequestBody @Validated UserRolesParam param) {
        UserRolesParamBO bo = new UserRolesParamBO();
        if (StringUtils.isNumeric(param.getUserId())) {
            bo.setUserId(Long.valueOf(param.getUserId()));
        }
        bo.setRoleIds(param.getRoleIds());
        bo.setLoginUserId(getUserId());

        userRoleManager.saveUserRoles(bo);
        return ResultModel.success(null);
    }

    @Permission("adm:user_role:query")
    @PostMapping("/role/query")
    @ApiOperation("查询用户-角色配置")
    public ResultModel<List<RoleVO>> queryUserRoles(@RequestBody @Validated UserIdParam param) {
        List<RoleIdDTO> list = userRoleManager.findByUserId(Long.valueOf(param.getUserId()));
        if (CollectionUtils.isEmpty(list)) {
            return new ResultModel<>();
        }
        return ResultModel.success(list.stream().map(dto -> {
            RoleVO vo = new RoleVO();
            vo.setRoleId(String.valueOf(dto.getRoleId()));
            return vo;
        }).collect(Collectors.toList()));
    }

    // 组装微树形
    private List<UserMenuVO> listWithTree(List<MenuDTO> entities) {
        // 2 组装成父子的树形结构
        List<UserMenuVO> level1Menus = entities.stream().filter(entity -> entity.getParentId() == 0).map(m -> {
            UserMenuVO vo = new UserMenuVO();
            vo.setMenuId(String.valueOf(m.getMenuId()));
            vo.setIcon(m.getIcon());
            vo.setLevel(m.getLevel());
            vo.setName(m.getName());
            vo.setSort(m.getSort());
            vo.setParentId(String.valueOf(m.getParentId()));
            vo.setUrl(m.getUrl());
            vo.setChildren(getChildrens(m, entities));
            return vo;
        }).sorted(Comparator.comparingInt(menu -> (menu.getSort() == null ? 0 : menu.getSort())))
                .collect(Collectors.toList());
        return level1Menus;
    }

    // 递归查找所有菜单的子菜单
    private List<UserMenuVO> getChildrens(MenuDTO root, List<MenuDTO> all) {
        List<UserMenuVO> children = all.stream().filter(entity -> {
            return entity.getParentId().longValue() == root.getMenuId()
                    .longValue();  // 注意此处应该用longValue()来比较，否则会出先bug，因为parentCid和catId是long类型
        }).map(m -> {
            // 1 找到子菜单
            UserMenuVO vo = new UserMenuVO();
            vo.setMenuId(String.valueOf(m.getMenuId()));
            vo.setIcon(m.getIcon());
            vo.setLevel(m.getLevel());
            vo.setName(m.getName());
            vo.setSort(m.getSort());
            vo.setParentId(String.valueOf(m.getParentId()));
            vo.setUrl(m.getUrl());
            vo.setChildren(getChildrens(m, all));
            return vo;
        }).sorted(Comparator.comparingInt(menu -> (menu.getSort() == null ? 0 : menu.getSort())))
                .collect(Collectors.toList());
        return children;
    }

}
