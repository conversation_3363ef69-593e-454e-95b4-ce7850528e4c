package com.dl.dops.biz.resourcecenter.manager.script.bo;

import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialBO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @describe: ScriptDetailDTO
 * @author: zhousx
 * @date: 2022/6/16 9:22
 */
@Data
public class ScriptDetailBO {
    private Long scriptId;

    private String question;

    private String content;

    private Date createDt;

    private Long createBy;

    private String creatorName;

    private Long category1;

    private Long category2;

    private String category1Name;

    private String category2Name;

    private List<MaterialBO> materials;

    private Integer publishStatus;

    private String remark;
}
