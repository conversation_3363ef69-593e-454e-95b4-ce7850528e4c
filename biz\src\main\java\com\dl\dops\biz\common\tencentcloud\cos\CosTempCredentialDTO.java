package com.dl.dops.biz.common.tencentcloud.cos;

import com.dl.dops.biz.common.BaseBO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CosTempCredentialDTO extends BaseBO {

    private static final long serialVersionUID = -5709302418614073832L;
    String tmpSecretId;

    String tmpSecretKey;

    String sessionToken;

    Long startTime;

    Long expiredTime;

    String bucketId;

    String region;

}
