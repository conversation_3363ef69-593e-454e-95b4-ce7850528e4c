package com.dl.dops.biz.common.forest.magicvideo.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @describe: TemplatePageQueryParam
 * @author: zhousx
 * @date: 2023/2/8 11:15
 */
@Data
public class TemplateAuthPageQueryParam extends AbstractPageParam {
    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty(value = "模板id")
    private String templateId;

    @ApiModelProperty(value = "来源模板id")
    private String sourceTemplateId;

    @ApiModelProperty("1-竖版 2-横版")
    private Integer resolutionType;

    @ApiModelProperty("授权租户编号")
    private String authTenantCode;
}
