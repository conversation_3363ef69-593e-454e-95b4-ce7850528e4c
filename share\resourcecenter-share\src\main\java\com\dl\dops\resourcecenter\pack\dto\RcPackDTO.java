package com.dl.dops.resourcecenter.pack.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-08 19:01
 */
@Data
@ApiModel("服务包视图对象")
public class RcPackDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("服务包id")
    private String packId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("所属一级分类id")
    private String category1;

    @ApiModelProperty("所属一级分类名")
    private String category1Name;

    @ApiModelProperty("所属二级分类id")
    private String category2;

    @ApiModelProperty("所属二级分类名")
    private String category2Name;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态描述")
    private String statusDesc;

    @ApiModelProperty("场景概述")
    private String sceneOverview;

    @ApiModelProperty("所属行业")
    private Integer domain;

    @ApiModelProperty("所属行业名")
    private String domainName;

    @ApiModelProperty("详细描述")
    private String detailedDescription;

    @ApiModelProperty("运营投放建议")
    private String suggest;

    /**
     * 场景 1-助你营，2-助你拍
     *
     * @see
     */
    @ApiModelProperty("场景 1-助你营，2-助你拍")
    private Integer scene;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date modifyTime;
}
