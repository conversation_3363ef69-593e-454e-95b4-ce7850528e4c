CREATE TABLE `category`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `biz_id`         bigint                DEFAULT NULL COMMENT '业务id',
    `category_type`  tinyint      NOT NULL DEFAULT '0' COMMENT '业务分类：0 默认； 1 海报；3 文章； 4 网页；5 视频；6 文件；7 文本；8 图片 ; 11 群sop; 12 个人sop;',
    `name`           varchar(128) NOT NULL DEFAULT '' COMMENT '分类名称',
    `category_level` tinyint      NOT NULL DEFAULT '0' COMMENT '级别： 0 一级（默认）； 1 二级 ；2 三级',
    `parent_id`      bigint                DEFAULT '0' COMMENT '父级分类id',
    `link`           varchar(128) NOT NULL DEFAULT '' COMMENT '分类级别路径，以|区分',
    `create_dt`      datetime     NOT NULL COMMENT '创建时间',
    `create_by`      bigint       NOT NULL COMMENT '创建人user_id',
    `modify_dt`      datetime     NOT NULL COMMENT '修改时间',
    `modify_by`      bigint       NOT NULL COMMENT '修改人user_id',
    `is_deleted`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0否 1是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_bizId` (`biz_id`) USING BTREE,
    KEY              `idx_type_name` (`category_type`,`name`),
    KEY              `idx_parent_id` (`parent_id`),
    KEY              `idx_name` (`name`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='资源分类表';


CREATE TABLE `tenant_auth`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `tenant_code` varchar(15) NOT NULL COMMENT '租户编号',
    `public_key`  varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '公钥',
    `private_key` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '私钥',
    `status`      tinyint     NOT NULL COMMENT '状态，1-已启用，2-已停用',
    `is_deleted`  tinyint     NOT NULL COMMENT '是否删除',
    `create_dt`   datetime    NOT NULL COMMENT '创建时间',
    `create_by`   bigint                                                         DEFAULT NULL COMMENT '创建人',
    `modify_dt`   datetime    NOT NULL COMMENT '修改时间',
    `modify_by`   bigint                                                         DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_tenantCode` (`tenant_code`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='租户授权表';


CREATE TABLE `material`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `biz_id`         bigint unsigned NOT NULL COMMENT '业务主键',
    `title`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '标题',
    `remark`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL COMMENT '摘要',
    `logo_img`       varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL COMMENT 'logo图',
    `category_ids`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci                DEFAULT '0' COMMENT '分类id，多个逗号分隔',
    `material_type`  tinyint unsigned NOT NULL COMMENT '素材类型：1文章，2网页，3视频，4文件，5文本，6图片',
    `article_type`   tinyint                                                       NOT NULL DEFAULT '0' COMMENT '文章类型：0 自建文章；1 微信公众号文章',
    `source_url`     varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '' COMMENT '文章源访问地址',
    `content`        longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '内容',
    `size`           int unsigned DEFAULT NULL COMMENT '素材大小，文件类型有值',
    `publish_status` int                                                                    DEFAULT NULL COMMENT '发布状态，1-已发布，2-取消发布',
    `source`         varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci                 DEFAULT NULL COMMENT '来源',
    `create_dt`      datetime                                                      NOT NULL COMMENT '创建时间',
    `create_by`      bigint unsigned NOT NULL COMMENT '创建人ID',
    `creator_name`   varchar(64)                                                            DEFAULT NULL COMMENT '创建人名称',
    `modify_dt`      datetime                                                      NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `modify_by`      bigint unsigned NOT NULL COMMENT '修改人ID',
    `modify_name`    varchar(64)                                                            DEFAULT NULL COMMENT '修改人名称',
    `is_deleted`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0否 1是',
    `create_from`    tinyint                                                       NOT NULL DEFAULT '0' COMMENT '添加来源：0资源中心，1本地上传',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_biz_id` (`biz_id`) USING BTREE,
    KEY              `idx_create_by` (`create_by`) USING BTREE,
    KEY              `idx_title` (`title`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='素材表';


CREATE TABLE `script`
(
    `id`             bigint                                                         NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `script_id`      bigint                                                         NOT NULL COMMENT '话术id',
    `question`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '问题',
    `content`        varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容',
    `category1`      bigint unsigned DEFAULT '0' COMMENT '一级分类id',
    `category2`      bigint unsigned DEFAULT '0' COMMENT '二级分类id',
    `publish_status` int                                                            NOT NULL COMMENT '发布状态，1-已发布，2-取消发布',
    `source`         varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '来源',
    `create_dt`      datetime                                                       NOT NULL COMMENT '创建时间',
    `create_by`      bigint                                                         NOT NULL COMMENT '创建人id',
    `creator_name`   varchar(64)                                            DEFAULT NULL COMMENT '创建人名称',
    `modify_dt`      datetime                                                       NOT NULL COMMENT '修改时间',
    `modify_by`      bigint                                                         NOT NULL COMMENT '修改人id',
    `modify_name`    varchar(64)                                            DEFAULT NULL COMMENT '修改名称',
    `is_deleted`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除  0否 1是',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_script_id` (`script_id`) USING BTREE,
    KEY              `idx_question` (`question`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='话术主体表';


CREATE TABLE `script_material`
(
    `id`          bigint   NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `script_id`   bigint   NOT NULL COMMENT '话术id',
    `material_id` bigint   NOT NULL COMMENT '素材id',
    `is_deleted`  tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `create_dt`   datetime NOT NULL,
    `modify_dt`   datetime NOT NULL,
    `create_by`   bigint   NOT NULL,
    `modify_by`   bigint   NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_script_id` (`script_id`),
    KEY           `idx_material_id` (`material_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='话术和素材关联表';


CREATE TABLE `sop`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `sop_id`       bigint                                                        NOT NULL COMMENT '业务id',
    `name`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '名称',
    `remark`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci                DEFAULT NULL COMMENT '描述',
    `category1`    bigint                                                                 DEFAULT NULL COMMENT '所属一级分类id',
    `category2`    bigint                                                                 DEFAULT NULL COMMENT '所属二级分类',
    `status`       tinyint                                                       NOT NULL COMMENT 'sop状态：0草稿  8已发布 9取消发布',
    `sop_type`     tinyint(1) NOT NULL DEFAULT '1' COMMENT 'sop类型：1个人sop  2群sop',
    `source`       varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci           DEFAULT NULL COMMENT '来源',
    `create_dt`    datetime                                                      NOT NULL COMMENT '创建时间',
    `create_by`    bigint                                                        NOT NULL COMMENT '创建人',
    `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci           DEFAULT NULL COMMENT '创建人姓名',
    `modify_dt`    datetime                                                      NOT NULL COMMENT '修改时间',
    `modify_by`    bigint                                                        NOT NULL COMMENT '修改人',
    `modify_name`  varchar(64)                                                            DEFAULT NULL COMMENT '修改人名称',
    `is_deleted`   tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除  0否1是',
    `delete_dt`    bigint                                                                 DEFAULT '1' COMMENT '删除时间，默认为1，逻辑删除时存删除时的时间戳',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_sop_id` (`sop_id`),
    UNIQUE KEY `uniq_name_tenantCode_deleteDt` (`name`,`delete_dt`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='sop主体表';


CREATE TABLE `sop_event`
(
    `id`              bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `event_id`        bigint                                                        NOT NULL COMMENT '事项id',
    `sop_id`          bigint                                                        NOT NULL COMMENT 'sop ID',
    `name`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci       NOT NULL DEFAULT '' COMMENT '事项名称',
    `remark`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci                DEFAULT NULL COMMENT '事项描述',
    `reach_type`      tinyint                                                       NOT NULL COMMENT '触达形式 1群发  2任务下发 3纯提醒  4企微朋友圈群发  5修改旅程阶段  6一对一私聊 7打开群聊发送',
    `content`         varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '发送内容',
    `is_deleted`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `rule_type`       tinyint(1) NOT NULL DEFAULT '1' COMMENT '时间规则类型 1定时推送   2周期推送',
    `rule_content`    varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci                DEFAULT NULL COMMENT '规则内容，定时推送时为天-HH:mm:ss  周期推送为首次天-天-HH:mm:ss',
    `create_dt`       datetime                                                               DEFAULT NULL COMMENT '创建时间',
    `create_by`       bigint                                                        NOT NULL COMMENT '创建人',
    `modify_dt`       datetime                                                               DEFAULT NULL COMMENT '修改时间',
    `modify_by`       bigint                                                        NOT NULL COMMENT '修改人',
    `validity_period` int                                                                    DEFAULT NULL COMMENT '有效期',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_event_id` (`event_id`),
    KEY               `idx_sop_id` (`sop_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='sop事件表';


CREATE TABLE `sop_event_cnt`
(
    `id`         bigint unsigned NOT NULL AUTO_INCREMENT,
    `event_id`   bigint NOT NULL,
    `cnt_type`   tinyint(1) NOT NULL COMMENT '内容类型 1素材 2海报',
    `cnt_id`     bigint NOT NULL COMMENT '内容id',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `create_dt`  datetime DEFAULT NULL,
    `create_by`  bigint NOT NULL,
    `modify_dt`  datetime DEFAULT NULL,
    `modify_by`  bigint NOT NULL,
    PRIMARY KEY (`id`),
    KEY          `idx_event_id` (`event_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='sop事件和内容关联表';


CREATE TABLE `pack`
(
    `id`                   bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `pack_id`              bigint      NOT NULL COMMENT '服务包id',
    `title`                varchar(50) NOT NULL COMMENT '标题',
    `category1`            bigint                                                        DEFAULT NULL COMMENT '所属一级分类',
    `category2`            bigint                                                        DEFAULT NULL COMMENT '所属二级分类',
    `scene_overview`       varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场景概述',
    `domain`               tinyint                                                       DEFAULT NULL COMMENT '适用行业，1-通用,2-银行,3-券商,4-基金,5-其他',
    `detailed_description` varchar(200)                                                  DEFAULT NULL COMMENT '详细描述',
    `suggest`              varchar(200)                                                  DEFAULT NULL COMMENT '运营投放建议',
    `status`               tinyint                                                       DEFAULT NULL COMMENT '状态,0-草稿，3-已发布,4-取消发布',
    `scene`                tinyint     NOT NULL COMMENT '场景 1-助你营，2-助你拍',
    `is_deleted`           tinyint     NOT NULL                                          DEFAULT '0' COMMENT '是否删除，0-否，1-是',
    `create_dt`            datetime    NOT NULL COMMENT '创建时间',
    `create_by`            bigint      NOT NULL COMMENT '创建人',
    `creator_name`         varchar(64)                                                   DEFAULT NULL COMMENT '创建人名称',
    `modify_dt`            datetime    NOT NULL COMMENT '修改时间',
    `modify_by`            bigint      NOT NULL COMMENT '修改人',
    `modify_name`          varchar(64)                                                   DEFAULT NULL COMMENT '修改人名称',
    `source`               varchar(20)                                                   DEFAULT NULL COMMENT '来源',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_packid` (`pack_id`) USING BTREE,
    KEY                    `idx_modifydt` (`modify_dt`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务包信息表';


CREATE TABLE `pack_chain_path`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `pack_id`       bigint   NOT NULL COMMENT '服务包id',
    `chain_path_id` bigint   NOT NULL COMMENT '链路id',
    `name`          varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '链路名称',
    `sort`          tinyint unsigned NOT NULL COMMENT '排序',
    `is_deleted`    tinyint  NOT NULL                                            DEFAULT '0' COMMENT '是否删除，0-否，1-是',
    `create_dt`     datetime NOT NULL COMMENT '创建时间',
    `create_by`     bigint                                                       DEFAULT NULL COMMENT '创建人',
    `modify_dt`     datetime NOT NULL COMMENT '修改时间',
    `modify_by`     bigint                                                       DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_chainpathid` (`chain_path_id`) USING BTREE,
    KEY             `idx_packid` (`pack_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务包链路表';


CREATE TABLE `pack_branch`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `chain_path_id` bigint                                                       NOT NULL COMMENT '链路id',
    `branch_id`     bigint                                                       NOT NULL COMMENT '分支id',
    `name`          varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分支名称',
    `sort`          tinyint                                                      NOT NULL COMMENT '排序',
    `is_deleted`    tinyint                                                      NOT NULL DEFAULT '0' COMMENT '是否删除，0-否，1-是',
    `create_dt`     datetime                                                     NOT NULL COMMENT '创建时间',
    `create_by`     bigint                                                                DEFAULT NULL COMMENT '创建人',
    `modify_dt`     datetime                                                     NOT NULL COMMENT '修改时间',
    `modify_by`     bigint                                                                DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_branchid` (`branch_id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务包分支表';


CREATE TABLE `pack_element`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `element_id`    bigint   NOT NULL COMMENT '元素id',
    `pack_id`       bigint   NOT NULL COMMENT '服务包id',
    `chain_path_id` bigint                                                         DEFAULT NULL COMMENT '链路id',
    `branch_id`     bigint                                                         DEFAULT NULL COMMENT '分支id',
    `type`          int      NOT NULL COMMENT '元素类型,1-文章,2-网页,3-视频,4-文件,5-文本,6-图片,8-话术,9-海报,10-指定产品,11-产品筛选范围',
    `sort`          tinyint unsigned NOT NULL COMMENT '排序',
    `title`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '标题',
    `content`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文字内容',
    `attachments`   varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '附件',
    `ext_data`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '扩展数据',
    `is_deleted`    tinyint  NOT NULL                                              DEFAULT '0' COMMENT '是否删除，0-否，1-是',
    `create_dt`     datetime NOT NULL COMMENT '创建时间',
    `create_by`     bigint                                                         DEFAULT NULL COMMENT '创建人',
    `modify_dt`     datetime NOT NULL COMMENT '修改时间',
    `modify_by`     bigint                                                         DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_elementid` (`element_id`) USING BTREE,
    KEY             `idx_packid_chainpathid_branchid` (`pack_id`,`chain_path_id`,`branch_id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务包元素表';
