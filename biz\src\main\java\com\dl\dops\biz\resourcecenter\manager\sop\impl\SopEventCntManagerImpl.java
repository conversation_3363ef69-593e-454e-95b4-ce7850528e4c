package com.dl.dops.biz.resourcecenter.manager.sop.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.resourcecenter.dal.sop.SopEventCntMapper;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventCntBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventCntSaveBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventSaveBO;
import com.dl.dops.biz.resourcecenter.dal.sop.po.SopEventCntPO;
import com.dl.dops.biz.resourcecenter.manager.sop.SopEventCntManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-07 10:37
 */
@Component
public class SopEventCntManagerImpl extends ServiceImpl<SopEventCntMapper, SopEventCntPO>
        implements SopEventCntManager {

    @Resource
    private OperatorUtil operatorUtil;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void batchSave(List<SopEventSaveBO> saveBOList) {
        if (CollectionUtils.isEmpty(saveBOList)) {
            return;
        }
        //提取事件id，把老的都删了。
        List<Long> eventIdList = saveBOList.stream().map(SopEventSaveBO::getEventId).collect(Collectors.toList());
        baseMapper.batchLogicDeleteByEventIds(eventIdList, operatorUtil.getOperator());

        //再批量插入
        //需要新增的列表
        List<SopEventCntPO> addList = new ArrayList<>();
        for (SopEventSaveBO saveBO : saveBOList) {
            if (CollectionUtils.isEmpty(saveBO.getCntSaveBOList())) {
                continue;
            }
            for (SopEventCntSaveBO cntSaveBO : saveBO.getCntSaveBOList()) {
                SopEventCntPO addPO = new SopEventCntPO();
                addPO.setEventId(saveBO.getEventId());
                addPO.setCntId(cntSaveBO.getCntId());
                addPO.setCntType(cntSaveBO.getCntType());
                addList.add(addPO);
            }
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            baseMapper.batchInsert(addList);
        }
    }

    @Override
    public void save(Long eventId, List<SopEventCntSaveBO> saveBOList) {
        Assert.isTrue(Objects.nonNull(eventId), "eventId不能为空");

        List<SopEventCntPO> exisitEventCntPOList = baseMapper.selectList(
                Wrappers.lambdaQuery(SopEventCntPO.class).eq(SopEventCntPO::getEventId, eventId)
                        .eq(SopEventCntPO::getIsDeleted, Const.ZERO));

        //为空，则新增
        if (CollectionUtils.isEmpty(exisitEventCntPOList)) {
            List<SopEventCntPO> insertList = saveBOList.stream().map(bo -> {
                SopEventCntPO po = new SopEventCntPO();
                po.setEventId(eventId);
                po.setCntId(bo.getCntId());
                po.setCntType(bo.getCntType());
                return po;
            }).collect(Collectors.toList());
            //批量新增
            baseMapper.batchInsert(insertList);
            return;
        }

        //需要新增的列表
        List<SopEventCntPO> needToAddList = calNeedToAddList(saveBOList, exisitEventCntPOList);
        //需要逻辑删除的id列表
        List<Long> needToDeleteIdList = calNeedToDeleteIdList(saveBOList, exisitEventCntPOList);

        //批量新增
        if (CollectionUtils.isNotEmpty(needToAddList)) {
            baseMapper.batchInsert(needToAddList);
        }

        //批量逻辑删除
        if (CollectionUtils.isNotEmpty(needToDeleteIdList)) {
            baseMapper.batchLogicDeleteByIds(needToDeleteIdList, operatorUtil.getOperator());
        }

    }

    @Override
    public List<SopEventCntBO> listByEventId(Long eventId) {
        Assert.isTrue(Objects.nonNull(eventId), "eventId不能为空");
        List<SopEventCntPO> poList = baseMapper.selectList(
                Wrappers.lambdaQuery(SopEventCntPO.class).eq(SopEventCntPO::getEventId, eventId)
                        .eq(SopEventCntPO::getIsDeleted, 0));

        return poList.stream().map(SopEventCntManagerImpl::cnvSopEventCntPO2DTO).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public void logicDelete(Long eventId) {
        Assert.isTrue(Objects.nonNull(eventId), "eventId不能为空");
        SopEventCntPO updatePO = new SopEventCntPO();
        updatePO.setIsDeleted(1);

        baseMapper.update(updatePO, Wrappers.lambdaUpdate(SopEventCntPO.class).eq(SopEventCntPO::getEventId, eventId));
    }

    @Override
    public List<SopEventCntBO> listByEventIdList(List<Long> eventIdList) {
        if (CollectionUtils.isEmpty(eventIdList)) {
            return Collections.emptyList();
        }
        List<SopEventCntPO> poList = baseMapper.selectList(
                Wrappers.lambdaQuery(SopEventCntPO.class).in(SopEventCntPO::getEventId, eventIdList)
                        .eq(SopEventCntPO::getIsDeleted, Const.ZERO));

        return poList.stream().map(SopEventCntManagerImpl::cnvSopEventCntPO2DTO).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<SopEventCntBO>> getEventCntMap(List<Long> eventIdList) {
        if (CollectionUtils.isEmpty(eventIdList)) {
            return Collections.emptyMap();
        }
        List<SopEventCntPO> poList = baseMapper.selectList(
                Wrappers.lambdaQuery(SopEventCntPO.class).in(SopEventCntPO::getEventId, eventIdList)
                        .eq(SopEventCntPO::getIsDeleted, 0));

        return poList.stream().map(SopEventCntManagerImpl::cnvSopEventCntPO2DTO).filter(Objects::nonNull)
                .collect(Collectors.groupingBy(SopEventCntBO::getEventId));
    }

    /**
     * 计算需要新增的列表
     *
     * @param list
     * @param exisitSopEventCntPOList
     * @return
     */
    private static List<SopEventCntPO> calNeedToAddList(List<SopEventCntSaveBO> list,
            List<SopEventCntPO> exisitSopEventCntPOList) {
        Set<Long> exisitCntIdSet = exisitSopEventCntPOList.stream().map(SopEventCntPO::getCntId)
                .collect(Collectors.toSet());
        //需要新增的列表
        List<SopEventCntPO> needToAddList = list.stream().map(bo -> {
            if (!exisitCntIdSet.contains(bo.getCntId())) {
                SopEventCntPO po = new SopEventCntPO();
                po.setEventId(bo.getEventId());
                po.setCntId(bo.getCntId());
                po.setCntType(bo.getCntType());
                return po;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return needToAddList;
    }

    /**
     * 计算需要逻辑删除的id列表
     *
     * @param list
     * @param exisitSopEventCntPOList
     * @return
     */
    private static List<Long> calNeedToDeleteIdList(List<SopEventCntSaveBO> list,
            List<SopEventCntPO> exisitSopEventCntPOList) {
        Set<Long> saveCntIdSet = list.stream().map(SopEventCntSaveBO::getCntId).collect(Collectors.toSet());
        List<Long> needToDeleteIdList = exisitSopEventCntPOList.stream().map(po -> {
            if (!saveCntIdSet.contains(po.getCntId())) {
                return po.getId();
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return needToDeleteIdList;
    }

    private static SopEventCntBO cnvSopEventCntPO2DTO(SopEventCntPO input) {
        if (Objects.isNull(input)) {
            return null;
        }
        SopEventCntBO result = new SopEventCntBO();
        result.setEventId(input.getEventId());
        result.setCntId(input.getCntId());
        result.setCntType(input.getCntType());
        return result;
    }
}
