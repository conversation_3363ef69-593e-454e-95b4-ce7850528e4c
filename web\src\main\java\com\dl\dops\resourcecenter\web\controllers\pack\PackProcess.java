package com.dl.dops.resourcecenter.web.controllers.pack;

import cn.easyes.core.biz.PageInfo;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.resourcecenter.dal.category.po.CategoryPO;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackBranchPO;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackChainPathPO;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackElementPO;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackPO;
import com.dl.dops.biz.resourcecenter.es.pack.po.EsIndexRcPack;
import com.dl.dops.biz.resourcecenter.manager.category.CategoryManager;
import com.dl.dops.biz.resourcecenter.manager.material.MaterialManager;
import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialBO;
import com.dl.dops.biz.resourcecenter.manager.pack.*;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackAddBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackBranchBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackBranchSaveBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackChainPathBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackElementAttachmentBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackElementBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackSearchBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackUpdateBO;
import com.dl.dops.biz.resourcecenter.manager.poster.PosterManager;
import com.dl.dops.biz.resourcecenter.manager.poster.bo.PosterBO;
import com.dl.dops.resourcecenter.pack.dto.RcPackBranchDetailDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackDetailDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackDomainDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackPageDTO;
import com.dl.dops.resourcecenter.pack.dto.RcPackSaveRespDTO;
import com.dl.dops.resourcecenter.pack.dto.RcRcPackChainPathDetailDTO;
import com.dl.dops.resourcecenter.pack.enums.RcPackDomainEnum;
import com.dl.dops.resourcecenter.pack.enums.RcPackStatusEnum;
import com.dl.dops.resourcecenter.pack.param.RcPackAddParam;
import com.dl.dops.resourcecenter.pack.param.RcPackBranchSaveParam;
import com.dl.dops.resourcecenter.pack.param.RcPackChainPathContentSaveParam;
import com.dl.dops.resourcecenter.pack.param.RcPackElementSaveParam;
import com.dl.dops.resourcecenter.pack.param.RcPackPageQueryParam;
import com.dl.dops.resourcecenter.pack.param.RcPackUpdateParam;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.resourcecenter.web.controllers.pack.convert.PackConvert;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class PackProcess extends AbstractController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PackProcess.class);

    @Resource
    private PackManager packManager;
    @Resource
    private PackChainPathManager packChainPathManager;
    @Resource
    private PackBranchManager packBranchManager;
    @Resource
    private PackElementManager packElementManager;
    @Resource
    private CategoryManager categoryManager;
    @Resource
    private MaterialManager materialManager;
    @Resource
    private PosterManager posterManager;
    @Resource
    private HostTimeIdg hostTimeIdg;

    @Autowired
    private PackSearchManager packSearchManager;

    /**
     * 服务包适用行业vo列表
     */
    private static List<RcPackDomainDTO> rcPackDomainDTOList;

    static {
        rcPackDomainDTOList = new ArrayList<>();
        for (RcPackDomainEnum domainEnum : RcPackDomainEnum.values()) {
            RcPackDomainDTO domainVO = new RcPackDomainDTO();
            domainVO.setDomain(domainEnum.getDomain());
            domainVO.setName(domainEnum.getName());
            rcPackDomainDTOList.add(domainVO);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    public ResultModel<RcPackSaveRespDTO> add(RcPackAddParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getCategory1()), "所属一级分类有误");

        //1.保存服务包基本信息
        PackAddBO saveBO = PackConvert.cnvPackAddParam2BO(param);
        Long packId = packManager.add(saveBO);

        //2.保存服务包链路
        List<PackChainPathBO> chainPathBOList = packChainPathManager
                .batchSave(packId, PackConvert.cnvPackChainPathSaveParam2BO(param.getChainPathList()));

        return ResultModel.success(PackConvert.buildPackSaveRespDTO(packId, chainPathBOList));
    }

    @Transactional(rollbackFor = Throwable.class)
    public ResultModel<RcPackSaveRespDTO> edit(RcPackUpdateParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getCategory1()), "所属一级分类有误");

        //1.修改服务包基本信息
        PackUpdateBO saveBO = PackConvert.cnvPackUpdateParam2BO(param);
        packManager.edit(saveBO);

        //2.保存服务包链路
        List<PackChainPathBO> chainPathDTOList = packChainPathManager
                .batchSave(saveBO.getPackId(), PackConvert.cnvPackChainPathSaveParam2BO(param.getChainPathList()));

        return ResultModel.success(PackConvert.buildPackSaveRespDTO(saveBO.getPackId(), chainPathDTOList));
    }

    public ResultPageModel<RcPackPageDTO> page(RcPackPageQueryParam param) {
        PackSearchBO bo = new PackSearchBO();
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            bo.setCreatorUserId(Long.valueOf(param.getCreateUserId()));
        }

        if (StringUtils.isNotBlank(param.getCategory1())) {
            bo.setCategory1(Long.valueOf(param.getCategory1()));
        }
        if (StringUtils.isNotBlank(param.getCategory2())) {
            bo.setCategory2(Long.valueOf(param.getCategory2()));
        }
        bo.setStatus(param.getStatus());
        bo.setDomain(param.getDomain());
        bo.setScene(param.getScene());
        bo.setTitle(param.getTitle());
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());

        //没有标题走db
        if (StringUtils.isBlank(bo.getTitle())){
            IPage<PackBO> page = packManager.pageQuery(bo);
            if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getRecords())) {
                return pageQueryModel(page, Collections.emptyList());
            }
            List<RcPackPageDTO> data = page.getRecords().stream().map(PackConvert::cnvPackBO2PageDTO)
                    .collect(Collectors.toList());
            return pageQueryModel(page, data);
        }

        //有标题走es
        IPage<RcPackPageDTO> page = new Page<>();
        PageInfo<EsIndexRcPack> esIndexPackPageInfo = packManager.pageFromEs(bo);
        page.setCurrent(esIndexPackPageInfo.getPageNum());
        page.setSize(esIndexPackPageInfo.getPageSize());
        page.setTotal(esIndexPackPageInfo.getTotal());
        page.setPages(esIndexPackPageInfo.getPages());
        List<RcPackPageDTO> packPageDTOList = esIndexPackPageInfo.getList().stream().map(item -> {

            RcPackPageDTO rcPackPageDTO = PackConvert.cnvEsIndexPack2PackPageDTO(item);
            return rcPackPageDTO;
        }).collect(Collectors.toList());

        page.setRecords(packPageDTOList);

        if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getRecords())) {
            return pageQueryModel(page, Collections.emptyList());
        }
        return pageQueryModel(page, packPageDTOList);

    }

    public ResultModel<RcPackDTO> info(Long packId) {
        //查询服务包信息
        PackBO dto = packManager.info(packId);
        Assert.notNull(dto, "服务包不存在");
        //查询分类名称
        Map<Long, String> categoryNameMap = this.queryCategoryNameMap(dto);
        return ResultModel.success(PackConvert.buildPackDTO(dto, categoryNameMap));
    }

    @Transactional(rollbackFor = Throwable.class)
    public ResultModel<Boolean> delete(Long packId) {
        //1.查询服务包
        PackBO dto = packManager.info(packId);
        Assert.notNull(dto, "服务包不存在");
        Assert.isTrue(!RcPackStatusEnum.RELEASED.getStatus().equals(dto.getStatus()), "该服务包已发布，请先将状态改为取消发布再进行删除操作");

        //2.查询链路列表
        List<PackChainPathPO> chainPathPOList = packChainPathManager
                .list(Wrappers.lambdaQuery(PackChainPathPO.class).eq(PackChainPathPO::getPackId, packId)
                        .eq(PackChainPathPO::getIsDeleted, Const.ZERO));
        List<Long> chainPathIdList = chainPathPOList.stream().map(PackChainPathPO::getChainPathId)
                .collect(Collectors.toList());

        //3.删除服务包
        packManager.update(Wrappers.lambdaUpdate(PackPO.class).eq(PackPO::getPackId, packId)
                .set(PackPO::getIsDeleted, Const.ONE));

        //若链路为空，直接返回
        if (CollectionUtils.isEmpty(chainPathIdList)) {
            return ResultModel.success(Boolean.TRUE);
        }

        //4.删除链路
        packChainPathManager.update(Wrappers.lambdaUpdate(PackChainPathPO.class)
                .in(PackChainPathPO::getChainPathId, chainPathIdList).set(PackChainPathPO::getIsDeleted, Const.ONE));
        //5.删除分支
        packBranchManager
                .update(Wrappers.lambdaUpdate(PackBranchPO.class).in(PackBranchPO::getChainPathId, chainPathIdList)
                        .eq(PackBranchPO::getIsDeleted, Const.ZERO).set(PackBranchPO::getIsDeleted, Const.ONE));
        //6.删除元素
        packElementManager.update(Wrappers.lambdaUpdate(PackElementPO.class).eq(PackElementPO::getPackId, packId)
                .eq(PackElementPO::getIsDeleted, Const.ZERO).set(PackElementPO::getIsDeleted, Const.ONE));

        return ResultModel.success(Boolean.TRUE);
    }

    @Transactional(rollbackFor = Throwable.class)
    public ResultModel<RcRcPackChainPathDetailDTO> saveChainPathContent(RcPackChainPathContentSaveParam saveParam) {
        //1.参数校验
        this.checkPackChainPathContentSaveParam(saveParam);

        //2.查询链路信息
        PackChainPathBO chainPathBO = packChainPathManager.getByChainPathId(Long.valueOf(saveParam.getChainPathId()));
        Assert.notNull(chainPathBO, "链路不存在");
        RcRcPackChainPathDetailDTO chainPathDetailDTO = new RcRcPackChainPathDetailDTO();
        PackConvert.fillPackChainPathDTO(chainPathBO, chainPathDetailDTO);

        //3.根据packId+chainPathId 查询已有的元素
        List<PackElementPO> existElementPOList = packElementManager
                .list(Wrappers.lambdaQuery(PackElementPO.class).eq(PackElementPO::getPackId, chainPathBO.getPackId())
                        .eq(PackElementPO::getChainPathId, chainPathBO.getChainPathId())
                        .eq(PackElementPO::getIsDeleted, Const.ZERO));

        //4.1 入参无真实的分支
        if (saveParam.getPackBranchList().size() == 1 && StringUtils
                .isBlank(saveParam.getPackBranchList().get(0).getName()) && StringUtils
                .isBlank(saveParam.getPackBranchList().get(0).getBranchId())) {
            List<RcPackBranchDetailDTO> branchDetailDTOList = this
                    .handleSaveFakeBranch(chainPathBO, existElementPOList, saveParam);
            chainPathDetailDTO.setPackBranchList(branchDetailDTOList);
            return ResultModel.success(chainPathDetailDTO);
        }

        //4.2 入参有真实的分支
        List<RcPackBranchDetailDTO> branchDetailDTOList = this
                .handleSaveRealBranchList(chainPathBO, existElementPOList, saveParam);
        chainPathDetailDTO.setPackBranchList(branchDetailDTOList);
        return ResultModel.success(chainPathDetailDTO);
    }

    public ResultModel<List<RcRcPackChainPathDetailDTO>> chainPathDetailList(Long packId) {
        //1.查询服务包信息
        PackBO packBO = packManager.info(packId);
        Assert.notNull(packBO, "服务包不存在");

        //2.查询链路信息
        List<PackChainPathBO> chainPathBOList = packChainPathManager.listByPackId(packBO.getPackId());
        if (CollectionUtils.isEmpty(chainPathBOList)) {
            return ResultModel.success(Collections.emptyList());
        }

        //3.查询分支map，key-链路id，value-对应的分支列表
        Map<Long, List<PackBranchBO>> chainPathBranchBOMap = packBranchManager.mapByChainPathIds(
                chainPathBOList.stream().map(PackChainPathBO::getChainPathId).collect(Collectors.toList()));

        //4.查询元素列表
        List<PackElementBO> elementBOList = packElementManager.listByPackId(packBO.getPackId(), null);
        //将元素根据链路id分组
        Map<Long, List<PackElementBO>> elementBOMap = elementBOList.stream()
                .collect(Collectors.groupingBy(PackElementBO::getChainPathId));

        //5.查询元素中的素材和海报
        Map<String, MaterialBO> materialBOMap = new HashMap<>();
        Map<String, PosterBO> posterBOMap = new HashMap<>();
        this.distinguishAttachmentAndQueryDetail(elementBOList, materialBOMap, posterBOMap);

        //链路vo列表
        List<RcRcPackChainPathDetailDTO> chainPathDetailDTOList = new ArrayList<>();

        LOGGER.info("打印日志1！！！！！ packId:{},,,,chainPathBOList:{}", packId, JSONUtil.toJsonStr(chainPathBOList));

        //6.装换并填充链路vo详情列表
        this.cnvAndfillChainPathDetailDTOList(chainPathBranchBOMap, elementBOMap, chainPathDetailDTOList,
                chainPathBOList, materialBOMap, posterBOMap);
        LOGGER.info("打印日志3！！！！！！ packId:{},,,,chainPathDetailDTOList:{}", packId,
                JSONUtil.toJsonStr(chainPathDetailDTOList));
        return ResultModel.success(chainPathDetailDTOList);
    }

    public ResultModel<RcPackDetailDTO> detail(Long packId){
        //1.查询服务包信息
        PackBO packBO = packManager.info(packId);
        Assert.notNull(packBO, "服务包不存在");

        //查询分类名称
        Map<Long, String> categoryNameMap = this.queryCategoryNameMap(packBO);
        RcPackDetailDTO packDetailDTO = new RcPackDetailDTO();
        PackConvert.fillPackDTO(packBO,packDetailDTO, categoryNameMap);

        //2.查询链路信息
        List<PackChainPathBO> chainPathDTOList = packChainPathManager.listByPackId(packBO.getPackId());
        if (CollectionUtils.isEmpty(chainPathDTOList)) {
            return ResultModel.success(packDetailDTO);
        }

        //3.查询分支map，key-链路id，value-对应的分支列表
        Map<Long, List<PackBranchBO>> chainPathBranchBOMap = packBranchManager.mapByChainPathIds(
                chainPathDTOList.stream().map(PackChainPathBO::getChainPathId).collect(Collectors.toList()));

        //4.查询元素列表
        List<PackElementBO> elementBOList = packElementManager.listByPackId(packBO.getPackId(), null);
        //将元素根据链路id分组
        Map<Long, List<PackElementBO>> elementBOMap = elementBOList.stream()
                .collect(Collectors.groupingBy(PackElementBO::getChainPathId));

        //5.查询元素中的素材和海报
        Map<String, MaterialBO> materialBOMap = new HashMap<>();
        Map<String, PosterBO> posterBOMap = new HashMap<>();
        this.distinguishAttachmentAndQueryDetail(elementBOList, materialBOMap, posterBOMap);

        //链路vo列表
        List<RcRcPackChainPathDetailDTO> chainPathDetailDTOList = new ArrayList<>();

        //6.装换并填充链路vo详情列表
        this.cnvAndfillChainPathDetailDTOList(chainPathBranchBOMap, elementBOMap, chainPathDetailDTOList,
                chainPathDTOList, materialBOMap, posterBOMap);

        packDetailDTO.setPackChainPathDetailList(chainPathDetailDTOList);
        return ResultModel.success(packDetailDTO);
    }

    public ResultModel<List<RcPackDomainDTO>> listDomain() {
        return ResultModel.success(rcPackDomainDTOList);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultModel publishOrCancel(Long packId) {
        Assert.isTrue(Objects.nonNull(packId), "服务包id不能为空");
        PackPO tpl = packManager.getOne(Wrappers.lambdaQuery(PackPO.class).eq(PackPO::getPackId, packId));
        Assert.notNull(tpl, "服务包模板不存在");

        Integer needToBeStatus = RcPackStatusEnum.CANCEL_RELEASED.getStatus().equals(tpl.getStatus()) ?
                RcPackStatusEnum.RELEASED.getStatus() :
                RcPackStatusEnum.CANCEL_RELEASED.getStatus();

        //如果变更为发布状态，需要校验下服务包是否有链路和元素
        if (RcPackStatusEnum.RELEASED.getStatus().equals(needToBeStatus)) {
            List<PackChainPathBO> tplChainPathList = packChainPathManager.listByPackId(packId);
            Assert.isTrue(CollectionUtils.isNotEmpty(tplChainPathList), "服务包模板链路为空，不可发布");

            List<PackElementBO> tplElementList = packElementManager.listByPackId(packId, null);
            Assert.isTrue(CollectionUtils.isNotEmpty(tplElementList), "服务包模板元素为空，不可发布");
        }
        //修改es上的发布状态
        packSearchManager.publishOrCancel(String.valueOf(packId),needToBeStatus);
        //更新，eq条件增加修改时间
        packManager.update(Wrappers.lambdaUpdate(PackPO.class).eq(PackPO::getId, tpl.getId())
                .eq(PackPO::getModifyDt, tpl.getModifyDt()).set(PackPO::getStatus, needToBeStatus)
                .set(PackPO::getModifyDt, new Date()).set(PackPO::getModifyBy, getUserId()));
        return ResultModel.success(null);
    }

    /**
     * 查询目录名称
     *
     * @param packBO
     * @return
     */
    private Map<Long, String> queryCategoryNameMap(PackBO packBO) {
        //提取分类id列表
        Set<Long> categoryIdSet = new HashSet<>();
        if (Objects.nonNull(packBO.getCategory1())) {
            categoryIdSet.add(packBO.getCategory1());
        }
        if (Objects.nonNull(packBO.getCategory2())) {
            categoryIdSet.add(packBO.getCategory2());
        }
        if (CollectionUtils.isEmpty(categoryIdSet)) {
            return Collections.emptyMap();
        }

        if (CollectionUtils.isEmpty(categoryIdSet)) {
            return new HashMap<>();
        }
        List<CategoryPO> poList = categoryManager
                .list(Wrappers.lambdaQuery(CategoryPO.class).in(CategoryPO::getBizId, categoryIdSet)
                        .eq(CategoryPO::getIsDeleted, Const.ZERO));
        return poList.stream().collect(Collectors.toMap(CategoryPO::getBizId, CategoryPO::getName));
    }

    /**
     * 处理保存虚假分支
     * 1.之前不存在元素，则本次元素全部新增
     * 2.存在有分支的元素，则将这些元素删除，再将入参中的元素新增。
     * 3.不存在有分支的元素，则表示已存在的都是没有分支的元素，将已有元素和入参中的元素进行比较，判断出哪些元素需要新增、哪些需要修改、哪些需要删除。
     *
     * @param chainPathDTO
     * @param saveParam
     * @return
     */
    private List<RcPackBranchDetailDTO> handleSaveFakeBranch(PackChainPathBO chainPathDTO,
            List<PackElementPO> existElementPOList, RcPackChainPathContentSaveParam saveParam) {
        List<RcPackElementSaveParam> packElementSaveParamList = saveParam.getPackBranchList().get(0).getPackElementList();
        RcRcPackChainPathDetailDTO chainPathDetailVO = new RcRcPackChainPathDetailDTO();
        PackConvert.fillPackChainPathDTO(chainPathDTO, chainPathDetailVO);

        //1.之前不存在元素，则本次元素全部新增
        if (CollectionUtils.isEmpty(existElementPOList)) {
            List<PackElementBO> packElementBOList = packElementManager
                    .doBatchInsert(chainPathDTO.getPackId(), chainPathDTO.getChainPathId(), null,
                            PackConvert.cnvPackElementSaveParam2BOList(packElementSaveParamList));

            RcPackBranchDetailDTO branchDetailDTO = PackConvert
                    .buildPackBranchDetailDTO(null, packElementBOList, new HashMap<>(), new HashMap<>());
            return Collections.singletonList(branchDetailDTO);
        }
        //2.筛选出有分支的元素，这些元素需要删除
        List<Long> hasBranchElementIdList = existElementPOList.stream()
                .filter(packElementPO -> Objects.nonNull(packElementPO.getBranchId())).map(PackElementPO::getId)
                .collect(Collectors.toList());
        //若存在有分支的元素，则将这些元素删除,将链路下的分支删除，再将入参中的元素新增。
        if (CollectionUtils.isNotEmpty(hasBranchElementIdList)) {
            packElementManager
                    .update(Wrappers.lambdaUpdate(PackElementPO.class).in(PackElementPO::getId, hasBranchElementIdList)
                            .set(PackElementPO::getIsDeleted, Const.ONE));

            //将该链路下的分支逻辑删除
            packBranchManager.update(Wrappers.lambdaUpdate(PackBranchPO.class)
                    .eq(PackBranchPO::getChainPathId, chainPathDTO.getChainPathId())
                    .eq(PackBranchPO::getIsDeleted, Const.ZERO).set(PackBranchPO::getIsDeleted, Const.ONE));

            List<PackElementBO> packElementBOList = packElementManager
                    .doBatchInsert(chainPathDTO.getPackId(), chainPathDTO.getChainPathId(), null,
                            PackConvert.cnvPackElementSaveParam2BOList(packElementSaveParamList));

            RcPackBranchDetailDTO branchDetailDTO = PackConvert
                    .buildPackBranchDetailDTO(null, packElementBOList, new HashMap<>(), new HashMap<>());
            return Collections.singletonList(branchDetailDTO);
        }

        //3.若不存在有分支的元素，则表示已存在的都是没有分支的元素（二者互斥），故下面处理已存在的没有分支的元素。
        List<RcPackBranchDetailDTO> branchDetailDTOList = new ArrayList<>();
        //处理元素列表。将已有元素和入参中的元素进行比较
        this.handleElementListOfOneBranch(chainPathDTO, branchDetailDTOList, saveParam.getPackBranchList().get(0), null,
                existElementPOList);
        return branchDetailDTOList;
    }

    /**
     * 处理保存真实的分支列表
     * 1.之前不存在元素，则本次元素全部新增
     * 2.之前存在无分支的元素，则将这些元素删除。入参中的所有元素都要新增。
     * 3.之前不存在无分支的元素，则表示已有的元素都是有分支的。
     * ↳ 3.1 之前存在分支，现在入参中不含该分支，则该分支及其对应的元素都需要删除
     * ↳ 3.2 若入参中的分支id是空，则表示该分支和其对应的元素是新增的。
     * ↳ 3.3 之前存在分支，现在入参中也有该分支，则需要对其对应的元素进行比较，判断出哪些元素需要新增、哪些需要修改、哪些需要删除。
     *
     * @param chainPathBO
     * @param saveParam
     * @return
     */
    private List<RcPackBranchDetailDTO> handleSaveRealBranchList(PackChainPathBO chainPathBO,
            List<PackElementPO> existElementPOList, RcPackChainPathContentSaveParam saveParam) {
        List<RcPackBranchDetailDTO> branchDetailDTOList = new ArrayList<>();

        //1.之前不存在元素，则本次元素全部新增
        if (CollectionUtils.isEmpty(existElementPOList)) {
            for (RcPackBranchSaveParam packBranchSaveParam : saveParam.getPackBranchList()) {
                //保存分支
                PackBranchBO branchBO = this.saveBranch(chainPathBO.getChainPathId(), packBranchSaveParam);
                //保存元素
                List<PackElementBO> insertElementBOList = packElementManager
                        .doBatchInsert(chainPathBO.getPackId(), chainPathBO.getChainPathId(), branchBO.getBranchId(),
                                PackConvert.cnvPackElementSaveParam2BOList(packBranchSaveParam.getPackElementList()));
                RcPackBranchDetailDTO branchDetailDTO = PackConvert
                        .buildPackBranchDetailDTO(branchBO, insertElementBOList, new HashMap<>(), new HashMap<>());
                branchDetailDTOList.add(branchDetailDTO);
            }
            return branchDetailDTOList.stream().sorted(Comparator.comparing(RcPackBranchDetailDTO::getSort))
                    .collect(Collectors.toList());
        }

        //筛选出无分支的元素，这些元素需要删除
        List<Long> hasNoBranchElementIdList = existElementPOList.stream()
                .filter(packElementPO -> Objects.isNull(packElementPO.getBranchId())).map(PackElementPO::getId)
                .collect(Collectors.toList());

        //2若存在无分支的元素，则将这些元素删除。入参中的所有元素都要新增。
        if (CollectionUtils.isNotEmpty(hasNoBranchElementIdList)) {
            packElementManager.update(Wrappers.lambdaUpdate(PackElementPO.class)
                    .in(PackElementPO::getId, hasNoBranchElementIdList).set(PackElementPO::getIsDeleted, Const.ONE));

            for (RcPackBranchSaveParam packBranchSaveParam : saveParam.getPackBranchList()) {
                //保存分支
                PackBranchBO branchBO = this.saveBranch(chainPathBO.getChainPathId(), packBranchSaveParam);
                //保存元素
                List<PackElementBO> insertElementBOList = packElementManager
                        .doBatchInsert(chainPathBO.getPackId(), chainPathBO.getChainPathId(), branchBO.getBranchId(),
                                PackConvert.cnvPackElementSaveParam2BOList(packBranchSaveParam.getPackElementList()));
                RcPackBranchDetailDTO branchDetailDTO = PackConvert
                        .buildPackBranchDetailDTO(branchBO, insertElementBOList, new HashMap<>(), new HashMap<>());
                branchDetailDTOList.add(branchDetailDTO);
            }
            return branchDetailDTOList.stream().sorted(Comparator.comparing(RcPackBranchDetailDTO::getSort))
                    .collect(Collectors.toList());
        }

        //3.不存在无分支的元素，则表示已有的元素都是有分支的。
        // 根据分支id将已有元素分组
        Map<Long, List<PackElementPO>> existBranchIdElementListMap = existElementPOList.stream()
                .collect(Collectors.groupingBy(PackElementPO::getBranchId));
        Set<Long> exisitBranchIdSet = new HashSet<>(existBranchIdElementListMap.keySet());

        //入参中的分支id集合
        Set<Long> saveParamBranchIdSet = saveParam.getPackBranchList().stream()
                .filter(branchSaveParam -> StringUtils.isNotBlank(branchSaveParam.getBranchId()))
                .map(RcPackBranchSaveParam::getBranchId).map(Long::valueOf).collect(Collectors.toSet());
        exisitBranchIdSet.stream().forEach(exisitBranchId -> {
            //3.1若当前分支id不在入参中的分支id集合中，表示该分支被删除了。就需要删除该分支和该分支下的元素
            if (!saveParamBranchIdSet.contains(exisitBranchId)) {
                //逻辑删除分支和该分支下的元素
                this.logicDeleteBranchAndElements(chainPathBO.getPackId(), chainPathBO.getChainPathId(),
                        exisitBranchId);
                //从已有分支元素map移除该key
                existBranchIdElementListMap.remove(exisitBranchId);
            }
        });

        for (RcPackBranchSaveParam packBranchSaveParam : saveParam.getPackBranchList()) {
            //3.2若分组id为空，则表示该分支是新增的。
            if (StringUtils.isBlank(packBranchSaveParam.getBranchId())) {
                //保存分支
                PackBranchBO branchBO = this.saveBranch(chainPathBO.getChainPathId(), packBranchSaveParam);
                //保存元素
                List<PackElementBO> insertElementBOList = packElementManager
                        .doBatchInsert(chainPathBO.getPackId(), chainPathBO.getChainPathId(), branchBO.getBranchId(),
                                PackConvert.cnvPackElementSaveParam2BOList(packBranchSaveParam.getPackElementList()));
                RcPackBranchDetailDTO branchDetailDTO = PackConvert
                        .buildPackBranchDetailDTO(branchBO, insertElementBOList, new HashMap<>(), new HashMap<>());
                branchDetailDTOList.add(branchDetailDTO);
                continue;
            }
            //3.3走到这，则表示之前存在分支，现在入参中也有该分支，则需要对其对应的元素进行比较，判断出哪些元素需要新增、哪些需要修改、哪些需要删除。
            //更新分支信息
            PackBranchBO packBranchBO = this.saveBranch(chainPathBO.getChainPathId(), packBranchSaveParam);
            //下面处理其元素：判断哪些元素是新增，哪些是修改，哪些需要删除
            //获取该分支下已有的元素
            List<PackElementPO> existElementList = existBranchIdElementListMap
                    .get(Long.valueOf(packBranchSaveParam.getBranchId()));
            this.handleElementListOfOneBranch(chainPathBO, branchDetailDTOList, packBranchSaveParam, packBranchBO,
                    existElementList);
        }
        return branchDetailDTOList.stream().sorted(Comparator.comparing(RcPackBranchDetailDTO::getSort))
                .collect(Collectors.toList());
    }

    /**
     * 处理单个分支下的元素列表
     * 判断哪些元素是新增，哪些是修改，哪些需要删除
     *
     * @param chainPathBO
     * @param branchDetailVOList
     * @param packBranchSaveParam
     * @param packBranchBO
     * @param existElementList
     */
    private void handleElementListOfOneBranch(PackChainPathBO chainPathBO,
            List<RcPackBranchDetailDTO> branchDetailVOList, RcPackBranchSaveParam packBranchSaveParam,
            PackBranchBO packBranchBO, List<PackElementPO> existElementList) {
        //需要更新的元素,即有元素id的
        List<RcPackElementSaveParam> needToUpdateList = packBranchSaveParam.getPackElementList().stream()
                .filter(e -> StringUtils.isNotBlank(e.getElementId())).collect(Collectors.toList());
        //需要新增的元素，即无元素id的
        List<RcPackElementSaveParam> needToAddList = packBranchSaveParam.getPackElementList().stream()
                .filter(s -> StringUtils.isBlank(s.getElementId())).collect(Collectors.toList());

        //需要逻辑删除的链路id列表
        List<Long> needToDeleteElementIdList = calNeedToDeleteIdList(packBranchSaveParam.getPackElementList(),
                existElementList);

        //用来存放新增、修改之后的元素
        List<PackElementBO> finalPackElementBOList = new ArrayList<>();
        //批量新增
        if (CollectionUtils.isNotEmpty(needToAddList)) {
            List<PackElementBO> insertElementBOList = packElementManager
                    .doBatchInsert(chainPathBO.getPackId(), chainPathBO.getChainPathId(),
                            Objects.nonNull(packBranchBO) ? packBranchBO.getBranchId() : null,
                            PackConvert.cnvPackElementSaveParam2BOList(needToAddList));
            finalPackElementBOList.addAll(insertElementBOList);
        }
        //批量修改
        if (CollectionUtils.isNotEmpty(needToUpdateList)) {
            List<PackElementBO> updateElementBOList = packElementManager
                    .doBatchUpdate(PackConvert.cnvPackElementSaveParam2BOList(needToUpdateList));
            finalPackElementBOList.addAll(updateElementBOList);
        }

        //批量删除
        if (CollectionUtils.isNotEmpty(needToDeleteElementIdList)) {
            packElementManager.update(Wrappers.lambdaUpdate(PackElementPO.class)
                    .in(PackElementPO::getId, needToDeleteElementIdList).set(PackElementPO::getIsDeleted, Const.ONE));
        }
        RcPackBranchDetailDTO branchDetailDTO = PackConvert
                .buildPackBranchDetailDTO(packBranchBO, finalPackElementBOList, new HashMap<>(), new HashMap<>());
        branchDetailVOList.add(branchDetailDTO);
    }

    /**
     * 计算需要逻辑删除的元素主键id列表
     *
     * @param list
     * @param existElementPOList
     * @return
     */
    private static List<Long> calNeedToDeleteIdList(List<RcPackElementSaveParam> list,
            List<PackElementPO> existElementPOList) {
        Set<Long> saveElementIdSet = list.stream().filter(param -> StringUtils.isNotBlank(param.getElementId()))
                .map(RcPackElementSaveParam::getElementId).map(Long::valueOf).collect(Collectors.toSet());
        List<Long> needToDeleteIdList = existElementPOList.stream().map(po -> {
            if (!saveElementIdSet.contains(po.getElementId())) {
                return po.getId();
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return needToDeleteIdList;
    }

    /**
     * 保存分支
     *
     * @param chainPathId
     * @param branchSaveParam
     * @return
     */
    private PackBranchBO saveBranch(Long chainPathId, RcPackBranchSaveParam branchSaveParam) {
        PackBranchSaveBO saveBO = new PackBranchSaveBO();
        saveBO.setChainPathId(chainPathId);
        if (StringUtils.isNotBlank(branchSaveParam.getBranchId())) {
            saveBO.setBranchId(Long.valueOf(branchSaveParam.getBranchId()));
        }
        saveBO.setName(branchSaveParam.getName());
        saveBO.setSort(branchSaveParam.getSort());
        return packBranchManager.save(saveBO);
    }

    /**
     * 逻辑删除分支和该分支下的元素
     *
     * @param packId
     * @param chainPathId
     * @param branchId
     */
    private void logicDeleteBranchAndElements(Long packId, Long chainPathId, Long branchId) {
        //逻辑删除分支
        packBranchManager.update(Wrappers.lambdaUpdate(PackBranchPO.class).eq(PackBranchPO::getBranchId, branchId)
                .set(PackBranchPO::getIsDeleted, Const.ONE));
        //逻辑删除该分支下的元素
        packElementManager.update(Wrappers.lambdaUpdate(PackElementPO.class).eq(PackElementPO::getPackId, packId)
                .eq(PackElementPO::getChainPathId, chainPathId).eq(PackElementPO::getBranchId, branchId)
                .eq(PackElementPO::getIsDeleted, Const.ZERO).set(PackElementPO::getIsDeleted, Const.ONE));
    }

    private void distinguishAttachmentAndQueryDetail(List<PackElementBO> elementBOList,
            Map<String, MaterialBO> materialBOMap, Map<String, PosterBO> posterBOMap) {
        if (CollectionUtils.isEmpty(elementBOList)) {
            return;
        }

        List<Long> materialIdList = new ArrayList<>();
        List<Long> posterIdList = new ArrayList<>();

        elementBOList.forEach(elementDTO -> {
            if (CollectionUtils.isEmpty(elementDTO.getAttachmentList())) {
                return;
            }

            for (PackElementAttachmentBO attachmentDTO : elementDTO.getAttachmentList()) {
                if (Objects.nonNull(attachmentDTO.getMaterialId())) {
                    materialIdList.add(attachmentDTO.getMaterialId());
                } else if (Objects.nonNull(attachmentDTO.getPosterId())) {
                    posterIdList.add(attachmentDTO.getPosterId());
                }
            }
        });

        if (CollectionUtils.isNotEmpty(materialIdList)) {
            List<MaterialBO> materialDTOList = materialManager.batchDetail(materialIdList);
            for (MaterialBO materialDTO : materialDTOList) {
                materialBOMap.put(materialDTO.getMaterialId(), materialDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(posterIdList)) {
            List<PosterBO> posterDTOList = posterManager.batchDetail(posterIdList);
            for (PosterBO posterDTO : posterDTOList) {
                posterBOMap.put(posterDTO.getBizId(), posterDTO);
            }
        }

    }

    /**
     * 装换并填充链路vo详情列表
     *
     * @param chainPathBranchBOMap
     * @param elementBOMap
     * @param chainPathDetailDTOList
     * @param currentPackChainPathList
     */
    private void cnvAndfillChainPathDetailDTOList(Map<Long, List<PackBranchBO>> chainPathBranchBOMap,
            Map<Long, List<PackElementBO>> elementBOMap, List<RcRcPackChainPathDetailDTO> chainPathDetailDTOList,
            List<PackChainPathBO> currentPackChainPathList, Map<String, MaterialBO> materialBOMap,
            Map<String, PosterBO> posterBOMap) {
        //遍历链路列表 构建链路dto详情
        currentPackChainPathList.forEach(chainPathDTO -> {
            //该链路下的分支列表
            List<PackBranchBO> branchDTOList = chainPathBranchBOMap.get(chainPathDTO.getChainPathId());
            //该链路下的元素列表
            List<PackElementBO> currentChainPathElementDTOList = elementBOMap.get(chainPathDTO.getChainPathId());
            //链路详情dto
            RcRcPackChainPathDetailDTO chainPathDetailDTO = PackConvert.cnvPackChainPathDTO2DetailDTO(chainPathDTO);
            LOGGER.info("打印日志2！！！chainPathDetailDTO:{}", JSONUtil.toJsonStr(chainPathDetailDTO));
            chainPathDetailDTOList.add(chainPathDetailDTO);

            //1若分支列表为空
            if (CollectionUtils.isEmpty(branchDTOList)) {
                //1.1若元素列表为空,则直接跳过本次循环
                if (CollectionUtils.isEmpty(currentChainPathElementDTOList)) {
                    return;
                }
                //1.2元素列表不为空，则表示这些元素没有对应的真实的分支。
                RcPackBranchDetailDTO branchDetailDTO = PackConvert
                        .buildPackBranchDetailDTO(null, currentChainPathElementDTOList, materialBOMap, posterBOMap);
                chainPathDetailDTO.setPackBranchList(Collections.singletonList(branchDetailDTO));
                return;
            }

            //2存在分支
            //将元素按照分支id分组
            Map<Long, List<PackElementBO>> branchIdElementListMap = currentChainPathElementDTOList.stream()
                    .collect(Collectors.groupingBy(PackElementBO::getBranchId));
            //遍历分支列表，构建分支dto
            List<RcPackBranchDetailDTO> branchDetailDTOList = branchDTOList.stream().map(branchDTO -> {
                RcPackBranchDetailDTO branchDetailDTO = PackConvert
                        .buildPackBranchDetailDTO(branchDTO, branchIdElementListMap.get(branchDTO.getBranchId()),
                                materialBOMap, posterBOMap);
                return branchDetailDTO;
            }).sorted(Comparator.comparing(RcPackBranchDetailDTO::getSort)).collect(Collectors.toList());
            chainPathDetailDTO.setPackBranchList(branchDetailDTOList);
        });
    }

    private void checkPackChainPathContentSaveParam(RcPackChainPathContentSaveParam param) {
        //校验分支
        for (RcPackBranchSaveParam packBranchSaveParam : param.getPackBranchList()) {
            if (StringUtils.isNotBlank(packBranchSaveParam.getBranchId())) {
                Assert.isTrue(StringUtils.isNotBlank(packBranchSaveParam.getName()), "分支名称不能为空");
            }
            Assert.isTrue(CollectionUtils.isNotEmpty(packBranchSaveParam.getPackElementList()), "素材不能为空");
            //校验元素列表
            for (RcPackElementSaveParam elementSaveParam : packBranchSaveParam.getPackElementList()) {
                Assert.notNull(elementSaveParam.getType(), "元素类型不能为空");
                Assert.notNull(elementSaveParam.getSort(), "元素排序不能为空");
            }
        }
    }
}
