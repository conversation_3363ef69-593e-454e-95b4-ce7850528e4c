package com.dl.dops.biz.system.dal.role.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-02-28 20:24
 */
@Data
@TableName("sys_role")
public class RolePO extends BasePO {

    private static final long serialVersionUID = -5454585638485097450L;
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 名称
     */
    private String name;

    /**
     * 角色类型
     */
    @TableField("role_type")
    private String roleType;

    /**
     * 是否删除，0-否，1-是
     */
    private int isDeleted;

}
