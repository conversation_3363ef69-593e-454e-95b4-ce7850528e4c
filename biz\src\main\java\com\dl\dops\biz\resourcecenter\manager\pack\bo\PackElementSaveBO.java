package com.dl.dops.biz.resourcecenter.manager.pack.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 20:20
 */
@Data
public class PackElementSaveBO {

    /**
     * 元素id
     */
    private Long elementId;
    /**
     * 元素类型
     *
     * @see：PackElementTypeEnum
     */
    private Integer type;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 标题
     */
    private String title;

    /**
     * 文字内容
     */
    private String content;

    /**
     * 附件列表
     */
    private String attachments;

    /**
     * 扩展数据
     * 存储json数据
     * 当type是10时，存储：{"productList":[{"productId":"111","sort":1},{"productId":"222","sort":2},{"productId":"333","sort":3}]}
     */
    private String extData;

    private String remark;
}
