package com.dl.dops.system.web.voicetrain.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-07 10:48
 */
@Data
public class VoiceTrainJobPageQueryParam extends AbstractPageParam {

    /**
     * 厂商，3-深声科技（线上训练），6-火山引擎
     */
    @ApiModelProperty("厂商，3-深声科技（线上训练），6-火山引擎")
    @NotNull(message = "厂商不能为空")
    private Integer channel;

    /**
     * 厂商训练模型编号
     */
    @ApiModelProperty("厂商训练模型编号")
    @NotBlank(message = "厂商训练模型编号不能为空")
    private String extModelCode;

    /**
     * 训练类型：0 数字人训练；1 声音训练
     *
     * @See:com.dl.aiservice.biz.manager.train.enums.TrainTypeEnum
     */
    @ApiModelProperty("训练类型：0 数字人训练；1 声音训练")
    @NotNull(message = "训练类型不能为空")
    private Integer trainType;
}
