package com.dl.dops.biz.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * DTO: 金融机构组织
 */
@Data
public class OrganizeDTO {

    @ApiModelProperty("表ID")
    private Long id;

    @ApiModelProperty("组织id")
    private Long organizeId;

    @ApiModelProperty("组织名称")
    private String organizeName;

    @ApiModelProperty("组织logo小图标")
    private String logoIconUrl;

    @ApiModelProperty("组织logo高清图")
    private String logoHighResUrl;

    @ApiModelProperty("排序（数字越大排序越靠前）")
    private Integer sort;
}

