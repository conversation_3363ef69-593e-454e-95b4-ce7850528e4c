package com.dl.dops.system.web.virtualman.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-30 10:18
 */
@Data
public class DaVirtualVoiceLinkParam {

    /**
     * 语速
     */
    @ApiModelProperty("语速")
    private String speed;

    /**
     * 音量
     */
    @ApiModelProperty("音量")
    private String volume;

    /**
     * 试听链接
     */
    @ApiModelProperty("试听链接")
    private String sampleLink;

    /**
     * 音频时长(单位：毫秒)
     */
    @ApiModelProperty("音频时长(单位：毫秒)")
    private Long duration;

}
