package com.dl.dops;

import cn.easyes.starter.config.EasyEsConfigProperties;
import cn.easyes.starter.config.EsAutoConfiguration;
import cn.easyes.starter.register.EsMapperScan;
import com.dl.dops.biz.common.annotation.BaseDao;
import com.dl.dops.biz.resourcecenter.mq.MqChannels;
import com.dtflys.forest.springboot.annotation.ForestScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@MapperScan(annotationClass = BaseDao.class)
@EsMapperScan("com.dl.dops.biz.resourcecenter.es")
@SpringBootApplication(exclude = { EsAutoConfiguration.class })
@EnableConfigurationProperties({ EasyEsConfigProperties.class })
@ForestScan(basePackages = { "com.dl.dops.biz.common.forest" })
@EnableDiscoveryClient
@EnableBinding({ MqChannels.class })
@EnableTransactionManagement
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
