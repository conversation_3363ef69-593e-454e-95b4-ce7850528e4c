package com.dl.dops.resourcecenter.web.controllers.taggroup.convert;

import com.dl.dops.biz.resourcecenter.dal.tag.po.TagGroupPO;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagPO;
import com.dl.dops.biz.resourcecenter.manager.tag.helper.TagHelper;
import com.dl.dops.resourcecenter.tag.dto.TagGroupDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-07 16:21
 */
public class TagGroupHelper {

    public static TagGroupDTO buildTagGroupDTO(TagGroupPO input, Map<Long, List<TagPO>> tagListMap) {
        TagGroupDTO result = new TagGroupDTO();
        result.setName(input.getName());
        result.setTagGroupId(String.valueOf(input.getTagGroupId()));
        result.setOrder(input.getOrdered());

        if (MapUtils.isEmpty(tagListMap)) {
            return result;
        }

        List<TagPO> tagList = tagListMap.get(input.getTagGroupId());
        if (CollectionUtils.isNotEmpty(tagList)) {
            result.setTagInfoDTOList(tagList.stream().map(TagHelper::cnvTagPO2DTO).collect(Collectors.toList()));
        }
        return result;
    }
}
