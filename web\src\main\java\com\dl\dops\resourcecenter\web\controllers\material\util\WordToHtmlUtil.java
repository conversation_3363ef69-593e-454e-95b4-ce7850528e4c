package com.dl.dops.resourcecenter.web.controllers.material.util;

import cn.hutool.core.io.FileUtil;
import com.dl.dops.biz.common.tencentcloud.cos.CosFileUploadManager;
import fr.opensagres.poi.xwpf.converter.core.BasicURIResolver;
import fr.opensagres.poi.xwpf.converter.core.FileImageExtractor;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLConverter;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLOptions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.converter.WordToHtmlConverter;
import org.apache.poi.hwpf.usermodel.PictureType;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import org.w3c.dom.Document;

import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

/**
 * @date
 * @description
 */
@Component
@Slf4j
public class WordToHtmlUtil {

    @Autowired
    private CosFileUploadManager cosFileUploadManager;

    /**
     * 上传Word文档，返回解析后的Html
     */
    public String docToHtmlText(MultipartFile file) {
        //使用字符数组流获取解析的内容
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        OutputStream outStream = new BufferedOutputStream(baos);
        try {
            //将上传的文件传入Document转换
            HWPFDocument wordDocument = new HWPFDocument(file.getInputStream());
            Document document = DocumentBuilderFactory.newInstance().newDocumentBuilder().newDocument();
            WordToHtmlConverter wordToHtmlConverter = new WordToHtmlConverter(document);
            //将读取到的图片上传并添加链接地址
            wordToHtmlConverter.setPicturesManager((imageStream, pictureType, name, width, height) -> {
                try {
                    //首先要判断图片是否能识别
                    if (pictureType.equals(PictureType.UNKNOWN)) {
                        return "[不能识别的图片]";
                    }
                    //此处上传到自己的文件服务器
                    log.info("doc2html, image name={}", name);
                    File f = new File(name);
                    OutputStream output = new FileOutputStream(f);
                    BufferedOutputStream bufferedOutput = new BufferedOutputStream(output);
                    bufferedOutput.write(imageStream);
                    bufferedOutput.flush();
                    IOUtils.closeQuietly(bufferedOutput);
                    String fileUrl = cosFileUploadManager.uploadFile(f, null);
                    FileUtil.del(f);
                    return fileUrl;
                } catch (Exception e) {
                    log.info("upload exception", e);
                }
                return "[图片上传失败]";
            });
            // word文档转Html文档
            wordToHtmlConverter.processDocument(wordDocument);
            Document htmlDocument = wordToHtmlConverter.getDocument();
            DOMSource domSource = new DOMSource(htmlDocument);
            StreamResult streamResult = new StreamResult(outStream);
            TransformerFactory factory = TransformerFactory.newInstance();
            Transformer serializer = factory.newTransformer();
            serializer.setOutputProperty(OutputKeys.ENCODING, "utf-8");
            serializer.setOutputProperty(OutputKeys.INDENT, "yes");
            serializer.setOutputProperty(OutputKeys.METHOD, "html");
            serializer.transform(domSource, streamResult);
            String content = baos.toString();
            log.info("docToHtmlText--->{}", content);
            return content;
        } catch (Exception e) {
            log.error("docToHtmlText 异常", e);
            return "";
        } finally {
            try {
                baos.close();
                outStream.close();
            } catch (IOException e) {
                log.error("docToHtmlText io关闭异常", e);
            }
        }
    }

    /**
     * 上传docx文档，返回解析后的Html
     */
    public String docxToHtmlText(MultipartFile file) {
        ByteArrayOutputStream htmlStream = new ByteArrayOutputStream();
        try {
            ZipSecureFile.setMinInflateRatio(0);
            // 将上传的文件传入Document转换
            XWPFDocument docxDocument = new XWPFDocument(file.getInputStream());
            XHTMLOptions options = XHTMLOptions.create();
            // 设置图片存储路径
            String path = System.getProperty("java.io.tmpdir");
            String firstImagePathStr = path + "/" + System.currentTimeMillis();
            options.setExtractor(new FileImageExtractor(new File(firstImagePathStr)));
            options.URIResolver(new BasicURIResolver(firstImagePathStr));
            // 转换html
            docxDocument.createNumbering();
            XHTMLConverter.getInstance().convert(docxDocument, htmlStream, options);
            String htmlStr = htmlStream.toString();

            String middleImageDirStr = "/word/media";
            String imageDirStr = firstImagePathStr + middleImageDirStr;
            File imageDir = new File(imageDirStr);
            String[] imageList = imageDir.list();
            if (imageList != null) {
                for (int i = 0; i < imageList.length; i++) {
                    try {
                        String oneImagePathStr = imageDirStr + "/" + imageList[i];
                        log.info("doc2html, image name={}", oneImagePathStr);
                        File fileImage = new File(oneImagePathStr);
                        if (fileImage.exists()) {
                            String fileUrl = cosFileUploadManager.uploadFile(fileImage, null);
                            //修改文档中的图片信息
                            htmlStr = htmlStr.replace(oneImagePathStr, fileUrl);
                        }
                    } catch (Exception e) {
                        log.info("upload docxToHtmlText exception", e);
                    }
                }
            }
            //删除图片路径
            File firstImagePath = new File(firstImagePathStr);
            FileUtils.deleteDirectory(firstImagePath);
            return htmlStr;
        } catch (Exception e) {
            log.error("docxToHtmlText 解析异常", e);
            return "";
        } finally {
            if (htmlStream != null) {
                try {
                    htmlStream.close();
                } catch (IOException e) {
                    log.error("docToHtmlText io关闭异常", e);
                }
            }
        }
    }

    //   public static void main(String[] args) {
    //        try {
    //            WordToHtmlUtil util = new WordToHtmlUtil();
    //            String content = util.docToHtmlText(util.getMulFileByPath("C:\\Users\\<USER>\\Desktop\\test2.doc"));
    //            FileUtils.writeStringToFile(new File("C:\\Users\\<USER>\\Desktop\\", "test-doc.html"), content, "utf-8");
    //
    //            String content2 = util.docxToHtmlText(util.getMulFileByPath("C:\\Users\\<USER>\\Desktop\\test.docx"));
    //            FileUtils.writeStringToFile(new File("C:\\Users\\<USER>\\Desktop\\", "test-docx.html"), content2, "utf-8");
    //        } catch (Exception e) {
    //            e.printStackTrace();
    //        }
    //    }

    /**
     * 获取MultipartFile文件
     *
     * @param picPath
     * @return
     */
    private MultipartFile getMulFileByPath(String picPath) {
        FileItem fileItem = createFileItem(picPath);
        MultipartFile mfile = new CommonsMultipartFile(fileItem);
        return mfile;
    }

    private FileItem createFileItem(String filePath) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "textField";
        int num = filePath.lastIndexOf(".");
        String extFile = filePath.substring(num);
        FileItem item = factory.createItem(textFieldName, "text/plain", true, "MyFileName" + extFile);
        File newfile = new File(filePath);
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        try {
            FileInputStream fis = new FileInputStream(newfile);
            OutputStream os = item.getOutputStream();
            while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            fis.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return item;
    }

}