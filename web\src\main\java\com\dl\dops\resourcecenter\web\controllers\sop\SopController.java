package com.dl.dops.resourcecenter.web.controllers.sop;

import cn.hutool.json.JSONUtil;
import com.dl.dops.resourcecenter.sop.dto.RcReachTypeDTO;
import com.dl.dops.resourcecenter.sop.dto.RcSopDTO;
import com.dl.dops.resourcecenter.sop.dto.RcSopDetailDTO;
import com.dl.dops.resourcecenter.sop.dto.RcSopEventDetailDTO;
import com.dl.dops.resourcecenter.sop.dto.RcSopStatusDTO;
import com.dl.dops.resourcecenter.sop.enums.RcReachTypeEnum;
import com.dl.dops.resourcecenter.sop.enums.RcSopStatusEnum;
import com.dl.dops.resourcecenter.sop.enums.RcSopTypeEnum;
import com.dl.dops.resourcecenter.sop.param.RcSopEventBatchSaveParam;
import com.dl.dops.resourcecenter.sop.param.RcSopPageQueryParam;
import com.dl.dops.resourcecenter.sop.param.RcSopSaveParam;
import com.dl.dops.resourcecenter.web.controllers.sop.convert.ReachTypeConvert;
import com.dl.dops.resourcecenter.web.controllers.sop.convert.SopConvert;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping({ "/resource/sop", "/dops/resource/sop" })
@Api("资源中心 - sop")
public class SopController {
    private static final Logger LOGGER = LoggerFactory.getLogger(SopController.class);

    @Autowired
    private SopProcess sopProcess;

    private static List<RcSopStatusDTO> rcSopStatusDTOList = new ArrayList<>();

    static {
        for (RcSopStatusEnum statusEnum : RcSopStatusEnum.values()) {
            RcSopStatusDTO rcSopStatusDTO = SopConvert.cnvSopStatusEnum2VO(statusEnum);
            rcSopStatusDTOList.add(rcSopStatusDTO);
        }
    }

    @GetMapping({ "/info", "/tenantauth/info" })
    @ApiOperation("查询资源中心sop信息")
    public ResultModel<RcSopDTO> info(@RequestParam String sopId) {
        return sopProcess.info(Long.valueOf(sopId));
    }

    @PostMapping("/add")
    @ApiOperation("创建资源中心sop")
    public ResultModel<String> add(@RequestBody @Validated RcSopSaveParam param) {
        LOGGER.info("创建sop模板，入参:{}", JSONUtil.toJsonStr(param));
        return sopProcess.add(param);
    }

    @PostMapping("/modify")
    @ApiOperation("修改资源中心sop")
    public ResultModel modifyTpl(@RequestBody @Validated RcSopSaveParam param) {
        LOGGER.info("修改sop模板，入参:{}", JSONUtil.toJsonStr(param));
        return sopProcess.modify(param);
    }

    @Deprecated
    @GetMapping("/publishorcancel")
    @ApiOperation("资源中心sop发布或取消发布")
    public ResultModel publishOrCancel(@RequestParam("sopId") String sopId) {
        return sopProcess.publishOrCancel(Long.valueOf(sopId));
    }

    @PostMapping("/page")
    @ApiOperation("查询资源中心sop列表")
    public ResultPageModel<RcSopDTO> page(@RequestBody @Validated RcSopPageQueryParam param) {
        return sopProcess.pageQuery(param);
    }

    @GetMapping("/delete")
    @ApiOperation("逻辑删除资源中心sop信息")
    public ResultModel delete(@RequestParam String sopId) {
        return sopProcess.delete(Long.valueOf(sopId));
    }

    @GetMapping("/done")
    @ApiOperation("资源中心sop创建完成")
    public ResultModel done(@RequestParam String sopId) {
        LOGGER.info("sop创建完成，入参:sopId:{}", sopId);
        return sopProcess.done(Long.valueOf(sopId));
    }

    @PostMapping("/batchsaveevent")
    @ApiOperation("批量保存事件")
    public ResultModel batchSaveEvent(@RequestBody @Validated RcSopEventBatchSaveParam param) {
        return sopProcess.batchSaveEvent(param);
    }

    @GetMapping("/eventdetail")
    @ApiOperation("查询sop事件详情")
    public ResultModel<RcSopEventDetailDTO> eventDetail(@RequestParam String eventId) {
        return sopProcess.eventDetail(Long.valueOf(eventId));
    }

    @GetMapping("/listevent")
    @ApiOperation("查询sop事件列表")
    public ResultModel<Map<Integer, List<RcSopEventDetailDTO>>> listEvent(@RequestParam String sopId) {
        return sopProcess.listEvent(Long.valueOf(sopId));
    }

    @GetMapping("/detail")
    @ApiOperation("查询资源中心sop详情")
    public ResultModel<RcSopDetailDTO> detail(@RequestParam String sopId){
        return sopProcess.detail(Long.valueOf(sopId));
    }

    @GetMapping("/reachtypelist")
    @ApiOperation("查询触达形式列表")
    public ResultModel<List<RcReachTypeDTO>> getReachTypelist(@RequestParam(required = false) Integer sopType) {
        List<RcReachTypeEnum> rcReachTypeEnumList;
        if (RcSopTypeEnum.INDIVIDUAL.getCode().equals(sopType)) {
            rcReachTypeEnumList = RcReachTypeEnum.getContactSopEvent();
        } else if (RcSopTypeEnum.GROUP.getCode().equals(sopType)) {
            rcReachTypeEnumList = RcReachTypeEnum.getGroupSopEvent();
        } else {
            rcReachTypeEnumList = Lists.newArrayList(RcReachTypeEnum.values());
        }
        return ResultModel.success(ReachTypeConvert.cnvReachTypeEnumList2VOList(rcReachTypeEnumList));
    }

    @GetMapping("/statuslist")
    @ApiOperation("查询sop状态列表")
    public ResultModel<List<RcSopStatusDTO>> getReachTypelist() {
        return ResultModel.success(rcSopStatusDTOList);
    }

}
