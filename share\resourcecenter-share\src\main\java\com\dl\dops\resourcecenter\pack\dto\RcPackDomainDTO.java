package com.dl.dops.resourcecenter.pack.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @see
 * @since 2022-09-15 20:40
 */
@Data
@ApiModel("服务包适用行业vo")
public class RcPackDomainDTO implements Serializable {

    private static final long serialVersionUID = 9145131184585861791L;
    @ApiModelProperty("编码")
    private Integer domain;

    @ApiModelProperty("名称")
    private String name;
}
