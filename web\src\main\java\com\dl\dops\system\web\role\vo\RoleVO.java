package com.dl.dops.system.web.role.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("租户角色信息")
public class RoleVO {
    @ApiModelProperty("角色id")
    private String roleId;
    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("角色类型")
    private String roleType;

    @ApiModelProperty("创建时间")
    private Long createBy;

    @ApiModelProperty("修改时间")
    private Long modifyBy;

    @ApiModelProperty("创建人")
    private Date createDt;

    @ApiModelProperty("修改人")
    private Date modifyDt;

}
