package com.dl.dops.biz.resourcecenter.manager.poster.bo;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Builder
public class PosterBO {

    String bizId;

    String name;

    String categoryId;

    Integer isRecommend;

    String logoImgUrl;

    String imgUrl;

    PosterTenantInfoBO tenantInfo;

    PosterCreatorInfoBO creator;
}
