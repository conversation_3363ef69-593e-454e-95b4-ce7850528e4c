package com.dl.dops.biz.system.manager.user.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.util.RedisUtil;
import com.dl.dops.biz.system.dal.function.FunctionMapper;
import com.dl.dops.biz.system.dal.function.po.FunctionPO;
import com.dl.dops.biz.system.dal.role.RoleFunctionMapper;
import com.dl.dops.biz.system.dal.role.RoleMenuMapper;
import com.dl.dops.biz.system.dal.role.UserRoleMapper;
import com.dl.dops.biz.system.dal.role.po.RoleFunctionPO;
import com.dl.dops.biz.system.dal.role.po.RoleMenuPO;
import com.dl.dops.biz.system.dal.role.po.UserRolePO;
import com.dl.dops.biz.system.manager.function.dto.FunctionDTO;
import com.dl.dops.biz.system.manager.menu.MenuManager;
import com.dl.dops.biz.system.manager.menu.bo.RoleMenuParamBO;
import com.dl.dops.biz.system.manager.menu.dto.MenuDTO;
import com.dl.dops.biz.system.manager.role.bo.RoleFunctionParamBO;
import com.dl.dops.biz.system.manager.user.ISysAuthCacheService;
import com.dl.dops.biz.system.manager.user.bo.UserRolesParamBO;
import com.dl.dops.biz.system.manager.user.dto.UserDTO;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户-角色-菜单权限缓存
 */
@Component
public class UserRoleMenuRedisCache implements CommandLineRunner, ISysAuthCacheService {

    public static final String ALL_AUTH_KEY = "dl.dops.authSet_key.all";

    public static final String ROLE_MENU_SET_CACHE_KEY_PREFIX = "dl.dops.roleMenu_set.key.";

    public static final String MENU_ALL_CACHE_KEY = "dl.dops.menus.all";

    public static final String FUNCTION_ALL_CACHE_KEY = "dl.dops.functions.all";

    public static final String MENU_FUNCTION_SET_CACHE_KEY_PREFIX = "dl.dops.menuFunction_set.key.";

    public static final String MENU_FUNCTION_MAP_CACHE_KEY = "dl.dops.menuFunction_map.key";

    public static final String USER_ROLE_SET_CACHE_KEY_PREFIX = "dl.dops.userRole_set.key.";

    public static final String ROLE_FUNCTION_SET_CACHE_KEY_PREFIX = "dl.dops.roleFunction_set.key.";

    public static final String ADMUSER_KEY_PREFIX = "dl.dops.adm_user.key.";

    public static final Long ONE_DAY_EXPIRE_SECONDS = 24 * 60 * 60L;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private RoleMenuMapper roleMenuMapper;
    @Autowired
    private RoleFunctionMapper roleFunctionMapper;
    @Autowired
    private FunctionMapper functionMapper;
    @Resource
    private MenuManager menuManager;

    private String getAllMenuKey() {
        return MENU_ALL_CACHE_KEY;
    }

    private String getAllFunctionKey() {
        return FUNCTION_ALL_CACHE_KEY;
    }

    private String getRoleMenuSetCacheKey(Long roleId) {
        return ROLE_MENU_SET_CACHE_KEY_PREFIX + roleId;
    }

    private String getUserRoleSetCacheKey(Long userId) {
        return USER_ROLE_SET_CACHE_KEY_PREFIX + userId;
    }

    private String getMenuFunctionSetCacheKey(Long menuId) {
        return MENU_FUNCTION_SET_CACHE_KEY_PREFIX + menuId;
    }

    private String getMenuFunctionMapCacheKey() {
        return MENU_FUNCTION_MAP_CACHE_KEY;
    }

    private String getRoleFuntionSetCacheKey(Long roleId) {
        return ROLE_FUNCTION_SET_CACHE_KEY_PREFIX + roleId;
    }

    private String getSysAdmUserKey(Long userId) {
        return ADMUSER_KEY_PREFIX + userId;
    }

    private void addCacheKey(String... keys) {
        redisUtil.addSetAll(ALL_AUTH_KEY, keys);
    }

    @Override
    public void run(String... args) throws Exception {
        refresh();
    }

    public void refresh() {
        //清除旧缓存
        clearAllCache();
        //菜单实体
        initMenusCache();
        //功能实体
        initFunctionCache();
        //用户-角色
        initUserRoleSetCache();
        //角色-菜单
        initRoleMenusCache();
        //角色-功能
        initRoleFunctionCache();
    }

    private void clearAllCache() {
        Set<String> set = redisUtil.getSet(ALL_AUTH_KEY);
        if (CollectionUtils.isNotEmpty(set)) {
            redisUtil.del(set.toArray(new String[set.size()]));
        }
    }

    /**
     * 用户-角色缓存
     * 使用set缓存，key：userid，set：roleId集合
     */
    public Map<Long, Set<Long>> initUserRoleSetCache() {
        Set<String> keys = new HashSet<>();
        List<UserRolePO> list = userRoleMapper.selectList(Wrappers.lambdaQuery());
        Map<Long, Set<Long>> userSet = new HashMap<>();
        list.forEach(p -> {
            Long userId = p.getUserId();
            Long value = p.getRoleId();
            if (userSet.containsKey(userId)) {
                userSet.get(userId).add(value);
            } else {
                Set<Long> longs = new HashSet<>();
                userSet.put(userId, longs);
                longs.add(value);
            }
        });
        if (MapUtils.isNotEmpty(userSet)) {
            userSet.forEach((k, v) -> {
                String key = getUserRoleSetCacheKey(k);
                keys.add(key);
                redisUtil.addSetAll(key, v.stream().map(l -> String.valueOf(l)).toArray());
            });
            addCacheKey(keys.toArray(new String[keys.size()]));
        }
        return userSet;
    }

    public List<FunctionDTO> getAllFunctions() {
        return redisUtil.hgetValues(getAllFunctionKey());
    }

    public Map<String, FunctionDTO> initFunctionCache() {
        List<FunctionPO> list = functionMapper
                .selectList(Wrappers.lambdaQuery(FunctionPO.class).eq(FunctionPO::getIsDeleted, Const.ZERO));
        List<FunctionDTO> dtoList = list.stream().map(po -> {
            FunctionDTO dto = new FunctionDTO();
            dto.setIcon(po.getIcon());
            dto.setName(po.getName());
            dto.setSort(po.getSort());
            dto.setFunctionId(po.getFunctionId());
            dto.setFunctionCode(po.getFunctionCode());
            return dto;
        }).collect(Collectors.toList());

        String key = getAllFunctionKey();
        Map<String, FunctionDTO> map = new LinkedHashMap<>();
        if (CollectionUtils.isNotEmpty(dtoList)) {
            dtoList.forEach(fun -> {
                map.put(String.valueOf(fun.getFunctionCode()), fun);
            });
        }
        redisUtil.hset(key, map);
        addCacheKey(key);
        return map;
    }

    /**
     * Map<Long,List<Long>> 角色-菜单缓存
     * 使用set缓存，set key：roleId，set：menuId集合
     */
    public Map<Long, Set<Long>> initRoleMenusCache() {
        List<RoleMenuPO> list = roleMenuMapper.selectList(Wrappers.lambdaQuery());
        Map<Long, Set<Long>> roleSet = new HashMap<>();
        list.forEach(p -> {
            Long roleId = p.getRoleId();
            Long value = p.getMenuId();
            if (roleSet.containsKey(roleId)) {
                roleSet.get(roleId).add(value);
            } else {
                Set<Long> longs = new HashSet<>();
                roleSet.put(roleId, longs);
                longs.add(value);
            }
        });
        if (MapUtils.isNotEmpty(roleSet)) {
            List<String> keys = new ArrayList<>(roleSet.size());
            roleSet.forEach((roleId, menus) -> {
                String key = getRoleMenuSetCacheKey(roleId);
                keys.add(key);
                redisUtil.addSetAll(key, menus.stream().map(l -> String.valueOf(l)).toArray());
            });
            addCacheKey(keys.toArray(new String[keys.size()]));
        }
        return roleSet;
    }

    /**
     * 获取角色相关菜单集合
     *
     * @param roleIds
     * @return
     */
    public Set<Long> getMenuIds(Collection<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptySet();
        }
        List<String> keys = roleIds.stream().map(roleId -> getRoleMenuSetCacheKey(roleId)).collect(Collectors.toList());
        return redisUtil.union(keys);
    }

    /**
     * 从缓存获取菜单，不保证顺序
     *
     * @param roleIds
     * @return
     */
    public List<MenuDTO> getMenus(Collection<Long> roleIds) {
        String key = getAllMenuKey();
        Set<Long> menuIds = getMenuIds(roleIds);
        List<MenuDTO> list = redisUtil.hget(key, menuIds);
        return list.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 返回全部菜单，不保证顺序
     *
     * @return
     */
    public List<MenuDTO> getAllMenus() {
        String redisKey = getAllMenuKey();
        return redisUtil.hgetValues(redisKey);
    }

    /**
     * 菜单对象缓存
     * 使用redis hash，hk：menuid,hv : SysMenuPO
     */
    public Map<String, MenuDTO> initMenusCache() {
        List<MenuDTO> list = menuManager.listAllMenus();
        String redisKey = getAllMenuKey();
        Map<String, MenuDTO> map = new LinkedHashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(menu -> map.put(String.valueOf(menu.getMenuId()), menu));
        }
        redisUtil.hset(redisKey, map);
        addCacheKey(redisKey);
        return map;
    }

    /**
     * 构建角色-》功能缓存
     */
    private void initRoleFunctionCache() {
        List<RoleFunctionPO> list = roleFunctionMapper.selectList(Wrappers.lambdaQuery());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<FunctionPO> functionList = functionMapper.selectList(Wrappers.lambdaQuery(FunctionPO.class)
                .in(FunctionPO::getFunctionId,
                        list.stream().map(RoleFunctionPO::getFunctionId).distinct().collect(Collectors.toList())));
        Map<Long, FunctionPO> functionMap = functionList.stream()
                .collect(Collectors.toMap(FunctionPO::getFunctionId, Function.identity()));

        //角色-功能
        Map<Long, Set<FunctionPO>> roleFunMap = new HashMap<>();
        list.stream().forEach(po -> {
            Long roleId = po.getRoleId();
            FunctionPO function = functionMap.get(po.getFunctionId());
            Set<FunctionPO> functions;
            if (roleFunMap.containsKey(roleId)) {
                functions = roleFunMap.get(roleId);
            } else {
                functions = new HashSet<>();
                roleFunMap.put(roleId, functions);
            }
            functions.add(function);
        });

        //缓存
        if (MapUtils.isNotEmpty(roleFunMap)) {
            List<String> keys = new ArrayList<>(roleFunMap.size());
            roleFunMap.forEach((k, v) -> {
                String key = getRoleFuntionSetCacheKey(k);
                keys.add(key);
                redisUtil.addSetAll(key, v.toArray());
            });
            addCacheKey(keys.toArray(new String[keys.size()]));
        }
    }

    /**
     * 更新角色-菜单缓存
     *
     * @param bo
     */
    public void updateRoleMenu(RoleMenuParamBO bo) {
        Long roleId = bo.getRoleId();
        //角色-菜单key
        String key = getRoleMenuSetCacheKey(roleId);

        //1、更新角色-菜单
        //删除旧缓存
        redisUtil.del(key);
        List<Long> menuIds = bo.getMenuIds();
        if (menuIds == null) {
            menuIds = Collections.emptyList();
        }
        redisUtil.addSetAll(key, menuIds.stream().map(String::valueOf).toArray());
    }

    /**
     * 更新角色-功能缓存
     *
     * @param bo
     */
    public void updateRoleFunction(RoleFunctionParamBO bo) {
        Long roleId = bo.getRoleId();
        //角色-功能key
        String key = getRoleFuntionSetCacheKey(roleId);

        //更新角色-功能
        //删除旧缓存
        redisUtil.del(key);
        List<Long> functionIds = bo.getFunctionIds();
        if (CollectionUtils.isEmpty(functionIds)) {
            return;
        }
        List<FunctionPO> functionList = functionMapper
                .selectList(Wrappers.lambdaQuery(FunctionPO.class).in(FunctionPO::getFunctionId, functionIds));
        redisUtil.addSetAll(key, functionList.toArray(functionList.toArray(new FunctionPO[functionList.size()])));
    }

    /**
     * 更新用户-角色缓存
     *
     * @param bo
     */
    public void updateUserRoles(UserRolesParamBO bo) {
        List<Long> roleIds = bo.getRoleIds();
        Long userId = bo.getUserId();
        String key = getUserRoleSetCacheKey(userId);
        //删除旧缓存
        redisUtil.del(key);
        //新增缓存
        if (CollectionUtils.isNotEmpty(roleIds)) {
            List<String> ids = roleIds.stream().map(String::valueOf).collect(Collectors.toList());
            redisUtil.addSetAll(key, ids.toArray());
        }
    }

    /**
     * 获取用户权限编码
     *
     * @param userId
     * @return
     */
    public Set<FunctionPO> getFunction(Long userId) {
        Set<Long> roleIds = getUserRoleIds(userId);
        List<String> keys = roleIds.stream().map(this::getRoleFuntionSetCacheKey).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(keys)) {
            return redisUtil.union(keys);
        }
        return Collections.emptySet();
    }

    /**
     * 判断角色roleid是否拥有functionCode权限
     *
     * @param roleId
     * @param functionCode
     * @return
     */
    public boolean hasPermission(Long roleId, String functionCode) {
        Set<FunctionPO> functionSet = redisUtil.getSet(getRoleFuntionSetCacheKey(roleId));
        if (CollectionUtils.isEmpty(functionSet)) {
            return false;
        }
        return functionSet.stream().map(FunctionPO::getFunctionCode).collect(Collectors.toList())
                .contains(functionCode);
    }

    public boolean hasPermission(Collection<Long> roleIds, String functionCode) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }
        for (Long roleId : roleIds) {
            if (hasPermission(roleId, functionCode)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 返回用户关联角色id
     *
     * @param userId
     * @return
     */
    public Set<Long> getUserRoleIds(Long userId) {
        Set<String> roleids = redisUtil.getSet(getUserRoleSetCacheKey(userId));
        return roleids.stream().map(Long::parseLong).collect(Collectors.toSet());
    }

    public UserDTO getUserDetail(Long userId) {
        return redisUtil.get(getSysAdmUserKey(userId));
    }

    public void setUserDetailCache(UserDTO userDTO) {
        redisUtil.set(getSysAdmUserKey(userDTO.getUserId()), userDTO, ONE_DAY_EXPIRE_SECONDS);
    }

    @Override
    public void refreshMenuFunctionCache() {
        //1、重置菜单缓存
        List<MenuDTO> oldMenus = getAllMenus();
        Map<String, MenuDTO> newMenus = initMenusCache();
        //2、重置功能缓存
        List<FunctionDTO> oldfunctions = getAllFunctions();
        Map<String, FunctionDTO> newFunctions = initFunctionCache();
        //4 删除角色-菜单缓存
        List<String> oldMenuCodes = oldMenus.stream().map(m -> String.valueOf(m.getMenuId()))
                .collect(Collectors.toList());
        if (oldMenuCodes.removeAll(newMenus.values())) {
            //去除角色-菜单关联
            deleteRoleMenus(oldMenuCodes);
        }
        //5 删除角色--操作缓存
        List<Long> oldFunctionIds = oldfunctions.stream().map(o -> o.getFunctionId()).collect(Collectors.toList());
        List<Long> newFunctionIds = newFunctions.values().stream().map(o -> o.getFunctionId())
                .collect(Collectors.toList());
        if (oldFunctionIds.removeAll(newFunctionIds)) {
            deleteRoleFunctions(oldFunctionIds);
        }

    }

    private void deleteRoleMenus(Collection<String> menuIds) {
        LambdaUpdateWrapper<RoleMenuPO> lambdaUpdateWrapper = Wrappers.lambdaUpdate(RoleMenuPO.class);
        lambdaUpdateWrapper
                .in(RoleMenuPO::getMenuId, menuIds.stream().map(Long::parseLong).collect(Collectors.toList()));
        roleMenuMapper.delete(lambdaUpdateWrapper);
        initRoleMenusCache();
    }

    private void deleteRoleFunctions(Collection<Long> functionIds) {
        LambdaUpdateWrapper<RoleFunctionPO> lambdaUpdateWrapper = Wrappers.lambdaUpdate(RoleFunctionPO.class);
        lambdaUpdateWrapper.in(RoleFunctionPO::getFunctionId, functionIds);
        roleFunctionMapper.delete(lambdaUpdateWrapper);
        initRoleFunctionCache();
    }
}
