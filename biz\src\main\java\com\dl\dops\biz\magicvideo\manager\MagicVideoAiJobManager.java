package com.dl.dops.biz.magicvideo.manager;

import cn.hutool.json.JSONUtil;
import com.dl.dops.biz.common.forest.magicvideo.MagicVideoClient;
import com.dl.dops.biz.common.forest.magicvideo.dto.StatisticsAiJobDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.StatisticsAiJobTenantDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.StatisticsAiJobTenantSummaryDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.StatisticsAiJobTenantSummaryQueryParamDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.VisualDmJobInfoDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.VisualTtsJobInfoDTO;
import com.dl.dops.biz.common.forest.magicvideo.param.StatisticsAiJobPageParam;
import com.dl.dops.biz.common.forest.magicvideo.param.StatisticsAiJobTopTenantQueryParam;
import com.dl.dops.biz.common.forest.magicvideo.param.VisualAiJobPageQueryParam;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-15 15:40
 */
@Component
public class MagicVideoAiJobManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(MagicVideoAiJobManager.class);

    @Resource
    private MagicVideoClient magicVideoClient;

    public ResultPageModel<StatisticsAiJobDTO> pageAiJobStatistics(StatisticsAiJobPageParam param) {
        ResultPageModel<StatisticsAiJobDTO> result = magicVideoClient.pageAiJobStatistics(param);
        if (!result.isSuccess()) {
            LOGGER.error("分页查询ai任务数据统计失败,param:{},,, resultModel = {}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("分页查询ai任务数据统计失败");
        }
        return result;
    }

    public ResultModel<List<StatisticsAiJobTenantDTO>> queryTopTenantStatistics(
            StatisticsAiJobTopTenantQueryParam param) {
        ResultModel<List<StatisticsAiJobTenantDTO>> result = magicVideoClient.queryTopTenantStatistics(param);
        if (!result.isSuccess()) {
            LOGGER.error("查询前几名租户的ai任务数据统计失败,param:{},,, resultModel = {}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("查询前几名租户的ai任务数据统计失败");
        }
        return result;
    }

    public ResultPageModel<VisualDmJobInfoDTO> pageDmJob(VisualAiJobPageQueryParam param) {
        ResultPageModel<VisualDmJobInfoDTO> result = magicVideoClient.pageDmJob(param);
        if (!result.isSuccess()) {
            LOGGER.error("分页查询数字人任务信息失败,param:{},,, resultModel = {}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("分页查询数字人任务信息失败");
        }
        return result;
    }

    public ResultPageModel<VisualTtsJobInfoDTO> pageTtsJob(VisualAiJobPageQueryParam param) {
        ResultPageModel<VisualTtsJobInfoDTO> result = magicVideoClient.pageTtsJob(param);
        if (!result.isSuccess()) {
            LOGGER.error("分页查询TTS任务信息失败,param:{},,, resultModel = {}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("分页查询TTS任务信息失败");
        }
        return result;
    }

    public ResultModel<StatisticsAiJobTenantSummaryDTO> specificTenantSummary(
            StatisticsAiJobTenantSummaryQueryParamDTO param) {
        ResultModel<StatisticsAiJobTenantSummaryDTO> result = magicVideoClient.specificTenantSummary(param);
        if (!result.isSuccess()) {
            LOGGER.error("查询指定租户的ai任务数据统计汇总失败,param:{},,, resultModel = {}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("查询指定租户的ai任务数据统计汇总失败");
        }
        return result;
    }

}
