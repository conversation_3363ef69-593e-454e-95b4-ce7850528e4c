<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="20220607173956" author="wangtc">
        <preConditions onError="HALT" onFail="CONTINUE" onSqlOutput="IGNORE">
            <not>
                <indexExists tableName="liquibase_test" columnNames="tenant_code,key"/>
            </not>
        </preConditions>
        <comment>create index</comment>
        <createIndex indexName="idx_1_liquibase_test"
                     tableName="liquibase_test"
                     unique="false">
            <column name="tenant_code"/>
            <column name="key"/>
        </createIndex>
        <rollback>
            <dropIndex tableName="liquibase_test" indexName="idx_1_liquibase_test"/>
        </rollback>

    </changeSet>

</databaseChangeLog>