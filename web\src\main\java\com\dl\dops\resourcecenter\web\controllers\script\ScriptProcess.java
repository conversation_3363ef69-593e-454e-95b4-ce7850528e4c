package com.dl.dops.resourcecenter.web.controllers.script;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.dops.biz.common.service.CosPathService;
import com.dl.dops.web.common.aop.RedisLock;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.util.RedisUtil;
import com.dl.dops.biz.resourcecenter.dal.script.po.ScriptPO;
import com.dl.dops.biz.resourcecenter.manager.script.ScriptManager;
import com.dl.dops.biz.resourcecenter.manager.script.ScriptSearchManager;
import com.dl.dops.biz.resourcecenter.manager.script.bo.CntScriptBO;
import com.dl.dops.biz.resourcecenter.manager.script.bo.CntScriptBatchBO;
import com.dl.dops.biz.resourcecenter.manager.script.bo.ScriptDetailBO;
import com.dl.dops.biz.resourcecenter.manager.script.bo.ScriptSearchBO;
import com.dl.dops.resourcecenter.material.dto.RcMaterialDTO;
import com.dl.dops.resourcecenter.material.enums.RcMaterialTypeEnum;
import com.dl.dops.resourcecenter.script.dto.RcCreatorDTO;
import com.dl.dops.resourcecenter.script.dto.RcScriptCategoryDTO;
import com.dl.dops.resourcecenter.script.dto.RcScriptDTO;
import com.dl.dops.resourcecenter.script.dto.RcScriptTemplateDTO;
import com.dl.dops.resourcecenter.script.dto.RcScriptUploadFailedDTO;
import com.dl.dops.resourcecenter.script.dto.RcScriptUploadResultDTO;
import com.dl.dops.resourcecenter.script.enums.RcScriptPublishStatusEnum;
import com.dl.dops.resourcecenter.script.param.RcScriptAddParam;
import com.dl.dops.resourcecenter.script.param.RcScriptEditParam;
import com.dl.dops.resourcecenter.script.param.RcScriptPageQueryParam;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.resourcecenter.web.controllers.material.convert.MaterialConvert;
import com.dl.dops.resourcecenter.web.controllers.script.handler.ScriptFailedHandler;
import com.dl.dops.resourcecenter.web.controllers.script.handler.ScriptTemplateHandler;
import com.dl.dops.resourcecenter.web.controllers.script.handler.ScriptUploadListener;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.common.utils.JsonUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @describe: CntScriptProcess
 * @author: zhousx
 * @date: 2022/6/15 10:13
 */
@Slf4j
@Component
public class ScriptProcess extends AbstractController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ScriptProcess.class);
    private static final String SCRIPT_LOCK_PREFIX = "dl-resource-add-script";
    @Autowired
    private ScriptManager scriptManager;
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ScriptSearchManager scriptSearchManager;

    @Autowired
    private CosPathService cosPathService;

    @RedisLock(prefix = SCRIPT_LOCK_PREFIX)
    public ResultModel<RcScriptDTO> add(RcScriptAddParam param) {
        CntScriptBO bo = new CntScriptBO();
        bo.setContent(param.getContent());
        bo.setQuestion(param.getQuestion());
        bo.setType(param.getType());
        if (StringUtils.isNotBlank(param.getCategory1())) {
            bo.setCategory1(Long.valueOf(param.getCategory1()));
        }
        if (StringUtils.isNotBlank(param.getCategory2())) {
            bo.setCategory2(Long.valueOf(param.getCategory2()));
        }
        if (CollectionUtils.isNotEmpty(param.getMaterialIds())) {
            bo.setMaterialIds(param.getMaterialIds().stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        bo.setPublishStatus(param.getPublishStatus());
        bo.setRemark(param.getRemark());
        RcScriptDTO dto = new RcScriptDTO();
        dto.setScriptId(scriptManager.addScript(bo) + "");
        return ResultModel.success(dto);
    }

    public ResultPageModel<RcScriptDTO> page(RcScriptPageQueryParam param) {
        ScriptSearchBO bo = new ScriptSearchBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setKeyword(param.getKeyword());
        if (StringUtils.isNumeric(param.getCategory1())) {
            bo.setCategory1(Long.valueOf(param.getCategory1()));
        }
        if (StringUtils.isNumeric(param.getCategory2())) {
            bo.setCategory2(Long.valueOf(param.getCategory2()));
        }
        if (NumberUtils.INTEGER_ONE.equals(param.getScope())) {
            bo.setCreateBy(getUserId());
        }
        if (Objects.nonNull(param.getPublishStatus())) {
            bo.setPublishStatus(param.getPublishStatus());
        }

        ResponsePageQueryDO<List<ScriptDetailBO>> result = StringUtils.isNotBlank(param.getKeyword()) ?
                scriptManager.searchScripts(bo) :
                scriptManager.pageQueryScripts(bo);
        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<RcScriptDTO> dtos = result.getDataResult().stream().map(scriptBO -> {
            RcScriptDTO scriptDTO = new RcScriptDTO();
            scriptDTO.setScriptId(scriptBO.getScriptId() + "");
            scriptDTO.setQuestion(scriptBO.getQuestion());
            scriptDTO.setContent(scriptBO.getContent());
            scriptDTO.setCreateTime(scriptBO.getCreateDt());
            scriptDTO.setPublishStatus(scriptBO.getPublishStatus());
            scriptDTO.setRemark(scriptBO.getRemark());

            RcScriptCategoryDTO category = new RcScriptCategoryDTO();
            if (Objects.nonNull(scriptBO.getCategory1())) {
                category.setCategory1Id(scriptBO.getCategory1() + "");
            }
            if (Objects.nonNull(scriptBO.getCategory2())) {
                category.setCategory2Id(scriptBO.getCategory2() + "");
            }
            category.setCategory1Name(scriptBO.getCategory1Name());
            category.setCategory2Name(scriptBO.getCategory2Name());
            scriptDTO.setCategory(category);

            RcCreatorDTO creator = new RcCreatorDTO();
            creator.setName(scriptBO.getCreatorName());
            creator.setUserId(scriptBO.getCreateBy() + "");
            scriptDTO.setCreator(creator);

            if (CollectionUtils.isNotEmpty(scriptBO.getMaterials())) {
                scriptDTO.setMaterials(scriptBO.getMaterials().stream().map(materialBO -> {
                    RcMaterialDTO materialDTO = new RcMaterialDTO();
                    materialDTO.setMaterialId(materialBO.getMaterialId());
                    materialDTO.setTitle(materialBO.getTitle());
                    materialDTO.setMaterialType(materialBO.getMaterialType());
                    materialDTO.setLogoImg(materialBO.getLogoImg());
                    //非文章类或者需要文章内容，设置content
                    if (!RcMaterialTypeEnum.ARTICLE.getCode().equals(materialBO.getMaterialType()) || Const.ONE
                            .equals(param.getNeedArticleContent())) {
                        materialDTO.setContent(materialBO.getContent());
                    }
                    cosPathService.postProcessObjectStorageMaterialDTO(materialDTO);
                    return materialDTO;
                }).collect(Collectors.toList()));
            }
            return scriptDTO;
        }).collect(Collectors.toList());
        return pageQueryModel(result, dtos);
    }

    public ResultModel<RcScriptDTO> detail(Long scriptId) {
        ScriptDetailBO detailBO = scriptManager.getDetail(scriptId);
        RcScriptDTO scriptDTO = new RcScriptDTO();
        scriptDTO.setScriptId(detailBO.getScriptId() + "");
        scriptDTO.setQuestion(detailBO.getQuestion());
        scriptDTO.setContent(detailBO.getContent());
        scriptDTO.setCreateTime(detailBO.getCreateDt());
        scriptDTO.setPublishStatus(detailBO.getPublishStatus());
        scriptDTO.setRemark(detailBO.getRemark());

        RcScriptCategoryDTO category = new RcScriptCategoryDTO();
        if (Objects.nonNull(detailBO.getCategory1())) {
            category.setCategory1Id(detailBO.getCategory1() + "");
        }
        if (Objects.nonNull(detailBO.getCategory2())) {
            category.setCategory2Id(detailBO.getCategory2() + "");
        }
        category.setCategory1Name(detailBO.getCategory1Name());
        category.setCategory2Name(detailBO.getCategory2Name());
        scriptDTO.setCategory(category);

        if (CollectionUtils.isNotEmpty(detailBO.getMaterials())) {
            scriptDTO.setMaterials(detailBO.getMaterials().stream()
                    .map(cosPathService.postProcessObjectStorageMaterialDTOFunction(MaterialConvert::cnvCntMaterialDTO2VO))
                    .collect(Collectors.toList()));
        }
        return ResultModel.success(scriptDTO);
    }

    public ResultModel<Void> delete(Long scriptId) {
        ScriptPO existPO = scriptManager.getOne(Wrappers.lambdaQuery(ScriptPO.class).eq(ScriptPO::getScriptId, scriptId)
                .eq(ScriptPO::getIsDeleted, Const.ZERO));
        Assert.notNull(existPO, "话术不存在");
        Assert.isTrue(RcScriptPublishStatusEnum.CANCEL_RELEASED.getStatus().equals(existPO.getPublishStatus()),
                "该话术已发布，请先将状态改为取消发布再进行删除操作");

        scriptManager.lambdaUpdate().eq(ScriptPO::getScriptId, scriptId)
                .eq(ScriptPO::getIsDeleted, NumberUtils.INTEGER_ZERO)
                .set(ScriptPO::getIsDeleted, NumberUtils.INTEGER_ONE).set(ScriptPO::getModifyDt, new Date())
                .set(ScriptPO::getModifyBy, getUserId()).update();

        //逻辑删除Es话术
        scriptSearchManager.deleteEsScript(scriptId);

        return ResultModel.success(null);
    }

    @RedisLock(prefix = SCRIPT_LOCK_PREFIX)
    public ResultModel<Void> edit(RcScriptEditParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getScriptId()), "话术id错误");
        CntScriptBO bo = new CntScriptBO();
        bo.setScriptId(Long.valueOf(param.getScriptId()));
        bo.setContent(param.getContent());
        bo.setQuestion(param.getQuestion());
        bo.setRemark(param.getRemark());
        if (StringUtils.isNotBlank(param.getCategory1())) {
            bo.setCategory1(Long.valueOf(param.getCategory1()));
        }
        if (StringUtils.isNotBlank(param.getCategory2())) {
            bo.setCategory2(Long.valueOf(param.getCategory2()));
        }
        if (CollectionUtils.isNotEmpty(param.getMaterialIds())) {
            bo.setMaterialIds(param.getMaterialIds().stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        scriptManager.editScript(bo);
        return ResultModel.success(null);
    }

    @SneakyThrows
    public void templateDownload(HttpServletResponse response) {
        RcScriptTemplateDTO sample = RcScriptTemplateDTO.builder().question("商品A的营销介绍").category1("无分类")
                .category2("无分类").content(
                        "示例：这款商品的主面料是棉的，柔软舒服，透气性非常好。具有良好的吸湿透气性，可排除周围多余的水分，让人感觉舒适，提高织物耐洗耐用效果， 面料属于纯天然纤维织物，与皮肤接触无任何刺激，无副作用，亲肤性好。")
                .build();

        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/x-msdownload");
        response.setHeader("Content-Disposition",
                "attachment; filename=" + URLEncoder.encode("话术批量导入模板.xlsx", "utf-8"));
        EasyExcel.write(response.getOutputStream(), RcScriptTemplateDTO.class).excelType(ExcelTypeEnum.XLSX)
                .sheet("话术列表").registerWriteHandler(new ScriptTemplateHandler()).doWrite(Arrays.asList(sample));
    }

    @SneakyThrows
    @RedisLock(prefix = SCRIPT_LOCK_PREFIX)
    public ResultModel<RcScriptUploadResultDTO> upload(MultipartFile file) {
        RcScriptUploadResultDTO vo = new RcScriptUploadResultDTO();
        EasyExcel.read(file.getInputStream(), CntScriptBatchBO.class, new ScriptUploadListener(vo))
                .sheet().doRead();
        return ResultModel.success(vo);
    }

    @SneakyThrows
    public void downloadFailedList(String batchId, HttpServletResponse response) {
        ScriptUploadListener.ScriptUploadFailResult[] failedResult = JsonUtils
                .fromJSON(redisUtil.get("script_batch_" + batchId),
                        ScriptUploadListener.ScriptUploadFailResult[].class);
        List<RcScriptUploadFailedDTO> list = new ArrayList<>();
        if (Objects.nonNull(failedResult)) {
            list.addAll(Arrays.stream(failedResult).map(f -> {
                RcScriptUploadFailedDTO vo = new RcScriptUploadFailedDTO();
                vo.setReason(f.getReason());
                vo.setCategory1(f.getCategory1Name());
                vo.setCategory2(f.getCategory2Name());
                vo.setContent(f.getContent());
                vo.setQuestion(f.getQuestion());
                return vo;
            }).collect(Collectors.toList()));
        }
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/x-msdownload");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("导入失败明细.xlsx", "utf-8"));
        EasyExcel.write(response.getOutputStream(), RcScriptUploadFailedDTO.class).excelType(ExcelTypeEnum.XLSX)
                .sheet("上传失败列表").registerWriteHandler(new ScriptFailedHandler()).doWrite(list);
    }

    public ResultModel publishOrCancel(Long scriptId) {
        scriptManager.publishOrCancel(scriptId);
        return ResultModel.success(null);
    }
}
