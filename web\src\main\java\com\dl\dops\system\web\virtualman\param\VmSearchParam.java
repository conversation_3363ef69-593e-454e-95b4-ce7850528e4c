package com.dl.dops.system.web.virtualman.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class VmSearchParam extends AbstractPageParam {

    @ApiModelProperty("数字人名称")
    String name;

    @ApiModelProperty("数字人来源编码")
    String vmCode;

    Integer channel;

}
