package com.dl.dops.system.web.user.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-06 19:44
 */
@Data
public class AddUserParam {
    @NotBlank
    @Size(min = 1, max = 32, message = "登录账号长度需在1-32位之间")
    @ApiModelProperty("登录账号")
    private String account;

    @NotBlank
    @Length(min = 6, max = 12, message = "密码长度必须在6-12位之间")
    @ApiModelProperty("登录密码")
    private String password;

    @Size(min = 0, max = 32, message = "姓名长度需在0-32位之间")
    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("角色id")
    @Size(max = 10)
    private List<Long> roleIds;
}
