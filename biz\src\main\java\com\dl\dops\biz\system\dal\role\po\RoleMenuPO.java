package com.dl.dops.biz.system.dal.role.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-01 10:44
 */
@Data
@TableName("sys_role_menu")
public class RoleMenuPO extends BasePO {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 菜单id
     */
    private Long menuId;

    /**
     * 角色id
     */
    private Long roleId;

}
