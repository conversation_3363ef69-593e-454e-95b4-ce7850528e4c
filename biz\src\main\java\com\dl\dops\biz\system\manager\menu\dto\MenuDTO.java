package com.dl.dops.biz.system.manager.menu.dto;

import com.dl.dops.biz.system.manager.function.dto.FunctionDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 09:16
 */
@Data
public class MenuDTO implements Serializable {
    private static final long serialVersionUID = -7529929394171175467L;
    @ApiModelProperty("菜单主键")
    private Long menuId;

    @ApiModelProperty("菜单名称")
    private String name;

    @ApiModelProperty("菜单图标")
    private String icon;

    @ApiModelProperty("菜单url")
    private String url;

    @ApiModelProperty("菜单顺序")
    private Integer sort;

    @ApiModelProperty("是否禁用【0-开启1-禁用】")
    private Integer disable = 1;

    @ApiModelProperty("上级菜单id")
    private Long parentId;

    @ApiModelProperty("是否已分配权限，0否1是")
    private Integer owner = 0;

    @ApiModelProperty("菜单等级【1-一级菜单2-二级菜单···】")
    private Integer level;

    @ApiModelProperty("子菜单")
    private List<MenuDTO> children = new ArrayList<>();

    @ApiModelProperty("可配置的权限")
    private List<FunctionDTO> functions = new ArrayList<>();

}
