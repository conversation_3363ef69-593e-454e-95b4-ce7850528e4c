package com.dl.dops.resourcecenter.pack.enums;

/**
 * 服务包元素类型枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 16:41
 */
public enum RcPackElementTypeEnum {
    ARTICLE(1, "文章"),
    WEBPAGE(2, "网页"),
    VIDEO(3, "视频"),
    FILE(4, "文件"),
    TEXT(5, "文本"),
    IMAGE(6, "图片"),
    //MINIAPP(7, "小程序"),
    SCRIPT(8, "话术"),
    POSTER(9, "海报"),
    /*SPECIFIED_PRODUCT(10, "指定产品"),
    SCOPE_OF_PRODUCT_SCREENING(11, "产品筛选范围"),
    VA_DYNAMIC_TEMPLATE(12, "助你拍视频模板")*/;

    private Integer type;

    private String desc;

    RcPackElementTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
