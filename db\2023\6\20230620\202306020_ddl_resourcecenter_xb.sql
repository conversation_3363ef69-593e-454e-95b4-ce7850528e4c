CREATE TABLE `organize` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '表ID',
  `organize_id` bigint NOT NULL COMMENT '组织id',
  `organize_name` varchar(200) NOT NULL COMMENT '组织名称',
  `logo_icon_url` varchar(300) DEFAULT NULL COMMENT '组织logo小图标',
  `logo_high_res_url` varchar(300) DEFAULT NULL COMMENT '组织logo高清图',
  `sort` int NOT NULL DEFAULT '1' COMMENT '排序（数字越大排序越靠前）',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `modify_dt` datetime NOT NULL COMMENT '修改时间',
  `create_by` bigint NOT NULL COMMENT '创建用户',
  `modify_by` bigint NOT NULL COMMENT '创建用户',
  PRIMARY KEY (`id`),
  KEY `idx_organize_id` (`organize_id`),
  KEY `idx_organize_name` (`organize_name`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='金融机构组织';