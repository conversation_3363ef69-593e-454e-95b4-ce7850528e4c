package com.dl.dops.biz.resourcecenter.manager.tag;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagGroupPO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagGroupAddBO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagGroupDeleteBO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagGroupEditBO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagPageBO;
import com.dl.dops.resourcecenter.tag.dto.TagGroupDetailDTO;
import com.dl.dops.resourcecenter.tag.dto.TagGroupPageDTO;

public interface TagGroupManager extends IService<TagGroupPO>, CommonService {
    /**
     * 新增标签组
     * @param groupAddBO
     * @return
     */
    Long add(TagGroupAddBO groupAddBO);

    /**
     * 编辑标签组
     * @param tagGroupEditBO
     * @return
     */
    Long edit(TagGroupEditBO tagGroupEditBO);

    /**
     * 标签组分页
     *
     * @param listBO
     * @return
     */
    IPage<TagGroupPageDTO> page(TagPageBO listBO);

    /**
     * 删除标签组
     *
     * @param deleteBO
     */
    String delete(TagGroupDeleteBO deleteBO);

    /**
     * @param tagGroupId
     * @param needCountTagRela 是否需查询标签关联数，0-否，1-是
     * @return
     */
    TagGroupDetailDTO detail(Long tagGroupId, Integer needCountTagRela);
}
