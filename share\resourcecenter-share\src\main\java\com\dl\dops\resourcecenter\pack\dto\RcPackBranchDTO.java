package com.dl.dops.resourcecenter.pack.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 20:42
 */
@Data
@ApiModel("服务包分支")
public class RcPackBranchDTO implements Serializable {

    private static final long serialVersionUID = -8260864945310825970L;
    @ApiModelProperty("分支id")
    private String branchId;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("排序")
    private Integer sort;
}
