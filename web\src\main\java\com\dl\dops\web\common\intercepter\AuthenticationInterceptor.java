package com.dl.dops.web.common.intercepter;

import com.dl.dops.biz.common.annotation.AdminAuth;
import com.dl.dops.biz.common.annotation.Logical;
import com.dl.dops.biz.common.annotation.NotLogin;
import com.dl.dops.biz.common.annotation.Permission;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.properties.WebIgnoreProperties;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.common.util.TenantAuthUtil;
import com.dl.dops.biz.system.manager.role.RoleManager;
import com.dl.dops.biz.system.manager.user.UserManager;
import com.dl.dops.biz.system.manager.user.dto.BasicUserDTO;
import com.dl.dops.biz.system.manager.user.dto.UserDTO;
import com.dl.dops.system.web.util.AccountCompent;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.framework.core.interceptor.expdto.CertificateException;
import com.dl.framework.core.interceptor.expdto.ForceExitException;
import com.dl.framework.core.interceptor.expdto.SessionTimeoutException;
import io.jsonwebtoken.lang.Collections;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Component
public class AuthenticationInterceptor implements HandlerInterceptor, Ordered {

    private final PathMatcher pathMatcher = new AntPathMatcher();
    @Autowired
    private UserManager userManager;
    @Autowired
    private RoleManager roleManager;
    @Autowired
    private AccountCompent accountCompent;
    @Autowired
    private WebIgnoreProperties properties;
    @Autowired
    private OperatorUtil operatorUtil;

    private boolean isIgnoreUrl(HttpServletRequest request) {
        String url = request.getRequestURI();
        List<String> urlPatterns = properties.getUrls();
        if (Collections.isEmpty(urlPatterns)) {
            return false;
        }
        for (String urlPattern : urlPatterns) {
            if (TenantAuthUtil.getPathMatcher().match(urlPattern, url)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        //判断是否为B端，B端请求不走此判断
        if (TenantAuthUtil.isFromTenantAuth(request) || TenantAuthUtil.isFromB(request)) {
            return true;
        }
        //判断是否忽略url
        if (isIgnoreUrl(request)) {
            return true;
        } else if (handler instanceof ResourceHttpRequestHandler) {
            //静态资源
            return false;
        }

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        //2、判断 NotLogin，有则跳过认证
        if (method.isAnnotationPresent(NotLogin.class)) {
            NotLogin loginToken = method.getAnnotation(NotLogin.class);
            if (loginToken.required()) {
                return true;
            }
        }

        // 从 http 请求头中取出 token
        String token = request.getHeader(Const.TOKEN_HEADER_NAME);
        //1、如果token为空的情况下，返回非法token，禁止访问
        if (StringUtils.isEmpty(token)) {
            //非法token
            throw new CertificateException();
        }
        BasicUserDTO dto;
        try {
            dto = userManager.parseJwtToken(token);
            token = userManager.formatToken(token);
        } catch (Exception e) {
            throw new CertificateException("非法请求");
        }

        //2、redis中登录超时
        String sessionToken = userManager.getSessionToken(dto.getUserId());
        if (StringUtils.isEmpty(sessionToken)) {
            throw new SessionTimeoutException();
        }
        //3、判断是否被其他登录踢掉
        if (!token.equals(sessionToken)) {
            throw new ForceExitException();
        }
        UserDTO detail = accountCompent.getCurrentDetail();
        //biz端的用户注入
        operatorUtil.init(dto.getUserId(), detail.getUserName());
        //刷新用户缓存
        userManager.refreshToken(dto.getUserId());

        //是否超管  //判断功能是否要求admin权限
        if (Objects.equals(detail.getIsSuperAdm(), 1)) {
            return true;
        } else if (method.isAnnotationPresent(AdminAuth.class)) {
            //功能需要admin权限
            throw BusinessServiceException.getInstance("该用户无此权限");
        }

        // 检查是否有 permission 注释，没有则跳过权限验证
        if (method.isAnnotationPresent(Permission.class)) {
            Permission permission = method.getAnnotation(Permission.class);
            if (!permission.required()) {
                return true;
            }
            if (!permissionLogic(permission, dto.getUserId())) {
                throw BusinessServiceException.getInstance("该用户无此权限");
            }
        }

        return true;
    }

    /**
     * 权限判断
     *
     * @param permission
     * @param userId
     * @return
     */
    private boolean permissionLogic(Permission permission, Long userId) {
        //是否判断权限
        if (!permission.required()) {
            return true;
        }
        Set<Long> roleIds = roleManager.findUserRoleByUserId(userId);
        String[] functionCodes = permission.value();
        //权限码逻辑关系 AND  OR
        Logical logical = permission.logical();
        if (functionCodes != null && functionCodes.length > 0) {
            for (String funCode : functionCodes) {
                boolean hasPermission = userManager.hasPermission(roleIds, funCode);
                if (logical == Logical.AND) {
                    if (!hasPermission) {
                        return false;
                    }
                } else {
                    if (hasPermission) {
                        return true;
                    }
                }
            }
        }
        return permission.logical() == Logical.AND;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) {
        //去除biz端的用户注入
        operatorUtil.remove();
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
