package com.dl.dops.biz.resourcecenter.dal.pack;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.dops.biz.common.annotation.BaseDao;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackChainPathPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 10:04
 */
@BaseDao
@DS("resourcecenter")
public interface PackChainPathMapper extends BaseMapper<PackChainPathPO> {

    void batchUpdate(@Param("list") List<PackChainPathPO> list);

    void batchLogicDeleteByChainPathIds(@Param("list") List<Long> list, @Param("operatorId") Long operatorId);
}
