package com.dl.dops.biz.system.manager.role.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.system.dal.role.RoleFunctionMapper;
import com.dl.dops.biz.system.dal.role.RoleMapper;
import com.dl.dops.biz.system.dal.role.RoleMenuMapper;
import com.dl.dops.biz.system.dal.role.po.RoleFunctionPO;
import com.dl.dops.biz.system.dal.role.po.RoleMenuPO;
import com.dl.dops.biz.system.dal.role.po.RolePO;
import com.dl.dops.biz.system.manager.menu.bo.RoleIdsMenuParamBO;
import com.dl.dops.biz.system.manager.menu.bo.RoleMenuParamBO;
import com.dl.dops.biz.system.manager.menu.helper.MenuTreeHelp;
import com.dl.dops.biz.system.manager.role.RoleMenuManager;
import com.dl.dops.biz.system.manager.role.bo.RoleFunctionParamBO;
import com.dl.dops.biz.system.manager.user.impl.UserRoleMenuRedisCache;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 14:29
 */
@Component
@DS("dops")
public class RoleMenuManagerImpl extends ServiceImpl<RoleMenuMapper, RoleMenuPO> implements RoleMenuManager {

    @Autowired
    private RoleFunctionMapper roleFunctionMapper;

    @Autowired
    private UserRoleMenuRedisCache userRoleMenuRedisCache;

    @Autowired
    private RoleMapper roleMapper;

    @Override
    public boolean hasMenu(Long roleId) {
        Assert.notNull(roleId, "角色ID不能为空");
        Integer count = baseMapper.selectCount(Wrappers.<RoleMenuPO>lambdaQuery().eq(RoleMenuPO::getRoleId, roleId));
        return count > 0;
    }

    private void restRoleMenu(RoleMenuParamBO bo) {
        Long roleId = bo.getRoleId();
        Long loginUserId = bo.getLoginUserId();
        List<Long> menuIds = bo.getMenuIds();
        //1、处理角色-菜单
        LambdaQueryWrapper<RoleMenuPO> wrappers = Wrappers.lambdaQuery();
        wrappers.eq(RoleMenuPO::getRoleId, roleId);
        baseMapper.delete(wrappers);
        if (CollectionUtils.isNotEmpty(menuIds)) {
            List<RoleMenuPO> list = menuIds.stream().map(menuId -> {
                RoleMenuPO po = new RoleMenuPO();
                po.setCreateBy(loginUserId);
                po.setRoleId(roleId);
                po.setMenuId(menuId);
                return po;
            }).collect(Collectors.toList());
            saveBatch(list);
        }
        //2、更新缓存
        userRoleMenuRedisCache.updateRoleMenu(bo);
    }

    private void restRoleFunction(RoleMenuParamBO bo) {
        Long roleId = bo.getRoleId();
        Long loginUserId = bo.getLoginUserId();
        List<Long> functionIds = bo.getFunctionIds();
        RoleFunctionParamBO roleFunctionParamBO = new RoleFunctionParamBO();
        roleFunctionParamBO.setRoleId(roleId);
        roleFunctionParamBO.setFunctionIds(functionIds);
        //1、处理角色-功能
        LambdaQueryWrapper<RoleFunctionPO> wrappers = Wrappers.lambdaQuery();
        wrappers.eq(RoleFunctionPO::getRoleId, roleId);
        roleFunctionMapper.delete(wrappers);
        if (CollectionUtils.isNotEmpty(functionIds)) {
            List<RoleFunctionPO> list = functionIds.stream().map(f -> {
                RoleFunctionPO po = new RoleFunctionPO();
                po.setCreateBy(loginUserId);
                po.setRoleId(roleId);
                po.setFunctionId(f);
                return po;
            }).collect(Collectors.toList());
            for (RoleFunctionPO po : list) {
                roleFunctionMapper.insert(po);
            }
        }
        //更新缓存
        userRoleMenuRedisCache.updateRoleFunction(roleFunctionParamBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveRoleMenu(RoleMenuParamBO bo) {
        MenuTreeHelp.validate(roleMapper, Lists.newArrayList(bo.getRoleId()), RolePO::getRoleId, "roleId参数非法");
        restRoleMenu(bo);
        restRoleFunction(bo);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delRoleMenu(RoleIdsMenuParamBO bo) {
        deleteRoleMenu(bo);
        deleteRoleFunction(bo);
        return true;
    }

    private void deleteRoleMenu(RoleIdsMenuParamBO bo) {
        List<Long> roleIds = bo.getRoleIds();
        List<Long> menuIds = bo.getMenuIds();

        //1、获取角色原有菜单
        LambdaQueryWrapper<RoleMenuPO> q = Wrappers.lambdaQuery();
        q.in(RoleMenuPO::getRoleId, roleIds);
        List<RoleMenuPO> cmenus = baseMapper.selectList(q);
        List<Long> delMenuIds = new ArrayList<>();
        List<Long> updateMenuIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cmenus)) {
            cmenus.forEach(m -> {
                if (menuIds.contains(m.getMenuId())) {
                    updateMenuIds.add(m.getMenuId());
                } else {
                    delMenuIds.add(m.getMenuId());
                }
            });
        }

        //2.删除已去掉权限的菜单
        if (CollectionUtils.isNotEmpty(delMenuIds)) {
            LambdaQueryWrapper<RoleMenuPO> wrappers = Wrappers.lambdaQuery();
            wrappers.in(RoleMenuPO::getRoleId, roleIds).in(RoleMenuPO::getMenuId, delMenuIds);
            baseMapper.delete(wrappers);
        }

        //3.更新缓存
        if (CollectionUtils.isNotEmpty(updateMenuIds)) {
            roleIds.forEach(r -> {
                RoleMenuParamBO roleMenuParamBO = new RoleMenuParamBO();
                roleMenuParamBO.setMenuIds(updateMenuIds);
                roleMenuParamBO.setRoleId(r);
                roleMenuParamBO.setLoginUserId(bo.getLoginUserId());
                userRoleMenuRedisCache.updateRoleMenu(roleMenuParamBO);
            });
        }
    }

    private void deleteRoleFunction(RoleIdsMenuParamBO bo) {
        List<Long> roleIds = bo.getRoleIds();
        List<Long> functionIds = bo.getFunctionIds();

        //1、获取角色原有功能权限
        LambdaQueryWrapper<RoleFunctionPO> q = Wrappers.lambdaQuery();
        q.in(RoleFunctionPO::getRoleId, roleIds);
        List<RoleFunctionPO> cfs = roleFunctionMapper.selectList(q);
        List<Long> delFunctionIds = new ArrayList<>();
        List<Long> updateFunctionIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cfs)) {
            cfs.forEach(m -> {
                if (functionIds.contains(m.getFunctionId())) {
                    updateFunctionIds.add(m.getFunctionId());
                } else {
                    delFunctionIds.add(m.getFunctionId());
                }
            });
        }

        //2.删除已去掉权限的功能权限
        if (CollectionUtils.isNotEmpty(delFunctionIds)) {
            LambdaQueryWrapper<RoleFunctionPO> wrappers = Wrappers.lambdaQuery();
            wrappers.in(RoleFunctionPO::getRoleId, roleIds).in(RoleFunctionPO::getFunctionId, delFunctionIds);
            roleFunctionMapper.delete(wrappers);
        }

        //3.更新缓存
        if (CollectionUtils.isNotEmpty(updateFunctionIds)) {
            roleIds.forEach(r -> {
                RoleFunctionParamBO roleFunctionParamBO = new RoleFunctionParamBO();
                roleFunctionParamBO.setFunctionIds(updateFunctionIds);
                roleFunctionParamBO.setRoleId(r);
                roleFunctionParamBO.setLoginUserId(bo.getLoginUserId());
                userRoleMenuRedisCache.updateRoleFunction(roleFunctionParamBO);
            });
        }
    }
}
