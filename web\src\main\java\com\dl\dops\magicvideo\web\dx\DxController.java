package com.dl.dops.magicvideo.web.dx;

import com.dl.dops.biz.common.forest.magicvideo.dto.*;
import com.dl.dops.biz.common.forest.magicvideo.param.*;
import com.dl.dops.biz.magicvideo.manager.MagicVideoAiJobManager;
import com.dl.dops.biz.magicvideo.manager.MagicVideoManager;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-15 15:39
 */
@RestController
@RequestMapping("/dops/magicvideo/dx")
@Api("ai任务统计管理")
public class DxController {

    @Resource
    private MagicVideoManager magicVideoManager;

    @PostMapping("/list")
    @ApiOperation("长图列表")
    public ResultPageModel<DxLongImageListDTO> list(@RequestBody @Validated DxLongImageListParam param) {
        return magicVideoManager.dxLongImageList(param);
    }

    @PostMapping("/add")
    @ApiOperation("添加长图")
    public ResultModel<Void> add(@RequestBody @Validated DxLongImageAddParam param) {
        return magicVideoManager.dxLongImageAdd(param);
    }

    @PostMapping("/detail")
    @ApiOperation("详情")
    public ResultModel<DxLongImageDetailDTO> detail(@RequestBody @Validated DxLongImageDetailParam param) {
        return magicVideoManager.dxLongImageDetail(param);
    }

    @PostMapping("/update")
    @ApiOperation("修改长图")
    public ResultModel<Void> update(@RequestBody @Validated DxLongImageUpdateParam param) {
        return magicVideoManager.dxLongImageUpdate(param);
    }

}
