package com.dl.dops.biz.magicvideo.manager;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.dops.biz.common.forest.magicvideo.MagicVideoClient;
import com.dl.dops.biz.common.forest.magicvideo.dto.*;
import com.dl.dops.biz.common.forest.magicvideo.param.*;
import com.dl.dops.biz.system.dal.user.po.UserPO;
import com.dl.dops.biz.system.manager.user.UserManager;
import com.dl.dops.magicvideo.deliveryplan.dto.DeliveryPlanDTO;
import com.dl.dops.magicvideo.deliveryplan.dto.VisualTemplateInternalDTO;
import com.dl.dops.magicvideo.deliveryplan.param.AddDeliveryPlanBO;
import com.dl.dops.magicvideo.deliveryplan.param.DeliveryPlanPageQueryBO;
import com.dl.dops.magicvideo.deliveryplan.param.ProduceBatchPageQueryBO;
import com.dl.dops.magicvideo.deliveryplan.param.TemplateInternalPageQueryBO;
import com.dl.dops.magicvideo.deliveryplan.param.UpdateDeliveryPlanBO;
import com.dl.dops.magicvideo.font.dto.PatternFontDTO;
import com.dl.dops.magicvideo.font.param.PatternFontPageParam;
import com.dl.dops.magicvideo.font.param.PatternFontParam;
import com.dl.dops.magicvideo.music.bo.BackgroundMusicBO;
import com.dl.dops.magicvideo.music.bo.BackgroundMusicPageBO;
import com.dl.dops.magicvideo.music.dto.BackGroundMusicInternalDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MagicVideoManager {

    @Autowired
    private HostTimeIdg hostTimeIdg;

    @Autowired
    private UserManager userManager;

    @Autowired
    private MagicVideoClient magicVideoClient;

    public String addBackgroundMusic(BackgroundMusicBO bo){
        try {
            ResultModel<String> resultModel = magicVideoClient.addBackgroundMusic(bo);
            if (!resultModel.isSuccess()){
                log.error("添加背景音乐失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
                throw BusinessServiceException.getInstance("添加背景音乐失败");
            }
            return resultModel.getDataResult();
        }catch (Exception e){
            log.error("添加音乐失败");
            throw BusinessServiceException.getInstance(e.getMessage());
        }
    }

    public IPage<BackGroundMusicInternalDTO> pageQueryBackgroundMusic(BackgroundMusicPageBO bo){
        ResultPageModel<BackGroundMusicInternalDTO> resultModel = magicVideoClient.pageQueryBackgroundMusic(
                bo);
        if (!resultModel.isSuccess()){
            log.error("查询背景音乐失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询背景音乐失败");
        }
        if (CollectionUtils.isEmpty(resultModel.getDataResult())){
            return new Page<>(bo.getPageIndex(), bo.getPageSize());
        }
        //查询创建人
        List<Long> userIdList = extractPropertyList(resultModel.getDataResult(), BackGroundMusicInternalDTO::getUserId);
        List<UserPO> userList = userManager.list(Wrappers.lambdaQuery(UserPO.class).in(UserPO::getUserId, userIdList));
        Map<Long, UserPO> userMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userList)){
             userMap = userList.stream()
                    .collect(Collectors.toMap(UserPO::getUserId, Function.identity(), (a, b) -> a));
        }
        for (BackGroundMusicInternalDTO item : resultModel.getDataResult()){
            item.setUserName(userMap.get(item.getUserId()).getUserName());
        }
        Page<BackGroundMusicInternalDTO> result = new Page<>(resultModel.getPageIndex(), resultModel.getPageSize(),
                resultModel.getTotal());
        result.setRecords(new ArrayList<>(resultModel.getDataResult()));
        result.setPages(result.getPages());
        return result;
    }

    public Boolean updateBackgroundMusic(BackgroundMusicBO bo){
        ResultModel<Boolean> resultModel = magicVideoClient.updateBackgroundMusic(bo);
        if (!resultModel.isSuccess()){
            log.error("修改背景音乐失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("修改背景音乐失败");
        }
        return resultModel.getDataResult();
    }

    public Boolean delBackgroundMusic(BackgroundMusicBO bo){
        ResultModel<Boolean> resultModel = magicVideoClient.delBackgroundMusic(bo);
        if (!resultModel.isSuccess()){
            log.error("删除背景音乐失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("删除背景音乐失败");
        }
        return resultModel.getDataResult();
    }

    public void addDeliveryPlan(AddDeliveryPlanBO bo) {
        ResultModel<Void> resultModel = magicVideoClient.addDeliveryPlan(bo);
        if (!resultModel.isSuccess()){
            log.error("新增交付计划失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance(resultModel.getMessage());
        }
    }

    public ResultPageModel<DeliveryPlanDTO> listDeliveryPlan(DeliveryPlanPageQueryBO param) {
        ResultPageModel<DeliveryPlanDTO> resultModel = magicVideoClient.listDeliveryPlan(param);
        if (!resultModel.isSuccess()){
            log.error("查询交付计划失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询交付计划失败");
        }

        return resultModel;
    }

    public ResultPageModel<VisualTemplateInternalDTO> templateList(TemplateInternalPageQueryBO param) {
        ResultPageModel<VisualTemplateInternalDTO> resultModel = magicVideoClient.templateList(param);
        if (!resultModel.isSuccess()){
            log.error("查询模板列表失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询模板列表失败");
        }

        return resultModel;
    }

    public String addPatternFont(PatternFontParam param){
        ResultModel<String> resultModel = magicVideoClient.addPatternFont(param);
        if (!resultModel.isSuccess()){
            log.error("添加花字失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("添加花字失败");
        }
        return resultModel.getDataResult();
    }

    public ResultModel<Boolean> updatePatternFont(PatternFontParam param){
        ResultModel<Boolean> resultModel = magicVideoClient.updatePatternFont(param);
        if (!resultModel.isSuccess()){
            log.error("修改花字失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("修改花字失败");
        }
        return resultModel;
    }

    public ResultPageModel<PatternFontDTO> pageQueryPatternFont(PatternFontPageParam param){
        ResultPageModel<PatternFontDTO> resultModel = magicVideoClient.pageQueryPatternFont(param);
        if (!resultModel.isSuccess()){
            log.error("分页查询花字失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("分页查询花字失败");
        }
        if (CollectionUtils.isEmpty(resultModel.getDataResult())){
            return new ResultPageModel<>();
        }
        //查询创建人
        List<Long> userIdList = extractPropertyList(resultModel.getDataResult(), PatternFontDTO::getUserId);
        List<UserPO> userList = userManager.list(Wrappers.lambdaQuery(UserPO.class).in(UserPO::getUserId, userIdList));
        Map<Long, UserPO> userMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userList)){
            userMap = userList.stream()
                    .collect(Collectors.toMap(UserPO::getUserId, Function.identity(), (a, b) -> a));
        }
        for (PatternFontDTO item : resultModel.getDataResult()){
            item.setUserName(userMap.get(item.getUserId()).getUserName());
        }
        return resultModel;
    }

    public ResultModel<PatternFontDTO> patternFontDetail(PatternFontParam param){
        ResultModel<PatternFontDTO> resultModel = magicVideoClient.patternFontDetail(param);
        if (!resultModel.isSuccess()){
            log.error("查询花字详情失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询花字详情失败");
        }
        return resultModel;
    }

    public ResultModel<Boolean> deletePatternFont(PatternFontParam param){
        ResultModel<Boolean> resultModel = magicVideoClient.deletePatternFont(param);
        if (!resultModel.isSuccess()){
            log.error("查询花字详情失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询花字详情失败");
        }
        return resultModel;
    }

    public void updateDeliveryPlan(UpdateDeliveryPlanBO param) {
        ResultModel<Void> resultModel = magicVideoClient.updateDeliveryPlan(param);
        if (!resultModel.isSuccess()){
            log.error("更新交付计划失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("更新交付计划失败");
        }
    }

    public static <T, R> List<R> extractPropertyList(Collection<T> list, Function<T, R> propertyExtractor) {
        return list.stream()
                .map(propertyExtractor)
                .collect(Collectors.toList());
    }

    /**
     * 批次列表
     * @param param
     * @return
     */
    public ResultPageModel<ProduceBatchDTO> batchList(ProduceBatchPageQueryBO param){
        ResultPageModel<ProduceBatchDTO> resultModel = magicVideoClient.batchList(param);
        if (!resultModel.isSuccess()){
            log.error("获取批次列表失败 resultModel = {}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("获取批次列表失败");
        }
        return resultModel;
    }

    /**
     * 批次下作业列表
     * @param param
     * @return
     */
    public ResultPageModel<ProduceJobDTO> jobListByBatchId(ProduceJobPageQueryParam param){
        ResultPageModel<ProduceJobDTO> result = magicVideoClient.jobListByBatchId(param);
        if (!result.isSuccess()){
            log.error("批次下作业列表失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("获取批次下作业列表失败");
        }
        return result;
    }

    /**
     * 取消批次
     * @param batchId
     * @return
     */
    public ResultModel<Void> cancelBatch(Long batchId){
        ResultModel<Void> result = magicVideoClient.cancelBatch(batchId);
        if (!result.isSuccess()){
            log.error("取消批次失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("取消批次失败");
        }
        return result;
    }

    /**
     * 作业详情
     * @param jobId
     * @return
     */
    public ResultModel<ProduceJobDTO> jobDetail(Long jobId){
        ResultModel<ProduceJobDTO> result = magicVideoClient.jobDetail(jobId);
        if (!result.isSuccess()){
            log.error("获取作业详情失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("获取作业详情失败");
        }
        return result;
    }

    /**
     * 当日生产统计
     * @return
     */
    public ResultModel<DailyProduceStatisticsDTO> dailyStatistics(){
        ResultModel<DailyProduceStatisticsDTO> result = magicVideoClient.dailyStatistics();
        if (!result.isSuccess()){
            log.error("当日生产统计失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("当日生产统计失败");
        }
        return result;
    }

    /**
     * 删除作品
     * @param jobId
     * @return
     */
    public ResultModel<Void> deleteWorks(Long jobId){
        ResultModel<Void> result = magicVideoClient.deleteWorks(jobId);
        if (!result.isSuccess()){
            log.error("删除作品失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("删除作品失败");
        }
        return result;
    }

    /**
     * 生产数量统计
     * @param param
     * @return
     */
    public ResultModel<List<CountVO>> count(CountParam param){
        ResultModel<List<CountVO>> result = magicVideoClient.count(param);
        if (!result.isSuccess()){
            log.error("生产数量统计失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("生产数量统计失败");
        }
        return result;
    }

    /**
     * 生产效率统计
     * @param param
     * @return
     */
    public ResultModel<List<EfficiencyVO>> efficiency(EfficiencyParam param){
        ResultModel<List<EfficiencyVO>> result = magicVideoClient.efficiency(param);
        if (!result.isSuccess()){
            log.error("生产效率统计失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("生产效率统计失败");
        }
        return result;
    }

    /**
     * 系统模板列表
     * @param param
     * @return
     */
    public ResultPageModel<TemplateAuthDTO> list(TemplateAuthPageQueryParam param){
        ResultPageModel<TemplateAuthDTO> result = magicVideoClient.list(param);
        if (!result.isSuccess()){
            log.error("获取系统模板列表失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("获取系统模板列表失败");
        }
        return result;
    }

    /**
     * 创建系统模板并授权
     * @param param
     * @return
     */
    public ResultModel<TemplateAuthDTO> addAndAuth(AddTemplateAndAuthParam param){
        ResultModel<TemplateAuthDTO> result = magicVideoClient.addAndAuth(param);
        if (!result.isSuccess()){
            log.error("创建系统模板并授权失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("创建系统模板并授权失败");
        }
        return result;
    }

    /**
     * 修改授权
     * @param param
     * @return
     */
    public ResultModel<Void> auth(AuthParam param){
        ResultModel<Void> result = magicVideoClient.auth(param);
        if (!result.isSuccess()){
            log.error("修改授权失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("修改授权失败");
        }
        return result;
    }

    /**
     * 系统模板启停
     *
     * @param param
     * @return
     */
    public ResultModel<Void> switchStatus(SwitchStatusParam param) {
        ResultModel<Void> result = magicVideoClient.switchStatus(param);
        if (!result.isSuccess()) {
            log.error("系统模板启停失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("系统模板启停失败");
        }
        return result;
    }

    /**
     * 修改系统模板的来源模板
     *
     * @param param
     * @return
     */
    public ResultModel<Void> changeSourceTemplate(ChangeSourceTemplateParam param) {
        ResultModel<Void> result = magicVideoClient.changeSourceTemplate(param);
        if (!result.isSuccess()) {
            log.error("修改系统模板的来源模板失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("修改系统模板的来源模板失败");
        }
        return result;
    }

    /**
     * 模板详情
     *
     * @param param
     * @return
     */
    public ResultModel<VisualTemplateInternalVO> detail(DetailParam param) {
        ResultModel<VisualTemplateInternalVO> result = magicVideoClient.detail(param);
        if (!result.isSuccess()) {
            log.error("获取模板详情失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("获取模板详情失败");
        }
        return result;
    }

    /**
     * 删除模板
     * @param templateId
     * @return
     */
    public ResultModel<Void> delete(Long templateId){
        ResultModel<Void> result = magicVideoClient.delete(templateId);
        if (!result.isSuccess()){
            log.error("删除模板失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("删除模板详情失败");
        }
        return result;
    }

    /**
     * 模板列表
     *
     * @param param
     * @return
     */
    public ResultPageModel<VisualTemplateInternalVO> list(TemplatePageQueryParam param) {
        ResultPageModel<VisualTemplateInternalVO> result = magicVideoClient.list(param);
        if (!result.isSuccess()) {
            log.error("获取模板列表失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("获取模板列表失败失败");
        }
        return result;
    }

    /**
     * 生产失败数据统计
     *
     * @param param
     * @return
     */
    public ResultModel<List<FailJobStatisticsDTO>> failStatistics(FailJobStatisticsParam param) {
        ResultModel<List<FailJobStatisticsDTO>> resultModel = magicVideoClient.failStatistics(param);
        if (!resultModel.isSuccess()) {
            log.error("查询生产失败数据统计失败,param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询生产失败数据统计失败");
        }
        return resultModel;
    }

    /**
     * 分页查询作品
     *
     * @param param
     * @return
     */
    public ResultPageModel<VisualProduceJobPageDTO> pageJob(VisualProduceJobInternalPageParamDTO param) {
        ResultPageModel<VisualProduceJobPageDTO> resultPageModel = magicVideoClient.pageJob(param);
        if (!resultPageModel.isSuccess()) {
            log.error("分页查询作品失败,param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultPageModel));
            throw BusinessServiceException.getInstance("分页查询作品失败");
        }
        return resultPageModel;
    }

    /**
     * 指定租户的任务数据统计汇总
     *
     * @param param
     * @return
     */
    public ResultModel<StatisticsJobTenantSummaryDTO> specificTenantSummary(
            StatisticsJobTenantSummaryQueryParam param) {
        ResultModel<StatisticsJobTenantSummaryDTO> resultModel = magicVideoClient.specificTenantSummary(param);
        if (!resultModel.isSuccess()) {
            log.error("查询指定租户的任务数据统计汇总失败,param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询指定租户的任务数据统计汇总失败");
        }
        return resultModel;
    }

    /**
     * 模板列表
     *
     * @param param
     * @return
     */
    public ResultPageModel<TagVO> tagList(TagPageQueryParam param) {
        ResultPageModel<TagVO> result = magicVideoClient.tagList(param);
        if (!result.isSuccess()) {
            log.error("获取标签列表失败 resultModel = {}", JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("获取标签列表失败失败");
        }
        return result;
    }

    /**
     * 长图列表
     * @param param
     * @return
     */
    public ResultPageModel<DxLongImageListDTO> dxLongImageList(DxLongImageListParam param) {
        ResultPageModel<DxLongImageListDTO> resultPageModel = magicVideoClient.dxLongImageList(param);
        if (!resultPageModel.isSuccess()) {
            log.error("分页查询长图列表失败,param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultPageModel));
            throw BusinessServiceException.getInstance("分页查询长图列表失败");
        }
        return resultPageModel;
    }

    /**
     * 添加长图
     * @param param
     * @return
     */
    public ResultModel<Void> dxLongImageAdd(DxLongImageAddParam param) {
        ResultModel<Void> result = magicVideoClient.dxLongImageAdd(param);
        if (!result.isSuccess()) {
            log.error("添加长图失败,param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("添加长图失败");
        }
        return result;
    }

    /**
     * 长图详情
     * @param param
     * @return
     */
    public ResultModel<DxLongImageDetailDTO> dxLongImageDetail(DxLongImageDetailParam param) {
        ResultModel<DxLongImageDetailDTO> result = magicVideoClient.dxLongImageDetail(param);
        if (!result.isSuccess()) {
            log.error("获取长图详情失败,param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("获取长图详情失败");
        }
        return result;
    }

    /**
     * 添加长图
     * @param param
     * @return
     */
    public ResultModel<Void> dxLongImageUpdate(DxLongImageUpdateParam param) {
        ResultModel<Void> result = magicVideoClient.dxLongImageUpdate(param);
        if (!result.isSuccess()) {
            log.error("修改长图失败,param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("修改长图失败");
        }
        return result;
    }
}
