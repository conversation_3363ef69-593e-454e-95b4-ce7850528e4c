package com.dl.dops.resourcecenter.material.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @ClassName MaterialArticleSpiderParam
 * @Description
 * <AUTHOR>
 * @Date 2022/7/11 10:10
 * @Version 1.0
 **/
@Data
public class RcMpArticleUrlParam implements Serializable {

    @ApiModelProperty("微信公众号文章地址")
    @NotBlank(message = "文章地址必填")
    private String mpArticleSourceUrl;
}
