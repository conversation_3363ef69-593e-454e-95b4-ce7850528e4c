package com.dl.dops.biz.resourcecenter.manager.category.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.resourcecenter.dal.category.CategoryMapper;
import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryDetailBO;
import com.dl.dops.biz.resourcecenter.mq.producer.DeleteCategoryProducer;
import com.dl.dops.biz.resourcecenter.dal.category.po.CategoryPO;
import com.dl.dops.biz.resourcecenter.manager.category.CategoryManager;
import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryBO;
import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryParamBO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.resourcecenter.manager.category.convert.CategoryConvert;
import com.dl.dops.biz.resourcecenter.manager.category.enums.DefaultCategoryEnum;
import com.dl.dops.resourcecenter.category.enums.RcCategoryLevelEnum;
import com.dl.dops.resourcecenter.category.enums.RcCategoryTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName SysCategoryServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/4/7 9:23
 * @Version 1.0
 **/
@Slf4j
@Service
public class CategoryManagerImpl extends ServiceImpl<CategoryMapper, CategoryPO> implements CategoryManager {

    @Resource
    private HostTimeIdg hostTimeIdg;

    @Resource
    private OperatorUtil operatorUtil;

    @Resource
    private DeleteCategoryProducer deleteCategoryProducer;

    @Override
    public List<CategoryDetailBO> listAll(Integer categoryType) {
        Assert.notNull(categoryType, "入参categoryType不能为空");
        List<CategoryPO> cateList = this.baseMapper.selectList(
                Wrappers.lambdaQuery(CategoryPO.class).eq(CategoryPO::getCategoryType, categoryType)
                        .eq(CategoryPO::getIsDeleted, Const.ZERO).orderByDesc(CategoryPO::getId));
        if (CollectionUtils.isEmpty(cateList)) {
            List<CategoryDetailBO> results = Lists.newArrayList();
            results.add(0, buildDefault(DefaultCategoryEnum.ALL, categoryType));
            results.add(buildDefault(DefaultCategoryEnum.UNCLASSIFIED, categoryType));
            return results;
        }
        Map<String, CategoryDetailBO> levelOneMap = cateList.stream()
                .filter(x -> RcCategoryLevelEnum.LEVEL_ONE.getCode().equals(x.getCategoryLevel())).map(CategoryConvert::cnvCategoryPO2DetailBO)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(CategoryBO::getId, Function.identity()));
        cateList.stream().filter(x -> RcCategoryLevelEnum.LEVEL_TWO.getCode().equals(x.getCategoryLevel())).forEach(x -> {
            CategoryDetailBO parent = levelOneMap.get(x.getParentId().toString());
            if (Objects.nonNull(parent)) {
                parent.getChildList().add(CategoryConvert.cnvCategoryPO2DetailBO(x));
            }
        });
        List<CategoryDetailBO> results = cateList.stream()
                .filter(x ->  RcCategoryLevelEnum.LEVEL_ONE.getCode().equals(x.getCategoryLevel()))
                .map(x -> levelOneMap.get(x.getBizId().toString())).collect(Collectors.toList());
        results.add(0, buildDefault(DefaultCategoryEnum.ALL, categoryType));
        results.add(buildDefault(DefaultCategoryEnum.UNCLASSIFIED, categoryType));
        return results;
    }

    @Override
    public List<CategoryBO> pureList(List<Long> categoryIds,Integer categoryType) {
        List<CategoryPO> cateList = this.baseMapper.selectList(
                Wrappers.lambdaQuery(CategoryPO.class)
                        .eq(Objects.nonNull(categoryType),CategoryPO::getCategoryType, categoryType)
                        .in(CollectionUtils.isNotEmpty(categoryIds),CategoryPO::getBizId,categoryIds)
                        .eq(CategoryPO::getIsDeleted, Const.ZERO).orderByDesc(CategoryPO::getId));
        if (CollectionUtils.isEmpty(cateList)) {
            return Collections.emptyList();
        }
        return cateList.stream().map(CategoryConvert::cnvCategoryPO2DetailBO).collect(Collectors.toList());
    }

    private CategoryDetailBO buildDefault(DefaultCategoryEnum cateEnum, Integer categoryType) {
        if (Objects.isNull(cateEnum)) {
            return null;
        }
        CategoryDetailBO categoryBO = new CategoryDetailBO();
        categoryBO.setId(cateEnum.getCode());
        categoryBO.setName(cateEnum.getDesc());
        categoryBO.setCategoryType(categoryType);
        categoryBO.setCategoryLevel(RcCategoryLevelEnum.LEVEL_ONE.getCode());
        categoryBO.setParentId(Const.ZERO.toString());
        return categoryBO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(CategoryParamBO paramBO) {
        Assert.notNull(paramBO, "入参不能为空");
        Assert.isTrue(StringUtils.isBlank(paramBO.getParentId()) || NumberUtils.isNumber(paramBO.getParentId()),
                "请选择正确的父级分类");
        Assert.notNull(paramBO.getCategoryType(), "入参categoryType不能为空");
        Assert.isTrue(StringUtils.isNotBlank(paramBO.getName()) && StringUtils.length(paramBO.getName()) <= 20,
                "分类名不为空且长度应<20");
        CategoryPO newCate = CategoryPO.builder().name(paramBO.getName()).categoryType(paramBO.getCategoryType())
                .categoryLevel(paramBO.getCategoryLevel()).build();
        Long bizId = hostTimeIdg.generateId().longValue();
        newCate.setBizId(bizId);
        if (StringUtils.isNotBlank(paramBO.getParentId())) {
            newCate.setParentId(Long.valueOf(paramBO.getParentId()));
            newCate.setLink(newCate.getParentId() + Const.VERTICAL_LINE + bizId);
        } else if (Objects.equals(paramBO.getCategoryLevel(), Const.ZERO)) {
            newCate.setParentId(0L);
            newCate.setLink(bizId.toString());
        }

        int count = baseMapper.selectCount(
                Wrappers.lambdaQuery(CategoryPO.class).eq(CategoryPO::getCategoryType, newCate.getCategoryType())
                        .eq(CategoryPO::getCategoryLevel, newCate.getCategoryLevel())
                        .eq(CategoryPO::getParentId, newCate.getParentId()).eq(CategoryPO::getName, newCate.getName())
                        .eq(CategoryPO::getIsDeleted, Const.ZERO));
        Assert.isTrue(count < Const.ONE, "该分类已存在");

        baseMapper.insert(newCate);
        return Boolean.TRUE;
    }

    @Override
    public boolean update(CategoryParamBO paramBO) {
        Assert.notNull(paramBO, "入参不能为空");
        Assert.isTrue(NumberUtils.isNumber(paramBO.getId()), "categoryId不合法");
        Assert.notNull(paramBO.getCategoryType(), "入参categoryType不能为空");
        Assert.isTrue(StringUtils.isNotBlank(paramBO.getName()) && StringUtils.length(paramBO.getName()) <= 20,
                "分类名不能为空且不超过20个字符");
        CategoryPO findOne = baseMapper.selectOne(
                Wrappers.lambdaQuery(CategoryPO.class).eq(CategoryPO::getBizId, Long.valueOf(paramBO.getId()))
                        .eq(CategoryPO::getCategoryType, paramBO.getCategoryType())
                        .eq(CategoryPO::getIsDeleted, Const.ZERO));
        Assert.notNull(findOne, "该分类不存在");

        findOne.setName(paramBO.getName());
        CategoryPO findCategoryByName = lambdaQuery().eq(CategoryPO::getCategoryType, findOne.getCategoryType())
                .eq(CategoryPO::getName, findOne.getName()).eq(CategoryPO::getCategoryLevel, findOne.getCategoryLevel())
                .eq(CategoryPO::getParentId, findOne.getParentId()).eq(CategoryPO::getIsDeleted, Const.ZERO).one();
        Assert.isTrue(Objects.isNull(findCategoryByName) || findCategoryByName.getId().equals(findOne.getId()),
                "该分类已存在");
        if (NumberUtils.isNumber(paramBO.getParentId())) {
            findOne.setParentId(Long.valueOf(paramBO.getParentId()));
        }
        updateById(findOne);
        return Boolean.TRUE;
    }

    @Override
    public boolean delete(String categoryId) {
        Assert.isTrue(NumberUtils.isNumber(categoryId), "请选择正确的分类ID");

        CategoryPO currentCategoryPO = baseMapper.selectOne(
                Wrappers.lambdaQuery(CategoryPO.class).eq(CategoryPO::getBizId, Long.valueOf(categoryId))
                        .eq(CategoryPO::getIsDeleted, Const.ZERO));
        Assert.notNull(currentCategoryPO, "该分类不存在");
        currentCategoryPO.setIsDeleted(Const.ONE);

        Date now = new Date();
        currentCategoryPO.setIsDeleted(Const.ONE);
        currentCategoryPO.setModifyDt(now);
        currentCategoryPO.setModifyBy(operatorUtil.getOperator());

        //将该分类删除
        baseMapper.update(currentCategoryPO,
                Wrappers.lambdaUpdate(CategoryPO.class).eq(CategoryPO::getId, currentCategoryPO.getId()));

        //若是二级分类，则直接发送mq。
        if (RcCategoryLevelEnum.LEVEL_TWO.getCode().equals(currentCategoryPO.getCategoryLevel())) {
            //发送mq
            deleteCategoryProducer.sendMQ(currentCategoryPO, null, now, operatorUtil.getOperator());
            return Boolean.TRUE;
        }
        //一级分类
        //查询子分类
        List<CategoryPO> subCategoryList = this
                .list(Wrappers.lambdaQuery(CategoryPO.class).eq(CategoryPO::getParentId, currentCategoryPO.getBizId())
                        .eq(CategoryPO::getIsDeleted, Const.ZERO));
        List<Long> subCategoryBizIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(subCategoryList)) {
            subCategoryBizIdList = subCategoryList.stream().map(CategoryPO::getBizId).collect(Collectors.toList());
            //删除该分类的子分类
            this.update(Wrappers.lambdaUpdate(CategoryPO.class).in(CategoryPO::getBizId, subCategoryBizIdList)
                    .set(CategoryPO::getIsDeleted, Const.ONE).set(CategoryPO::getModifyBy, operatorUtil.getOperator())
                    .set(CategoryPO::getModifyDt, now));
        }
        //发送mq
        deleteCategoryProducer.sendMQ(currentCategoryPO, subCategoryBizIdList, now, operatorUtil.getOperator());
        return Boolean.TRUE;
    }

    @Override
    public CategoryDetailBO getByCategoryId(RcCategoryTypeEnum type, Long categoryId) {
        Assert.notNull(categoryId, "请选择正确的分类ID");
        Assert.notNull(type, "请选择正确的分类业务类型");
        CategoryPO po = lambdaQuery().eq(CategoryPO::getBizId, categoryId)
                .eq(CategoryPO::getCategoryType, type.getCode()).eq(CategoryPO::getIsDeleted, Const.ZERO).one();
        Assert.notNull(po, "该分类不存在");
        CategoryDetailBO categoryBO = CategoryConvert.cnvCategoryPO2DetailBO(po);
        if (Objects.isNull(categoryBO)) {
            return null;
        }
        //若是二级分类，则直接返回
        if (RcCategoryLevelEnum.LEVEL_TWO.getCode().equals(po.getCategoryLevel())) {
            return categoryBO;
        }
        //若是一级分类，则查询其下的分类
        List<CategoryPO> poList = lambdaQuery().eq(CategoryPO::getParentId, categoryId)
                .eq(CategoryPO::getCategoryType, type.getCode()).eq(CategoryPO::getIsDeleted, Const.ZERO).list();
        if (CollectionUtils.isNotEmpty(poList)) {
            List<CategoryDetailBO> childList = poList.stream().map(CategoryConvert::cnvCategoryPO2DetailBO).collect(Collectors.toList());
            categoryBO.setChildList(childList);
        }
        return categoryBO;
    }

}
