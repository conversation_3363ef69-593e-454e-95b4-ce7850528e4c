package com.dl.dops.magicvideo.web.chartlet;

import com.dl.dops.biz.common.forest.magicvideo.dto.InternalChareletDeleteParam;
import com.dl.dops.biz.common.forest.magicvideo.dto.InternalChartletCategoryDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.InternalChartletInfoAddParam;
import com.dl.dops.biz.common.forest.magicvideo.dto.InternalChartletInfoDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.InternalChartletInfoUptParam;
import com.dl.dops.biz.common.forest.magicvideo.dto.InternalChartletPageParam;
import com.dl.dops.biz.magicvideo.manager.MagicVideoChartletManager;
import com.dl.dops.magicvideo.web.chartlet.param.ChareletDeleteParam;
import com.dl.dops.magicvideo.web.chartlet.param.ChartletInfoAddParam;
import com.dl.dops.magicvideo.web.chartlet.param.ChartletInfoUptParam;
import com.dl.dops.magicvideo.web.chartlet.param.ChartletPageParam;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-16 09:27
 */
@RestController
@RequestMapping("/dops/chartlet")
public class ChartletInfoController extends AbstractController {

    @Resource
    private MagicVideoChartletManager magicVideoChartletManager;

    @ApiOperation("新增贴图")
    @PostMapping("/add")
    public ResultModel<String> add(@RequestBody @Validated ChartletInfoAddParam param) {
        InternalChartletInfoAddParam internalParam = new InternalChartletInfoAddParam();
        internalParam.setName(param.getName());
        internalParam.setContentUrl(param.getContentUrl());
        internalParam.setResolutionRatio(param.getResolutionRatio());
        internalParam.setCoverImg(param.getCoverImg());
        internalParam.setDuration(param.getDuration());
        internalParam.setCreateBy(getUserId());
        internalParam.setCreatorName(getUserName());
        internalParam.setCategory(param.getCategory());
        Long bizId = magicVideoChartletManager.add(internalParam);
        return ResultModel.success(String.valueOf(bizId));
    }

    @ApiOperation("修改贴图")
    @PostMapping("/update")
    public ResultModel<Void> update(@RequestBody @Validated ChartletInfoUptParam param) {
        InternalChartletInfoUptParam internalParam = new InternalChartletInfoUptParam();
        internalParam.setBizId(param.getBizId());
        internalParam.setName(param.getName());
        internalParam.setContentUrl(param.getContentUrl());
        internalParam.setResolutionRatio(param.getResolutionRatio());
        internalParam.setCoverImg(param.getCoverImg());
        internalParam.setDuration(param.getDuration());
        internalParam.setModifyBy(getUserId());
        internalParam.setModifyName(getUserName());
        internalParam.setCategory(param.getCategory());
        magicVideoChartletManager.update(internalParam);
        return ResultModel.success(null);
    }

    @ApiOperation("分页查询贴图信息")
    @PostMapping("/page")
    public ResultPageModel<InternalChartletInfoDTO> page(@RequestBody ChartletPageParam param) {
        InternalChartletPageParam internalParam = new InternalChartletPageParam();
        internalParam.setName(param.getName());
        internalParam.setPageIndex(param.getPageIndex());
        internalParam.setPageSize(param.getPageSize());
        return magicVideoChartletManager.page(internalParam);
    }

    @ApiOperation("删除贴图")
    @PostMapping("/delete")
    public ResultModel<Void> delete(@RequestBody @Validated ChareletDeleteParam param) {
        InternalChareletDeleteParam internalParam = new InternalChareletDeleteParam();
        internalParam.setBizId(param.getBizId());
        internalParam.setModifyBy(getUserId());
        internalParam.setModifyName(getUserName());
        magicVideoChartletManager.delete(internalParam);
        return ResultModel.success(null);
    }

    @ApiOperation("获取全部贴图分类")
    @PostMapping("/listallcategory")
    public ResultModel<List<InternalChartletCategoryDTO>> listAllCategory() {
        return ResultModel.success(magicVideoChartletManager.listAllCategory());
    }

}
