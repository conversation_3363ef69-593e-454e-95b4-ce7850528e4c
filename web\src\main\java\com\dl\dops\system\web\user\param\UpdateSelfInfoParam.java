package com.dl.dops.system.web.user.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-05 16:17
 */
@Data
@ApiModel("修改个人信息")
public class UpdateSelfInfoParam {
    @ApiModelProperty("账号")
    @Size(min = 4, max = 32, message = "账号长度最长32位")
    private String account;

    @ApiModelProperty("姓名")
    @Size(min = 0, max = 64, message = "姓名长度最长64位")
    private String userName;

    private Long userId;

    private Integer status;

}
