package com.dl.dops.biz.common.forest.aiservice;

import com.dl.aiservice.share.digitalasset.DaVirtualManAuthDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManPageRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceAuthorizeDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceChannelDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDetailDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoicePageDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceUpdateDTO;
import com.dl.aiservice.share.digitalman.DigitalManInfoDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainParamDTO;
import com.dl.aiservice.share.voiceclone.AudioTrainResponseDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainJobDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainJobPageQueryDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultPageQueryDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultUpdateNameParamDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;

import java.util.List;

/**
 * @describe: AiServiceClient
 * @author: zhousx
 * @date: 2023/3/16 16:31
 */
@BaseRequest(interceptor = AiServiceAuthInterceptor.class)
public interface AiServiceClient {

    String TTS_URL = "/voice/clone/tts";

    String VOICE_TRAIN_URL = "/voice/clone/audio/train";

    /**
     * 查询单个数字人信息
     *
     * @param bizId
     * @return
     */
    @Post("/digital/asset/vm/one")
    ResultModel<DigitalManInfoDTO> query(@Query("bizId") Long bizId);

    @Post("/digital/asset/vm/page")
    ResultPageModel<DaVirtualManDTO> pageVm(@JSONBody DaVirtualManPageRequestDTO param);

    @Post("/digital/asset/vm/saveOrUpdate")
    ResultModel<String> vmSaveOrUpdate(@JSONBody DaVirtualManDTO param);

    @Post("/digital/asset/vm/scene/list")
    ResultModel<List<DaVirtualManScenesDTO>> vmSceneList(@JSONBody DaVirtualManRequestDTO param);

    @Post("/digital/asset/vm/scene/saveOrUpdate")
    ResultModel sceneSaveOrUpdate(@JSONBody DaVirtualManScenesDTO param);

    @Post("/digital/asset/vm/auth")
    ResultModel vmAuth(@JSONBody DaVirtualManAuthDTO param);

    @Post("/digital/voice/page")
    ResultPageModel<DaVirtualVoiceDTO> voicePage(@JSONBody DaVirtualVoicePageDTO param);

    @Post("/digital/voice/pageSearch")
    ResultPageModel<DaVirtualVoiceDTO> pageSearch(@JSONBody DaVirtualVoicePageDTO param);

    @Post("/digital/voice/detail")
    ResultModel<DaVirtualVoiceDTO> voiceDetail(@JSONBody DaVirtualVoiceDetailDTO param);

    @Post("/digital/voice/saveOrUpdate")
    ResultModel voiceSaveOrUpdate(@JSONBody DaVirtualVoiceDTO param);

    @Post("/digital/voice/authorize")
    ResultModel voiceAuthorize(@JSONBody DaVirtualVoiceAuthorizeDTO param);

    @Post("/digital/voice/enable")
    ResultModel voiceEnable(@JSONBody DaVirtualVoiceUpdateDTO param);

    @Post("/digital/voice/delete")
    ResultModel voiceDelete(@JSONBody DaVirtualVoiceUpdateDTO param);

    @Get("/digital/voice/channel")
    ResultModel<List<DaVirtualVoiceChannelDTO>> voiceChannel();

    /**
     * 分页查询声音训练结果
     *
     * @param queryDTO
     * @return
     */
    @Post("/voice/clone/trainresult/page")
    ResultPageModel<VoiceTrainResultDTO> pageTrainResult(@JSONBody VoiceTrainResultPageQueryDTO queryDTO);

    /**
     * 修改声音训练结果的训练名
     *
     * @param paramDTO
     * @return
     */
    @Post("/voice/clone/trainresult/updatename")
    ResultModel<Void> updateTrainResultName(@JSONBody VoiceTrainResultUpdateNameParamDTO paramDTO);

    /**
     * 分页查询声音训练结果下的训练任务
     *
     * @param queryDTO
     * @return
     */
    @Post("/voice/clone/trainresult/job/page")
    ResultPageModel<VoiceTrainJobDTO> pageTrainResultJob(@JSONBody VoiceTrainJobPageQueryDTO queryDTO);

    /**
     * 声纹模型训练
     *
     * @param paramDTO
     * @return
     */
    @Post("/voice/clone/audio/train")
    ResultModel<AudioTrainResponseDTO> audioTrain(Integer channel, @JSONBody AudioTrainParamDTO paramDTO);

    /**
     * 语音合成 TTS
     *
     * @return
     */
    @Post("/voice/clone/tts")
    ResultModel<TTSResponseDTO> tts(Integer channel, @JSONBody TTSProduceParamDTO param);

}
