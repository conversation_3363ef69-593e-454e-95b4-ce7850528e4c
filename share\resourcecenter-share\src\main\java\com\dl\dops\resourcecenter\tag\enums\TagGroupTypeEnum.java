package com.dl.dops.resourcecenter.tag.enums;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public enum TagGroupTypeEnum {

    ARTICLE(1, "文章"),
    WEBPAGE(2, "网页"),
    VIDEO(3, "视频"),
    FILE(4, "文件"),
    IMAGE(5, "图片"),
    INDIVIDUAL_SOP(6, "个人sop"),
    GROUP_SOP(7, "群sop"),
    OPERATE_ASSISTENT_PACK(8, "助你营服务包"),
    VIDEO_ASSISTENT_PACK(9, "快视频服务包");

    private Integer code;

    private String desc;

    private static List<Integer> materialList = new ArrayList<>();

    private static List<Integer> sopList = new ArrayList<>();
    private static List<Integer> packList = new ArrayList<>();

    static {
        materialList.add(ARTICLE.getCode());
        materialList.add(WEBPAGE.getCode());
        materialList.add(VIDEO.getCode());
        materialList.add(FILE.getCode());
        materialList.add(IMAGE.getCode());

        sopList.add(INDIVIDUAL_SOP.getCode());
        sopList.add(GROUP_SOP.getCode());

        packList.add(OPERATE_ASSISTENT_PACK.getCode());
        packList.add(VIDEO_ASSISTENT_PACK.getCode());
    }

    TagGroupTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isMaterial(Integer type) {
        if (Objects.isNull(type)) {
            return false;
        }
        return materialList.contains(type);
    }

    public static boolean isSop(Integer type) {
        if (Objects.isNull(type)) {
            return false;
        }
        return sopList.contains(type);
    }

    public static boolean isPack(Integer type) {
        if (Objects.isNull(type)) {
            return false;
        }
        return packList.contains(type);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static TagGroupTypeEnum parse(Integer value) {
        if (Objects.isNull(value)) {
            return null;
        }
        for (TagGroupTypeEnum type : TagGroupTypeEnum.values()) {
            if (type.code.equals(value)) {
                return type;
            }
        }
        return null;
    }
}
