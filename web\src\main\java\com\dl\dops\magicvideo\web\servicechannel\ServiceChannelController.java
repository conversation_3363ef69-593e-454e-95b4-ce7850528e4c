package com.dl.dops.magicvideo.web.servicechannel;

import com.dl.aiservice.share.enums.DigitalManChannelEnum;
import com.dl.aiservice.share.enums.VoiceChannelEnum;
import com.dl.dops.magicvideo.web.servicechannel.vo.ServiceChannelVO;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-06-05 16:09
 */
@RestController
@RequestMapping("/dops/servicechannel")
public class ServiceChannelController {

    private static List<ServiceChannelVO> dmChannelList = new ArrayList<>();

    private static List<ServiceChannelVO> voiceChannelList = new ArrayList<>();

    static {
        for (DigitalManChannelEnum dmChannelEnum : DigitalManChannelEnum.values()) {
            ServiceChannelVO vo = new ServiceChannelVO();
            vo.setName(dmChannelEnum.getName());
            vo.setCode(dmChannelEnum.getCode());
            dmChannelList.add(vo);
        }

        for (VoiceChannelEnum voiceChannelEnum : VoiceChannelEnum.values()) {
            ServiceChannelVO vo = new ServiceChannelVO();
            vo.setName(voiceChannelEnum.getName());
            vo.setCode(voiceChannelEnum.getCode());
            voiceChannelList.add(vo);
        }
    }

    @ApiOperation("获取数字人厂商列表")
    @GetMapping("/dmchannellist")
    public ResultModel<List<ServiceChannelVO>> dmChannelList() {
        return ResultModel.success(dmChannelList);
    }

    @ApiOperation("获取声音厂商列表")
    @GetMapping("/voicechannellist")
    public ResultModel<List<ServiceChannelVO>> voiceChannelList() {
        return ResultModel.success(voiceChannelList);
    }

}
