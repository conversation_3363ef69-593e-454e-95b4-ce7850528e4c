package com.dl.dops.biz.resourcecenter.manager.tag.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.resourcecenter.dal.material.po.MaterialPO;
import com.dl.dops.biz.resourcecenter.dal.tag.TagRelaMapper;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagRelaPO;
import com.dl.dops.biz.resourcecenter.manager.material.MaterialManager;
import com.dl.dops.biz.resourcecenter.manager.tag.TagRelaManager;
import com.dl.dops.resourcecenter.tag.enums.TagGroupTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class TagRelaManagerImpl extends ServiceImpl<TagRelaMapper, TagRelaPO> implements TagRelaManager {

    @Autowired
    private MaterialManager materialManager;

    @Override
    public void batchUpdateBizModifyDt(Integer bizType, List<Long> bizIds) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return;
        }
        if (TagGroupTypeEnum.isMaterial(bizType)) {
            materialManager.update(Wrappers.lambdaUpdate(MaterialPO.class).in(MaterialPO::getBizId, bizIds)
                    .set(MaterialPO::getModifyDt, new Date()));
        }

        //todo:sop、pack
    }
}
