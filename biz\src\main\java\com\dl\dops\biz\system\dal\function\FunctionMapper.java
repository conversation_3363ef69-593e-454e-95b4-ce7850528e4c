package com.dl.dops.biz.system.dal.function;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.dops.biz.common.annotation.BaseDao;
import com.dl.dops.biz.system.dal.function.po.FunctionPO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-06 16:37
 */
@BaseDao
@DS("dops")
public interface FunctionMapper extends BaseMapper<FunctionPO> {
}
