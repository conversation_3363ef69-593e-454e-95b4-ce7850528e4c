package com.dl.dops.resourcecenter.common.enums;

/**
 * @ClassName PosterScopeEnum
 * @Description 查询搜索范围枚举类
 * <AUTHOR>
 * @Date 2022/4/6 10:41
 * @Version 1.0
 **/
public enum CommonScopeEnum {

    ALL(0, "企业"),

    MINE(1, "我的");

    private Integer code;
    private String desc;

    CommonScopeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
