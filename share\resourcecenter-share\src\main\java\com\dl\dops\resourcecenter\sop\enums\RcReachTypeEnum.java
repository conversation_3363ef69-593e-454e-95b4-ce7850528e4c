package com.dl.dops.resourcecenter.sop.enums;


import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 触达形式
 */
public enum RcReachTypeEnum {

    ONE_KEY_BROADCAST(1, "一键群发客户", 1, false, true, true, "员工群发助手一键群发多个客户", 1),
    BATCH_MASS_GROUP(2, "群发客户群", 1, true, false, true, "员工批量选择我的客户群后发送", 2),
    //OPEN_ROOM_AND_SEND(3, "打开群并发送", 3, true, false, false,"员工打开指定群会话后发送",3),
    WE_COM_PYQ_MASS(4, "群发朋友圈", 1, false, true, true, "员工批量选择我的客户后发送朋友圈", 6),
    REPOST_TO_SESSION(5, "转发到会话", 3, true, true, false, "员工自主选择会话后发送", 7),
    WORK_NOTICE(6, "工作通知", 7, true, true, false, "给员工发送通知指导其完成任务", 8),
    ONE_KEY_WE_COM_PYQ_MASS(7, "一键群发朋友圈", 1, false, false, true, "员工一键群发多个客户朋友圈", 4),
    BATCH_MASS_CONTACT(8, "群发客户", 1, false, true, true, "员工批量选择我的客户后发", 5),
    ONE_KEY_BROADCAST_ROOM(9, "一键群发客户群", 1, false, false, true, "员工群发助手一键群发多个客户群", 9),
    HIGH_LEVEL_BROADCAST_ROOM(10, "高级群发客户群", 7, false, false, false, "高级群发客户群", 10);

    private Integer type;

    private String name;

    /**
     * 有效期
     */
    private Integer validityPeriod;

    /**
     * 群sop是否支持
     */
    private boolean supportByGroupSop;

    /**
     * 个人sop是否支持
     */
    private boolean supportByContactSop;

    /**
     * 是否消耗企微次数
     */
    private boolean consumeWeComTime;

    /**
     * 描述
     */
    private String desc;

    /**
     * 排序
     */
    private Integer sort;

    RcReachTypeEnum(Integer type, String name, Integer validityPeriod, boolean supportByGroupSop,
            boolean supportByContactSop, boolean consumeWeComTime, String desc, Integer sort) {
        this.type = type;
        this.name = name;
        this.validityPeriod = validityPeriod;
        this.supportByGroupSop = supportByGroupSop;
        this.supportByContactSop = supportByContactSop;
        this.consumeWeComTime = consumeWeComTime;
        this.desc = desc;
        this.sort = sort;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public Integer getValidityPeriod() {
        return validityPeriod;
    }

    public boolean supportByGroupSop() {
        return supportByGroupSop;
    }

    public boolean supportByContactSop() {
        return supportByContactSop;
    }

    public boolean consumeWeComTime() {
        return consumeWeComTime;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getSort() {
        return sort;
    }

    public static List<RcReachTypeEnum> getGroupSopEvent() {
        List<RcReachTypeEnum> rcReachTypeEnums = new ArrayList<>();
        for (RcReachTypeEnum rcReachTypeEnum : RcReachTypeEnum.values()) {
            if (rcReachTypeEnum.supportByGroupSop) {
                rcReachTypeEnums.add(rcReachTypeEnum);
            }
        }
        return rcReachTypeEnums;
    }

    public static List<RcReachTypeEnum> getContactSopEvent() {
        List<RcReachTypeEnum> rcReachTypeEnums = new ArrayList<>();
        for (RcReachTypeEnum rcReachTypeEnum : RcReachTypeEnum.values()) {
            if (rcReachTypeEnum.supportByContactSop) {
                rcReachTypeEnums.add(rcReachTypeEnum);
            }
        }
        return rcReachTypeEnums;
    }

    public static RcReachTypeEnum parse(Integer reachType) {
        if (Objects.isNull(reachType)) {
            return null;
        }
        for (RcReachTypeEnum rcReachTypeEnum : RcReachTypeEnum.values()) {
            if (rcReachTypeEnum.getType().equals(reachType)) {
                return rcReachTypeEnum;
            }
        }
        return null;
    }

    public static String getNameByType(Integer reachType) {
        RcReachTypeEnum rcReachTypeEnum = parse(reachType);
        if (Objects.isNull(rcReachTypeEnum)) {
            return "";
        }
        return rcReachTypeEnum.getName();
    }

    public static boolean judgeIsSupportByGroupSop(Integer reachType) {
        RcReachTypeEnum rcReachTypeEnum = RcReachTypeEnum.parse(reachType);
        if (Objects.isNull(rcReachTypeEnum)) {
            return false;
        }
        return rcReachTypeEnum.supportByGroupSop;
    }

    public static boolean judgeIsSupportByContactSop(Integer reachType) {
        RcReachTypeEnum rcReachTypeEnum = RcReachTypeEnum.parse(reachType);
        if (Objects.isNull(rcReachTypeEnum)) {
            return false;
        }
        return rcReachTypeEnum.supportByContactSop;
    }
}
