package com.dl.dops.biz.resourcecenter.manager.script.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @describe: CntScriptBatchBO
 * @author: zhousx
 * @date: 2022/6/21 10:08
 */
@Data
public class CntScriptBatchBO {
    @ExcelProperty({ "填写须知：", "1、请管理员使用此模版导入，从第8行开始为导入的话术内容；", "2、问题、话术内容均不能为空，为空将导致单条内容导入失败；一级与二级分类若为空，则默认进入“未分类”的类型；",
            "3、单个【问题】最多支持15个汉字，单个【一级/二级分类】最多支持10个汉字，单条【话术内容】最多支持500个汉字，超过部分将被截断；", "4、分类名称需按系统已维护分类填写，未匹配到的分类话术将导入失败。",
            "5、单次最多导入1000条话术，超出数量的话术将导入失败。", "问题" })
    private String question;

    @ExcelProperty({ "填写须知：", "1、请管理员使用此模版导入，从第8行开始为导入的话术内容；", "2、问题、话术内容均不能为空，为空将导致单条内容导入失败；一级与二级分类若为空，则默认进入“未分类”的类型；",
            "3、单个【问题】最多支持15个汉字，单个【一级/二级分类】最多支持10个汉字，单条【话术内容】最多支持500个汉字，超过部分将被截断；", "4、分类名称需按系统已维护分类填写，未匹配到的分类话术将导入失败。",
            "5、单次最多导入1000条话术，超出数量的话术将导入失败。", "话术内容" })
    private String content;

    @ExcelProperty({ "填写须知：", "1、请管理员使用此模版导入，从第8行开始为导入的话术内容；", "2、问题、话术内容均不能为空，为空将导致单条内容导入失败；一级与二级分类若为空，则默认进入“未分类”的类型；",
            "3、单个【问题】最多支持15个汉字，单个【一级/二级分类】最多支持10个汉字，单条【话术内容】最多支持500个汉字，超过部分将被截断；", "4、分类名称需按系统已维护分类填写，未匹配到的分类话术将导入失败。",
            "5、单次最多导入1000条话术，超出数量的话术将导入失败。", "一级分类" })
    private String category1Name;

    @ExcelProperty({ "填写须知：", "1、请管理员使用此模版导入，从第8行开始为导入的话术内容；", "2、问题、话术内容均不能为空，为空将导致单条内容导入失败；一级与二级分类若为空，则默认进入“未分类”的类型；",
            "3、单个【问题】最多支持15个汉字，单个【一级/二级分类】最多支持10个汉字，单条【话术内容】最多支持500个汉字，超过部分将被截断；", "4、分类名称需按系统已维护分类填写，未匹配到的分类话术将导入失败。",
            "5、单次最多导入1000条话术，超出数量的话术将导入失败。", "二级分类" })
    private String category2Name;

    private Long category1Id = 0L;

    private Long category2Id = 0L;
}
