package com.dl.dops.biz.resourcecenter.manager.script.impl;

import cn.easyes.core.biz.PageInfo;
import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.easyes.core.toolkit.EsWrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.resourcecenter.dal.category.po.CategoryPO;
import com.dl.dops.biz.resourcecenter.dal.material.po.MaterialPO;
import com.dl.dops.biz.resourcecenter.dal.script.ScriptMapper;
import com.dl.dops.biz.resourcecenter.dal.script.po.ScriptMaterialPO;
import com.dl.dops.biz.resourcecenter.dal.script.po.ScriptPO;
import com.dl.dops.biz.resourcecenter.es.script.EsIndexRcScriptMapper;
import com.dl.dops.biz.resourcecenter.es.script.po.EsIndexRcScript;
import com.dl.dops.biz.resourcecenter.manager.category.CategoryManager;
import com.dl.dops.biz.resourcecenter.manager.material.MaterialManager;
import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialBO;
import com.dl.dops.biz.resourcecenter.manager.material.helper.MaterialHelper;
import com.dl.dops.biz.resourcecenter.manager.script.ScriptManager;
import com.dl.dops.biz.resourcecenter.manager.script.ScriptMaterialManager;
import com.dl.dops.biz.resourcecenter.manager.script.ScriptSearchManager;
import com.dl.dops.biz.resourcecenter.manager.script.bo.CntScriptBO;
import com.dl.dops.biz.resourcecenter.manager.script.bo.CntScriptBatchBO;
import com.dl.dops.biz.resourcecenter.manager.script.bo.ScriptDetailBO;
import com.dl.dops.biz.resourcecenter.manager.script.bo.ScriptSearchBO;
import com.dl.dops.resourcecenter.script.enums.RcScriptPublishStatusEnum;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【cnt_script】的数据库操作Service实现
 * @createDate 2022-06-14 16:09:16
 */
@Slf4j
@Service
public class ScriptManagerImpl extends ServiceImpl<ScriptMapper, ScriptPO> implements ScriptManager {
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private ScriptMaterialManager scriptMaterialManager;
    @Autowired
    private MaterialManager materialManager;
    @Autowired
    private CategoryManager categoryManager;
    @Autowired
    private EsIndexRcScriptMapper esIndexRcScriptMapper;
    @Resource
    private OperatorUtil operatorUtil;

    @Autowired
    private ScriptSearchManager scriptSearchManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addScript(CntScriptBO bo) {
        ScriptPO script = this.lambdaQuery().eq(ScriptPO::getQuestion, bo.getQuestion()).one();
        Assert.isNull(script, "问题重复");

        Long scriptId = hostTimeIdg.generateId().longValue();
        script = new ScriptPO();
        script.setQuestion(bo.getQuestion());
        script.setScriptId(scriptId);
        script.setCategory1(bo.getCategory1());
        script.setCategory2(bo.getCategory2());
        script.setContent(bo.getContent());
        script.setPublishStatus(bo.getPublishStatus());
        script.setSource(bo.getSource());
        script.setRemark(bo.getRemark());
        script.setCreateBy(operatorUtil.getOperator());
        script.setModifyBy(operatorUtil.getOperator());
        script.setCreatorName(operatorUtil.getOperatorName());
        script.setModifyName(operatorUtil.getOperatorName());
        this.save(script);

        if (CollectionUtils.isNotEmpty(bo.getMaterialIds())) {
            List<ScriptMaterialPO> scriptMaterials = bo.getMaterialIds().stream().map(mId -> {
                ScriptMaterialPO s = new ScriptMaterialPO();
                s.setScriptId(scriptId);
                s.setMaterialId(mId);
                return s;
            }).collect(Collectors.toList());
            scriptMaterialManager.saveBatch(scriptMaterials);
        }
        return scriptId;
    }

    @Override
    public ResponsePageQueryDO<List<ScriptDetailBO>> searchScripts(ScriptSearchBO searchBO) {
        ResponsePageQueryDO<List<ScriptDetailBO>> response = new ResponsePageQueryDO<>();
        LambdaEsQueryWrapper<EsIndexRcScript> wrapper = EsWrappers.lambdaQuery(EsIndexRcScript.class);
        wrapper.eq(EsIndexRcScript::getIsDeleted, false)
                .eq(Objects.nonNull(searchBO.getCategory1()), EsIndexRcScript::getCategory1, searchBO.getCategory1())
                .eq(Objects.nonNull(searchBO.getCategory2()), EsIndexRcScript::getCategory2, searchBO.getCategory2())
                .eq(Objects.nonNull(searchBO.getCreateBy()), EsIndexRcScript::getCreateBy, searchBO.getCreateBy())
                .eq(Objects.nonNull(searchBO.getPublishStatus()), EsIndexRcScript::getPublishStatus,
                        searchBO.getPublishStatus()).and(StringUtils.isNotBlank(searchBO.getKeyword()),
                w -> w.matchPhase(EsIndexRcScript::getContent, searchBO.getKeyword()).or()
                        .matchPhase(EsIndexRcScript::getQuestion, searchBO.getKeyword()))
                .orderByDesc(EsIndexRcScript::getCreateDt);
        PageInfo<EsIndexRcScript> result = esIndexRcScriptMapper
                .pageQuery(wrapper, searchBO.getPageIndex(), searchBO.getPageSize());
        List<EsIndexRcScript> data = result.getList();
        log.info("esIndexCntScriptMapper.pageQuery result={}", JsonUtils.toJSON(data));
        if (CollectionUtils.isEmpty(data)) {
            return response;
        }

        // 素材
        List<ScriptMaterialPO> cntScriptMaterials = new ArrayList<>();
        cntScriptMaterials.addAll(scriptMaterialManager.lambdaQuery().in(ScriptMaterialPO::getScriptId,
                data.stream().map(EsIndexRcScript::getScriptId).collect(Collectors.toList()))
                .eq(ScriptMaterialPO::getIsDeleted, NumberUtils.INTEGER_ZERO).list());
        Map<Long, List<ScriptMaterialPO>> cntScriptMaterialMap = cntScriptMaterials.stream()
                .collect(Collectors.groupingBy(ScriptMaterialPO::getScriptId));

        // 素材详情
        List<MaterialPO> cntMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cntScriptMaterials)) {
            cntMaterials.addAll(materialManager.lambdaQuery().in(MaterialPO::getBizId,
                    cntScriptMaterials.stream().map(ScriptMaterialPO::getMaterialId).distinct()
                            .collect(Collectors.toList())).eq(MaterialPO::getIsDeleted, Const.ZERO).list());
        }
        Map<Long, MaterialPO> cntMaterialMap = cntMaterials.stream()
                .collect(Collectors.toMap(MaterialPO::getBizId, Function.identity()));

        // 分类
        List<Long> categoryIds = new ArrayList<>();
        categoryIds.addAll(data.stream().map(EsIndexRcScript::getCategory1).distinct().collect(Collectors.toList()));
        categoryIds.addAll(data.stream().map(EsIndexRcScript::getCategory2).distinct().collect(Collectors.toList()));
        Map<Long, String> categoryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(categoryIds)) {
            categoryMap.putAll(categoryManager.lambdaQuery().in(CategoryPO::getBizId, categoryIds)
                    .eq(CategoryPO::getIsDeleted, Const.ZERO).list().stream()
                    .collect(Collectors.toMap(CategoryPO::getBizId, CategoryPO::getName)));
        }

        List<ScriptDetailBO> dtos = new ArrayList<>();
        dtos.addAll(data.stream().map(s -> {
            ScriptDetailBO detailBO = new ScriptDetailBO();
            detailBO.setScriptId(s.getScriptId());
            detailBO.setQuestion(s.getQuestion());
            detailBO.setContent(s.getContent());
            detailBO.setCreateBy(s.getCreateBy());
            detailBO.setCreateDt(s.getCreateDt());
            detailBO.setCreatorName(s.getCreatorName());
            detailBO.setCategory1(s.getCategory1());
            detailBO.setCategory2(s.getCategory2());
            detailBO.setCategory1Name(categoryMap.get(s.getCategory1()));
            detailBO.setCategory2Name(categoryMap.get(s.getCategory2()));
            detailBO.setPublishStatus(s.getPublishStatus());
            detailBO.setRemark(s.getRemark());
            List<ScriptMaterialPO> scriptMaterials = cntScriptMaterialMap.get(s.getScriptId());
            if (CollectionUtils.isNotEmpty(scriptMaterials)) {
                detailBO.setMaterials(scriptMaterials.stream().map(m -> {
                    MaterialBO materialDTO = MaterialHelper.convertPO2DTO(cntMaterialMap.get(m.getMaterialId()));
                    return materialDTO;
                }).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            return detailBO;
        }).collect(Collectors.toList()));
        response.setPageIndex(result.getPageNum());
        response.setPageSize(result.getPageSize());
        response.setTotal(result.getTotal());
        response.setDataResult(dtos);
        return response;
    }

    @Override
    public ScriptDetailBO getDetail(Long scriptId) {
        Assert.notNull(scriptId, "话术id为空");
        ScriptPO script = this.lambdaQuery().eq(ScriptPO::getScriptId, scriptId).one();
        Assert.notNull(script, "话术不存在");
        List<ScriptMaterialPO> cntScriptMaterials = scriptMaterialManager.lambdaQuery()
                .eq(ScriptMaterialPO::getScriptId, scriptId)
                .eq(ScriptMaterialPO::getIsDeleted, NumberUtils.INTEGER_ZERO).list();
        // 分类
        List<Long> categoryIds = new ArrayList<>();
        if (!NumberUtils.LONG_ZERO.equals(script.getCategory1())) {
            categoryIds.add(script.getCategory1());
        }
        if (!NumberUtils.LONG_ZERO.equals(script.getCategory2())) {
            categoryIds.add(script.getCategory2());
        }
        Map<Long, String> categoryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(categoryIds)) {
            categoryMap.putAll(categoryManager.lambdaQuery().in(CategoryPO::getBizId, categoryIds).list().stream()
                    .collect(Collectors.toMap(CategoryPO::getBizId, CategoryPO::getName)));
        }

        // 素材详情
        List<MaterialPO> cntMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cntScriptMaterials)) {
            cntMaterials.addAll(materialManager.lambdaQuery().in(MaterialPO::getBizId,
                    cntScriptMaterials.stream().map(ScriptMaterialPO::getMaterialId).distinct()
                            .collect(Collectors.toList())).eq(MaterialPO::getIsDeleted, Const.ZERO).list());
        }
        Map<Long, MaterialPO> cntMaterialMap = cntMaterials.stream()
                .collect(Collectors.toMap(MaterialPO::getBizId, Function.identity()));

        ScriptDetailBO detailBO = new ScriptDetailBO();
        detailBO.setScriptId(script.getScriptId());
        detailBO.setQuestion(script.getQuestion());
        detailBO.setContent(script.getContent());
        detailBO.setCategory1(script.getCategory1());
        detailBO.setCategory2(script.getCategory2());
        detailBO.setCategory1Name(categoryMap.get(script.getCategory1()));
        detailBO.setCategory2Name(categoryMap.get(script.getCategory2()));
        detailBO.setPublishStatus(script.getPublishStatus());
        detailBO.setRemark(script.getRemark());
        if (CollectionUtils.isNotEmpty(cntScriptMaterials)) {
            detailBO.setMaterials(cntScriptMaterials.stream().map(m -> {
                MaterialBO materialDTO = MaterialHelper.convertPO2DTO(cntMaterialMap.get(m.getMaterialId()));
                return materialDTO;
            }).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        return detailBO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editScript(CntScriptBO bo) {
        ScriptPO script = this.lambdaQuery().eq(ScriptPO::getScriptId, bo.getScriptId()).one();
        Assert.notNull(script, "话术不存在");
        ScriptPO duplicatQuestion = this.lambdaQuery().eq(ScriptPO::getQuestion, bo.getQuestion())
                .ne(ScriptPO::getScriptId, bo.getScriptId())

                .one();
        Assert.isNull(duplicatQuestion, "问题重复");

        script.setQuestion(bo.getQuestion());
        script.setCategory1(bo.getCategory1());
        script.setCategory2(bo.getCategory2());
        script.setContent(bo.getContent());
        script.setRemark(bo.getRemark());
        script.setModifyBy(operatorUtil.getOperator());
        script.setModifyName(operatorUtil.getOperatorName());
        this.saveOrUpdate(script);

        List<ScriptMaterialPO> cntScriptMaterials = new ArrayList<>();
        cntScriptMaterials
                .addAll(scriptMaterialManager.lambdaQuery().eq(ScriptMaterialPO::getScriptId, script.getScriptId())
                        .list());
        Map<Long, ScriptMaterialPO> cntScriptMaterialMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(cntScriptMaterials)) {
            cntScriptMaterials
                    .forEach(cntScriptMaterialPO -> cntScriptMaterialPO.setIsDeleted(NumberUtils.INTEGER_ONE));
            cntScriptMaterialMap.putAll(cntScriptMaterials.stream()
                    .collect(Collectors.toMap(ScriptMaterialPO::getMaterialId, Function.identity())));
        }

        if (CollectionUtils.isNotEmpty(bo.getMaterialIds())) {
            bo.getMaterialIds().forEach(mId -> {
                ScriptMaterialPO s = cntScriptMaterialMap.containsKey(mId) ?
                        cntScriptMaterialMap.get(mId) :
                        new ScriptMaterialPO();
                s.setScriptId(script.getScriptId());
                s.setMaterialId(mId);
                s.setIsDeleted(NumberUtils.INTEGER_ZERO);
                cntScriptMaterialMap.put(mId, s);
            });
            scriptMaterialManager.saveOrUpdateBatch(cntScriptMaterialMap.values());
        } else {
            //逻辑删除已有的话术素材
            scriptMaterialManager.update(Wrappers.lambdaUpdate(ScriptMaterialPO.class)
                    .eq(ScriptMaterialPO::getScriptId, bo.getScriptId())
                    .set(ScriptMaterialPO::getIsDeleted, Const.ONE));
        }
    }

    @Override
    public void batchSave(List<CntScriptBatchBO> batch) {
        Assert.notEmpty(batch, "批量上传失败，数据不可为空");
        List<CategoryPO> categories = new ArrayList<>();
        categories.addAll(categoryManager.lambdaQuery().eq(CategoryPO::getIsDeleted, NumberUtils.INTEGER_ZERO).list());
        Map<String, CategoryPO> categoryMap = categories.stream()
                .collect(Collectors.toMap(CategoryPO::getName, Function.identity(), (v1, v2) -> v1));

        this.saveBatch(batch.stream().map(bo -> {
            ScriptPO po = new ScriptPO();
            po.setScriptId(hostTimeIdg.generateId().longValue());
            po.setQuestion(bo.getQuestion());
            po.setContent(bo.getContent());
            if (StringUtils.isNotBlank(bo.getCategory1Name()) && categoryMap.containsKey(bo.getCategory1Name())) {
                po.setCategory1(categoryMap.get(bo.getCategory1Name()).getBizId());
            }
            if (StringUtils.isNotBlank(bo.getCategory2Name()) && categoryMap.containsKey(bo.getCategory2Name())) {
                po.setCategory2(categoryMap.get(bo.getCategory2Name()).getBizId());
            }
            return po;
        }).collect(Collectors.toList()));
    }

    @Override
    public ResponsePageQueryDO<List<ScriptDetailBO>> pageQueryScripts(ScriptSearchBO searchBO) {
        ResponsePageQueryDO<List<ScriptDetailBO>> response = new ResponsePageQueryDO<>();
        LambdaQueryWrapper<ScriptPO> queryWrapper = Wrappers.lambdaQuery(ScriptPO.class);
        queryWrapper.eq(Objects.nonNull(searchBO.getCategory2()), ScriptPO::getCategory2, searchBO.getCategory2())
                .eq(Objects.nonNull(searchBO.getCategory1()), ScriptPO::getCategory1, searchBO.getCategory1())
                .eq(ScriptPO::getIsDeleted, 0)
                .eq(Objects.nonNull(searchBO.getPublishStatus()), ScriptPO::getPublishStatus,
                        searchBO.getPublishStatus())
                .eq(Objects.nonNull(searchBO.getCreateBy()), ScriptPO::getCreateBy, searchBO.getCreateBy())
                .orderByDesc(ScriptPO::getCreateDt);

        IPage<ScriptPO> pageResult = baseMapper.selectPage(convert(searchBO), queryWrapper);
        List<ScriptPO> data = pageResult.getRecords();
        log.info("baseMapper.pageQuery result={}", JsonUtils.toJSON(pageResult));
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return response;
        }

        // 素材
        List<ScriptMaterialPO> cntScriptMaterials = new ArrayList<>();
        cntScriptMaterials.addAll(scriptMaterialManager.lambdaQuery().in(ScriptMaterialPO::getScriptId,
                data.stream().map(ScriptPO::getScriptId).collect(Collectors.toList()))
                .eq(ScriptMaterialPO::getIsDeleted, NumberUtils.INTEGER_ZERO).list());
        Map<Long, List<ScriptMaterialPO>> cntScriptMaterialMap = cntScriptMaterials.stream()
                .collect(Collectors.groupingBy(ScriptMaterialPO::getScriptId));

        // 素材详情
        List<MaterialPO> cntMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cntScriptMaterials)) {
            cntMaterials.addAll(materialManager.lambdaQuery().in(MaterialPO::getBizId,
                    cntScriptMaterials.stream().map(ScriptMaterialPO::getMaterialId).distinct()
                            .collect(Collectors.toList())).eq(MaterialPO::getIsDeleted, Const.ZERO).list());
        }
        Map<Long, MaterialPO> cntMaterialMap = cntMaterials.stream()
                .collect(Collectors.toMap(MaterialPO::getBizId, Function.identity()));

        // 分类
        List<Long> categoryIds = new ArrayList<>();
        categoryIds.addAll(data.stream().map(ScriptPO::getCategory1).distinct().collect(Collectors.toList()));
        categoryIds.addAll(data.stream().map(ScriptPO::getCategory2).distinct().collect(Collectors.toList()));
        Map<Long, String> categoryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(categoryIds)) {
            categoryMap.putAll(categoryManager.lambdaQuery().in(CategoryPO::getBizId, categoryIds)
                    .eq(CategoryPO::getIsDeleted, Const.ZERO).list().stream()
                    .collect(Collectors.toMap(CategoryPO::getBizId, CategoryPO::getName)));
        }

        List<ScriptDetailBO> dtos = new ArrayList<>();
        dtos.addAll(data.stream().map(s -> {
            ScriptDetailBO dto = new ScriptDetailBO();
            dto.setScriptId(s.getScriptId());
            dto.setQuestion(s.getQuestion());
            dto.setContent(s.getContent());
            dto.setCreateBy(s.getCreateBy());
            dto.setCreateDt(s.getCreateDt());
            dto.setCreatorName(s.getCreatorName());
            dto.setCategory1(s.getCategory1());
            dto.setCategory2(s.getCategory2());
            dto.setCategory1Name(categoryMap.get(s.getCategory1()));
            dto.setCategory2Name(categoryMap.get(s.getCategory2()));
            dto.setPublishStatus(s.getPublishStatus());
            dto.setRemark(s.getRemark());
            List<ScriptMaterialPO> scriptMaterials = cntScriptMaterialMap.get(s.getScriptId());
            if (CollectionUtils.isNotEmpty(scriptMaterials)) {
                dto.setMaterials(scriptMaterials.stream().map(m -> {
                    MaterialBO materialDTO = MaterialHelper.convertPO2DTO(cntMaterialMap.get(m.getMaterialId()));
                    return materialDTO;
                }).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            return dto;
        }).collect(Collectors.toList()));
        response.setPageIndex(pageResult.getCurrent());
        response.setPageSize(pageResult.getSize());
        response.setTotal(pageResult.getTotal());
        response.setDataResult(dtos);
        return response;
    }

    @Override
    public void publishOrCancel(Long scriptId) {
        Assert.notNull(scriptId, "话术模板id不存在");
        ScriptPO tpl = baseMapper.selectOne(Wrappers.lambdaQuery(ScriptPO.class).eq(ScriptPO::getScriptId, scriptId)
                .eq(ScriptPO::getIsDeleted, Const.ZERO));
        Assert.notNull(tpl, "话术模板不存在");

        Integer needToBePublistStatus = RcScriptPublishStatusEnum.CANCEL_RELEASED.getStatus()
                .equals(tpl.getPublishStatus()) ?
                RcScriptPublishStatusEnum.RELEASED.getStatus() :
                RcScriptPublishStatusEnum.CANCEL_RELEASED.getStatus();

        //更新es上的发布状态
        scriptSearchManager.publishOrCancel(scriptId,needToBePublistStatus);

        this.update(Wrappers.lambdaUpdate(ScriptPO.class).eq(ScriptPO::getScriptId, scriptId)
                .eq(ScriptPO::getModifyDt, tpl.getModifyDt()).set(ScriptPO::getPublishStatus, needToBePublistStatus)
                .set(ScriptPO::getModifyDt, new Date()).set(ScriptPO::getModifyBy, operatorUtil.getOperator()));
    }
}




