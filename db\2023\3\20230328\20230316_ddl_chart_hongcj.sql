CREATE TABLE `template_dynamic_chart` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `template_id` bigint NOT NULL comment '模板id',
    `name` varchar(50) NOT NULL DEFAULT '' COMMENT '图表名称',
    `type` tinyint unsigned NOT NULL COMMENT '图表类型 1.柱状图 2.折线图 3.饼图',
    `cover_url` varchar(1000) NULL comment '封面图url',
    `preview_video_url` varchar(1000) NULL comment '预览视频合成URl',
    `param_json` text NULL comment '图表参数信息',
    `create_dt` datetime NOT NULL COMMENT '创建时间',
    `modify_dt` datetime NOT NULL COMMENT '更新时间',
    `create_by` bigint NOT NULL COMMENT '创建人',
    `modify_by` bigint NOT NULL COMMENT '更新人',
    `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_template_dynamic_chart_tid` (`template_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '动态图表图';