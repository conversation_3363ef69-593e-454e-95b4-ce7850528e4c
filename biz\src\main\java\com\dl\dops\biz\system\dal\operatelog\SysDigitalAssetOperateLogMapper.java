package com.dl.dops.biz.system.dal.operatelog;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.dl.dops.biz.common.annotation.BaseDao;
import com.dl.dops.biz.system.dal.operatelog.po.SysDigitalAssetOperateLogPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【sys_digital_asset_operate_log(数字资产管理日志记录表)】的数据库操作Mapper
* @createDate 2023-08-30 15:43:09
* @Entity com.dl.dops.biz.system.dal.operatelog.po.SysDigitalAssetOperateLog
*/
@BaseDao
@DS("dops")
public interface SysDigitalAssetOperateLogMapper extends BaseMapper<SysDigitalAssetOperateLogPO> {

}




