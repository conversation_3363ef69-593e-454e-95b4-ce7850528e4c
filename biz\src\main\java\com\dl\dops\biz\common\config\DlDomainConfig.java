package com.dl.dops.biz.common.config;

import com.dl.dops.biz.common.constant.Const;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * DL域名配置
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-06-07 09:08
 */
@Component
public class DlDomainConfig {

    @Value("${dl.local.callback.domain}")
    private String localCallbackDomain;

    @Value("${dl.basicservice.baseUrl}")
    private String basicServiceBaseUrl;

    /**
     * 内网回调使用
     *
     * @return
     */
    public String localCallbackDomain() {
        return removeLastSlash(localCallbackDomain);
    }

    public String getBasicServiceBaseUrl() {
        return basicServiceBaseUrl;
    }

    public String removeLastSlash(String domain) {
        if (StringUtils.isBlank(domain)) {
            return domain;
        }
        if (StringUtils.lastIndexOf(domain, Const.SLASH) == (domain.length() - Const.ONE)) {
            return StringUtils.substring(domain, Const.ZERO, domain.length() - Const.ONE);
        }
        return domain;
    }
}
