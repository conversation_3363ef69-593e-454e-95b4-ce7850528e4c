package com.dl.dops.biz.resourcecenter.dal.pack.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 13:30
 */
@Data
@TableName("pack_element")
public class PackElementPO extends BasePO {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 服务包id
     */
    private Long packId;

    /**
     * 元素id
     */
    private Long elementId;

    /**
     * 链路id
     */
    private Long chainPathId;

    /**
     * 分支id
     */
    private Long branchId;

    /**
     * 元素类型
     *
     * @see：PackElementTypeEnum
     */
    private Integer type;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 附件
     */
    private String attachments;

    /**
     * 扩展数据
     */
    private String extData;

    /**
     * 摘要
     */
    private String remark;

    /**
     * 是否删除，0-否，1-是
     */
    private Integer isDeleted;

}
