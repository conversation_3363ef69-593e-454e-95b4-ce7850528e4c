package com.dl.dops.biz.common.util;

import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-22 14:01
 */
@Component
public class TenantAuthUtil {

    private static final PathMatcher pathMatcher = new AntPathMatcher();

    private static ThreadLocal<String> tenantHodler = new ThreadLocal<>();

    public void init(String tenantCode) {
        tenantHodler.set(tenantCode);
    }

    public String getTenantCode() {
        return tenantHodler.get();
    }

    public void remove() {
        tenantHodler.remove();
    }

    public static PathMatcher getPathMatcher() {
        return pathMatcher;
    }

    public static boolean isFromTenantAuth(HttpServletRequest request) {
        String url = request.getRequestURI();
        return pathMatcher.match("/**/tenantauth/**", url);
    }

    public static boolean isFromB(HttpServletRequest request) {
        String url = request.getRequestURI();
        return pathMatcher.match("/resource/**", url);
    }

}
