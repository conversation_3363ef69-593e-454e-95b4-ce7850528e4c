package com.dl.dops.biz.system.manager.role.dto;

import com.dl.dops.biz.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel("角色id")
@NoArgsConstructor
@AllArgsConstructor
public class RoleIdDTO extends BaseDTO {

    private static final long serialVersionUID = 631895729261655474L;
    private Long roleId;

}
