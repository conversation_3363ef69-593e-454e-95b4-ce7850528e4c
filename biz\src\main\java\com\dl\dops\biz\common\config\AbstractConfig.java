package com.dl.dops.biz.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Configuration
public abstract class AbstractConfig {
    @Autowired
    private Environment env;

    protected boolean isDev() {
        List<String> list = Arrays.stream(env.getActiveProfiles()).collect(Collectors.toList());
        return list.contains("dev") || list.contains("cdev");
    }

}
