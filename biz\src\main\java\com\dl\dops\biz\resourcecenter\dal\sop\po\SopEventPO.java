package com.dl.dops.biz.resourcecenter.dal.sop.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

@Data
@TableName("sop_event")
public class SopEventPO extends BasePO {
    private static final long serialVersionUID = -1465842828804549469L;
    @TableId
    public Long id;

    @TableField("event_id")
    private Long eventId;

    @TableField("sop_id")
    private Long sopId;

    @TableField("content")
    private String content;

    @TableField("reach_type")
    private Integer reachType;

    @TableField("rule_type")
    private Integer ruleType;

    /**
     * 定时推送时为天-HH:mm:ss  周期推送为首次天-天-HH:mm:ss
     */
    @TableField("rule_content")
    private String ruleContent;

    @TableField("is_deleted")
    private Integer isDeleted;

    @TableField("name")
    private String name;

    @TableField("remark")
    private String remark;

    /**
     * 有效期
     */
    @TableField("validity_period")
    private Integer validityPeriod;
}
