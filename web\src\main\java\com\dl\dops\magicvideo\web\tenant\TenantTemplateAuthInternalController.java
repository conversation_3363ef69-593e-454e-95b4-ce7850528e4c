package com.dl.dops.magicvideo.web.tenant;

import com.dl.dops.biz.common.forest.magicvideo.dto.TenantTemplateAuthDTO;
import com.dl.dops.biz.common.forest.magicvideo.param.TenantAuthParam;
import com.dl.dops.biz.common.forest.magicvideo.param.TenantTemplateAuthPageQueryParam;
import com.dl.dops.biz.magicvideo.manager.MagicVideoAuthManager;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/dops/magicvideo/tenanttemplateauth")
@Api("租户模板授权模块")
public class TenantTemplateAuthInternalController {
    @Autowired
    private MagicVideoAuthManager magicVideoAuthManager;

    @PostMapping("/list")
    @ApiOperation("模板列表")
    public ResultPageModel<TenantTemplateAuthDTO> list(@RequestBody @Validated TenantTemplateAuthPageQueryParam param) {
        return magicVideoAuthManager.list(param);
    }

    @PostMapping("/auth")
    @ApiOperation("修改授权")
    public ResultModel<Void> auth(@RequestBody @Validated TenantAuthParam param) {
        return magicVideoAuthManager.auth(param);
    }


}
