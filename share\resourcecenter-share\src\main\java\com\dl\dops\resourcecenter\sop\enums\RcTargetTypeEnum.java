package com.dl.dops.resourcecenter.sop.enums;

/**
 * 目标类型枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-11 09:20
 */
public enum RcTargetTypeEnum {

    CONTACT(1, "客户"),
    CONTACT_ROOM(2, "客户群"),
    EMPLOYEE(3, "员工");

    private Integer type;

    private String desc;

    RcTargetTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
