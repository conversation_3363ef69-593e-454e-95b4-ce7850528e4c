package com.dl.dops.biz.common.forest.magicvideo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-08-20 16:34
 */
@Data
public class StatisticsAiJobTenantSummaryDTO implements Serializable {
    private static final long serialVersionUID = -6903457263981948392L;

    @ApiModelProperty("汇总的毫秒")
    private Long summaryTimeMillis;

    @ApiModelProperty("最新更新时间")
    private Date latestModifyDt;

    /**
     * 汇总的字符数
     */
    private Long summaryTextLength;

}
