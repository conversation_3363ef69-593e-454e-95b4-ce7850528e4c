package com.dl.dops.resourcecenter.sop.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-07 09:32
 */
@Data
@ApiModel("添加Sop事件内容参数")
public class RcSopEventCntAddParam implements Serializable {

    private static final long serialVersionUID = 6298731104310117316L;
    @ApiModelProperty("内容类型 1素材 2海报")
    private Integer contentType;

    @ApiModelProperty("内容id")
    private String contentId;

}
