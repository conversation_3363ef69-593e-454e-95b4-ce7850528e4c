package com.dl.dops.system.web.user.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @describe: SysUserRolesParam
 * @author: zhousx
 * @date: 2022/7/5 14:37
 */
@Data
@ApiModel("用户-角色集合参数")
public class UserRolesParam {

    @ApiModelProperty("用户id")
    @NotBlank
    private String userId;

    @ApiModelProperty("角色id")
    @Size(max = 10)
    private List<Long> roleIds;
}
