package com.dl.dops.biz.resourcecenter.manager.rs.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-10-21 16:37
 */
@Data
public class RsPackChainPathDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 服务包id
     */
    private Long packId;

    /**
     * 链路id
     */
    private Long chainPathId;

    /**
     * 名称
     */
    private String name;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 分支列表
     */
    private List<RsPackBranchDTO> branchList;

}
