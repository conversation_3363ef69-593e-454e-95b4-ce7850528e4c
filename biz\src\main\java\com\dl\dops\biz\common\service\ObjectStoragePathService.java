package com.dl.dops.biz.common.service;

import com.dl.dops.biz.common.util.UrlUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import javax.validation.constraints.NotNull;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 对象存储路径处理服务
 */
public interface ObjectStoragePathService {

    /**
     * 对象存储资源路径前缀
     *
     * @return 资源前缀
     */
    String getObjectStorageResourcePrefix();

    /**
     * 从潜在的URL里提取路径部分，如果不是合法的URL，则返回输入的值
     *
     * @param possibleUrl 潜在的URL
     * @return 提取结果
     */
    default String getPathFromPossibleUrl(@NotNull String possibleUrl) {
        Assert.notNull(possibleUrl, "URL不应为null");
        String pathFromUrlString = UrlUtils.getPathFromUrlString(possibleUrl);
        return pathFromUrlString == null ? possibleUrl : pathFromUrlString;
    }

    /**
     * 预处理参数中的对象存储URL属性，提取路径并替换原值
     *
     * @param obj    参数对象
     * @param getter 提取方法
     * @param setter 设置方法
     * @param <T>    参数类型
     */
    default <T> void preProcessObjectStorageUrl(T obj, Function<T, String> getter, BiConsumer<T, String> setter) {
        String original = getter.apply(obj);
        if (StringUtils.isNotEmpty(original)) {
            setter.accept(obj, getPathFromPossibleUrl(original));
        }
    }

    /**
     * 预处理参数中的对象存储URL属性，提取路径并替换原值
     *
     * @param getter 提取方法
     * @param setter 设置方法
     */
    default void preProcessObjectStorageUrl(Supplier<String> getter, Consumer<String> setter) {
        String original = getter.get();
        if (StringUtils.isNotEmpty(original)) {
            setter.accept(getPathFromPossibleUrl(original));
        }
    }

    /**
     * 拼接对外返回的对象存储路径
     *
     * @param path 原始路径
     * @return 拼接结果
     */
    default String compositeObjectStoragePath(String path) {
        return getObjectStorageResourcePrefix() + path;
    }

    /**
     * 从给定对象和方法提取，并拼接对外返回的对象存储路径，覆盖对应属性
     *
     * @param obj    对象实例
     * @param getter 提取方法
     * @param setter 设置方法
     * @param <T>    参数类型
     */
    default <T> void compositeObjectStoragePath(T obj, Function<T, String> getter, BiConsumer<T, String> setter) {
        String original = getter.apply(obj);
        if (StringUtils.isNotEmpty(original)) {
            setter.accept(obj, getObjectStorageResourcePrefix() + original);
        }
    }

    /**
     * 从给定对象和方法提取，并拼接对外返回的对象存储路径，覆盖对应属性
     *
     * @param getter 提取方法
     * @param setter 设置方法
     */
    default void compositeObjectStoragePath(Supplier<String> getter, Consumer<String> setter) {
        String original = getter.get();
        if (StringUtils.isEmpty(original)) {
            return;
        }
        //如果已经由http打头,则不处理
        if (StringUtils.startsWith(original, "http")) {
            return;
        }

        setter.accept(getObjectStorageResourcePrefix() + original);
    }
}
