package com.dl.dops.resourcecenter.web.controllers.sop.convert;

import com.dl.dops.biz.common.enums.SymbolE;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopAddBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventCntBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopModifyBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopSearchBO;
import com.dl.dops.biz.resourcecenter.manager.sop.consts.SopConst;
import com.dl.dops.resourcecenter.sop.dto.RcSopDTO;
import com.dl.dops.resourcecenter.sop.dto.RcSopEventCntDTO;
import com.dl.dops.resourcecenter.sop.dto.RcSopEventDTO;
import com.dl.dops.resourcecenter.sop.dto.RcSopStatusDTO;
import com.dl.dops.resourcecenter.sop.enums.RcReachTypeEnum;
import com.dl.dops.resourcecenter.sop.enums.RcSopStatusEnum;
import com.dl.dops.resourcecenter.sop.param.RcSopPageQueryParam;
import com.dl.dops.resourcecenter.sop.param.RcSopSaveParam;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-09 20:03
 */
public class SopConvert {

    public static RcSopDTO fillSopDTO(SopBO input, RcSopDTO result, Map<Long, String> categoryNameMap) {
        result.setSopId(String.valueOf(input.getSopId()));
        result.setName(input.getName());
        result.setSopType(input.getSopType());
        result.setStatus(input.getStatus());
        result.setCreateTime(input.getCreateDt());
        result.setRemark(input.getRemark());
        result.setCreatorName(input.getCreatorName());
        if (Objects.nonNull(input.getCategory1())) {
            result.setCategory1(String.valueOf(input.getCategory1()));
            result.setCategory1Name(categoryNameMap.get(input.getCategory1()));
        }
        if (Objects.nonNull(input.getCategory2())) {
            result.setCategory2(String.valueOf(input.getCategory2()));
            result.setCategory2Name(categoryNameMap.get(input.getCategory2()));
        }

        if (StringUtils.isNotBlank(input.getRemark())) {
            if (input.getRemark().contains(SopConst.SOP_TPL_REMARK_TARGET_CONF_SPLIT)) {
                String[] arr = input.getRemark().split(SopConst.SOP_TPL_REMARK_TARGET_CONF_SPLIT);
                result.setRemark(arr[0]);
                result.setTargetConfDesc(arr[1]);
            } else {
                result.setRemark(input.getRemark());
            }
        }
        return result;
    }

    public static RcSopDTO cnvSopDTO2TplVO(SopBO input, Map<Long, String> categoryNameMap) {
        RcSopDTO result = new RcSopDTO();
        fillSopDTO(input,result,categoryNameMap);
        return result;
    }

    public static SopAddBO cnvSopSaveParam2BO(RcSopSaveParam input) {
        SopAddBO result = new SopAddBO();
        if (StringUtils.isNumeric(input.getCategory1())) {
            result.setCategory1(Long.valueOf(input.getCategory1()));
        }
        if (StringUtils.isNumeric(input.getCategory2())) {
            result.setCategory2(Long.valueOf(input.getCategory2()));
        }

        result.setName(input.getName());
        result.setSopType(input.getSopType());
        result.setSopType(input.getSopType());

        StringBuffer sbf = new StringBuffer();
        sbf.append(Objects.nonNull(input.getRemark()) ? input.getRemark() : SymbolE.BLANK.getValue())
                .append(SopConst.SOP_TPL_REMARK_TARGET_CONF_SPLIT).append(input.getTargetConfDesc());
        result.setRemark(sbf.toString());
        return result;
    }

    public static SopSearchBO cnvSopPageQueryParam2SearchBO(RcSopPageQueryParam input) {
        SopSearchBO result = new SopSearchBO();
        result.setName(input.getName());
        result.setSopType(input.getSopType());
        result.setStatus(input.getStatus());
        result.setPageIndex(input.getPageIndex());
        result.setPageSize(input.getPageSize());
        return result;
    }

    public static SopModifyBO cnvSopTplSaveParam2ModifyBO(RcSopSaveParam input) {
        SopModifyBO result = new SopModifyBO();
        result.setSopId(Long.valueOf(input.getSopId()));

        if (StringUtils.isNumeric(input.getCategory1())) {
            result.setCategory1(Long.valueOf(input.getCategory1()));
        }
        if (StringUtils.isNumeric(input.getCategory2())) {
            result.setCategory2(Long.valueOf(input.getCategory2()));
        }
        result.setName(input.getName());
        result.setSopType(input.getSopType());
        StringBuffer sbf = new StringBuffer();
        sbf.append(Objects.nonNull(input.getRemark()) ? input.getRemark() : SymbolE.BLANK.getValue())
                .append(SopConst.SOP_TPL_REMARK_TARGET_CONF_SPLIT).append(input.getTargetConfDesc());
        result.setRemark(sbf.toString());
        return result;
    }

    public static RcSopEventDTO fillSopEventVO(SopEventBO input, RcSopEventDTO result) {
        result.setEventId(String.valueOf(input.getEventId()));
        result.setContent(input.getContent());
        result.setSopId(String.valueOf(input.getSopId()));
        result.setRuleContent(input.getRuleContent());
        result.setRuleType(input.getRuleType());
        result.setReachWay(input.getReachType());
        result.setReachWayName(RcReachTypeEnum.getNameByType(input.getReachType()));
        result.setRemark(input.getRemark());
        result.setName(input.getName());
        result.setValidityPeriod(input.getValidityPeriod());
        return result;
    }

    public static RcSopEventCntDTO cnvSopEventCntDTO2VO(SopEventCntBO input) {
        if (Objects.isNull(input)) {
            return null;
        }
        RcSopEventCntDTO result = new RcSopEventCntDTO();
        result.setContentId(String.valueOf(input.getCntId()));
        result.setContentType(input.getCntType());
        return result;
    }

    public static RcSopStatusDTO cnvSopStatusEnum2VO(RcSopStatusEnum input) {
        if (Objects.isNull(input)) {
            return null;
        }
        RcSopStatusDTO result = new RcSopStatusDTO();
        result.setCode(input.getCode());
        result.setDesc(input.getDesc());
        return result;
    }

}
