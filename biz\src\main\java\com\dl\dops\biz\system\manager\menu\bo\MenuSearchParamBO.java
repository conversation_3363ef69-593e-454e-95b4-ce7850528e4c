package com.dl.dops.biz.system.manager.menu.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("菜单搜索")
public class MenuSearchParamBO {

    @ApiModelProperty("父级菜单ID")
    @Size(min = 0)
    private Long parentId;

    @ApiModelProperty(hidden = true)
    private Set<Long> roleIds;

}
