package com.dl.dops.biz.resourcecenter.manager.material.helper;

import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialBO;
import com.dl.dops.biz.resourcecenter.dal.material.po.MaterialPO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-22 16:17
 */
public class MaterialHelper {

    public static MaterialBO convertPO2DTO(MaterialPO po) {
        if (Objects.isNull(po)) {
            return null;
        }
        MaterialBO dto = new MaterialBO();
        dto.setMaterialId(po.getBizId() + "");
        dto.setMaterialType(po.getMaterialType());
        dto.setContent(po.getContent());
        dto.setLogoImg(po.getLogoImg());
        dto.setRemark(po.getRemark());

        dto.setCategoryIds(po.getCategoryIds());
        dto.setCreateBy(po.getCreateBy());
        dto.setModifyBy(po.getModifyBy());
        dto.setArticleType(po.getArticleType());
        dto.setMpArticleSourceUrl(po.getSourceUrl());
        dto.setCreator(po.getCreatorName());
        dto.setCreateTime(po.getCreateDt());
        dto.setSize(po.getSize());
        dto.setTitle(po.getTitle());
        dto.setPublishStatus(po.getPublishStatus());
        return dto;
    }

}
