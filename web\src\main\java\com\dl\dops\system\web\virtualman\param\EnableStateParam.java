package com.dl.dops.system.web.virtualman.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EnableStateParam {

    @NotNull(message = "启用状态必填")
    @Max(value = 1, message = "启用状态只能是0或1")
    @ApiModelProperty(value = "启用状态： 0 否 ；1 启用", required = true)
    Integer enableState;

    @NotNull(message = "数字人唯一标识必填")
    @ApiModelProperty(value = "数字人唯一标识bizId", required = true)
    Long vmBizId;
}
