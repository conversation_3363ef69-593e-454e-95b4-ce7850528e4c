package com.dl.dops.biz.system.manager.role.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.system.dal.role.RoleFunctionMapper;
import com.dl.dops.biz.system.dal.role.po.RoleFunctionPO;
import com.dl.dops.biz.system.manager.role.RoleFunctionManager;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-06 17:30
 */
@DS("dops")
@Component
public class RoleFunctionManagerImpl extends ServiceImpl<RoleFunctionMapper, RoleFunctionPO>
        implements RoleFunctionManager {
}
