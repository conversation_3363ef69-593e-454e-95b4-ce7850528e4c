package com.dl.dops.biz.resourcecenter.manager.rs.dto;

import com.dl.dops.resourcecenter.pack.enums.RcPackSceneEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-10-21 16:23
 */
@Data
public class RsPackTplDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务包id
     */
    private Long packId;

    /**
     * 标题
     */
    private String title;

    /**
     * 所属一级分类id
     */
    private Long category1;

    /**
     * 所属二级分类id
     */
    private Long category2;
    /**
     * 场景概述
     */
    private String sceneOverview;

    /**
     * 适用行业
     */
    private Integer domain;

    /**
     * 详细描述
     */
    private String detailedDescription;

    /**
     * 运营投放建议
     */
    private String suggest;

    /**
     * 场景 1-助你营，2-助你拍
     *
     * @see RcPackSceneEnum
     */
    private Integer scene;

    /**
     * 创建时间
     */
    private Date createDt;

    /**
     * 修改时间
     */
    private Date modifyDt;

    /**
     * 链路列表
     */
    private List<RsPackChainPathDTO> chainPathList;
}
