package com.dl.dops.biz.resourcecenter.manager.material.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.resourcecenter.dal.material.MaterialMapper;
import com.dl.dops.biz.resourcecenter.dal.material.po.MaterialPO;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagPO;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagRelaPO;
import com.dl.dops.biz.resourcecenter.manager.category.enums.DefaultCategoryEnum;
import com.dl.dops.biz.resourcecenter.manager.material.MaterialManager;
import com.dl.dops.biz.resourcecenter.manager.material.MaterialSearchManager;
import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialBO;
import com.dl.dops.biz.resourcecenter.manager.material.helper.MaterialHelper;
import com.dl.dops.biz.resourcecenter.manager.tag.TagManager;
import com.dl.dops.biz.resourcecenter.manager.tag.TagRelaManager;
import com.dl.dops.resourcecenter.material.enums.RcMaterialPublishStatusEnum;
import com.dl.dops.resourcecenter.material.enums.RcMaterialTypeEnum;
import com.dl.dops.resourcecenter.material.param.RcMaterialAddParam;
import com.dl.dops.resourcecenter.material.param.RcMaterialDeleteParam;
import com.dl.dops.resourcecenter.material.param.RcMaterialPageQueryParam;
import com.dl.dops.resourcecenter.material.param.RcMaterialUpdateParam;
import com.dl.dops.resourcecenter.material.param.RcUpdateMaterialCategoryParam;
import com.dl.dops.resourcecenter.tag.TagDefaultEnum;
import com.dl.dops.resourcecenter.tag.dto.TagInfoDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class MaterialManagerImpl extends ServiceImpl<MaterialMapper, MaterialPO> implements MaterialManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialManagerImpl.class);
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private OperatorUtil operatorUtil;

    @Autowired
    private MaterialSearchManager materialSearchManager;

    @Autowired
    private TagRelaManager tagRelaManager;

    @Autowired
    private TagManager tagManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(RcMaterialAddParam param, Integer publishMidState) {
        if (StringUtils.isBlank(param.getCategoryId())) {
            param.setCategoryId(DefaultCategoryEnum.UNCLASSIFIED.getCode());
        }
        MaterialPO po = new MaterialPO();
        po.setContent(param.getContent());
        Long materialId = StringUtils.isBlank(param.getMaterialId()) ?
                hostTimeIdg.generateId().longValue() :
                Long.valueOf(param.getMaterialId());
        po.setBizId(materialId);
        po.setCategoryIds(param.getCategoryId());
        po.setLogoImg(param.getLogoImg());
        po.setRemark(param.getRemark());
        po.setTitle(param.getTitle());
        po.setArticleType(param.getArticleType());
        po.setMaterialType(param.getMaterialType());
        po.setSourceUrl(param.getMpArticleSourceUrl());
        po.setSize(param.getSize());
        po.setCreateBy(operatorUtil.getOperator());
        po.setModifyBy(operatorUtil.getOperator());
        po.setCreatorName(operatorUtil.getOperatorName());
        po.setModifyName(operatorUtil.getOperatorName());
        if (Objects.nonNull(publishMidState)) {
            po.setPublishStatus(publishMidState);
        } else {
            po.setPublishStatus(param.getPublishStatus());
        }
        po.setCreateFrom(param.getCreateFrom());
        //入库
        this.save(po);
        //处理标签
        if(CollectionUtils.isNotEmpty(param.getTagIds())){
            List<TagRelaPO> tagRelaPOList = new ArrayList<>();
            param.getTagIds().forEach(item->{
                TagRelaPO tagRelaPO = new TagRelaPO();
                tagRelaPO.setTagId(Long.valueOf(item));
                tagRelaPO.setType(param.getTagGroupType());
                tagRelaPO.setBizId(po.getBizId());
                tagRelaPO.setIsDelete(Const.ZERO);
                tagRelaPOList.add(tagRelaPO);
            });
            tagRelaManager.saveBatch(tagRelaPOList);
        }
        return po.getBizId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(RcMaterialUpdateParam param, Integer publishMidState) {
        Assert.isTrue(StringUtils.isNumeric(param.getMaterialId()), "错误的素材id");
        Boolean resp = true;
        Long bizId = Long.valueOf(param.getMaterialId());
        // 身份校验
        LambdaQueryWrapper<MaterialPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MaterialPO::getBizId, bizId);
        MaterialPO exist = getOne(queryWrapper);
        Assert.notNull(exist, "素材不存在");
        // 更新
        LambdaUpdateWrapper<MaterialPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(MaterialPO::getBizId, bizId)
                .set(Objects.nonNull(param.getContent()), MaterialPO::getContent, param.getContent())
                .set(StringUtils.isNotBlank(param.getCategoryId()), MaterialPO::getCategoryIds, param.getCategoryId())
                .set(StringUtils.isNotBlank(param.getTitle()), MaterialPO::getTitle, param.getTitle())
                .set(StringUtils.isNotBlank(param.getLogoImg()), MaterialPO::getLogoImg, param.getLogoImg())
                .set(Objects.nonNull(param.getRemark()), MaterialPO::getRemark, param.getRemark())
                .set(Objects.nonNull(publishMidState), MaterialPO::getPublishStatus, publishMidState)
                .set(Objects.nonNull(param.getSize()), MaterialPO::getSize, param.getSize())
                .set(StringUtils.isNotBlank(param.getMpArticleSourceUrl()), MaterialPO::getSourceUrl,
                        param.getMpArticleSourceUrl())
                .set(Objects.nonNull(operatorUtil.getOperator()), MaterialPO::getModifyBy, operatorUtil.getOperator())
                .set(StringUtils.isNotBlank(operatorUtil.getOperatorName()), MaterialPO::getModifyName,
                        operatorUtil.getOperatorName());
        this.update(updateWrapper);

        //处理标签关联关系
        LambdaQueryWrapper<TagRelaPO> relaQueryWrapper = new LambdaQueryWrapper<>();
        relaQueryWrapper.eq(TagRelaPO::getBizId, bizId).eq(TagRelaPO::getIsDelete, Const.ZERO);
        List<TagRelaPO> oldRelaPOS = tagRelaManager.list(relaQueryWrapper);
        List<Long> oldTagIds = oldRelaPOS.stream().map(item -> item.getTagId()).collect(Collectors.toList());
        //需要新增的关联关系
        List<TagRelaPO> newTagRelaPOList = new ArrayList<>();
        //需要删除的关联关系
        List<Long> needDelRelaIdList = new ArrayList<>();
        List<Long> requestTagIds = param.getTagIds().stream().map(item -> Long.valueOf(item)).collect(Collectors.toList());

        oldTagIds.forEach(item->{
            if (!requestTagIds.contains(item)){
                needDelRelaIdList.add(item);
            }
        });
        requestTagIds.forEach(item->{
            if (!oldTagIds.contains(item)){
                TagRelaPO tagRelaPO = new TagRelaPO();
                tagRelaPO.setBizId(Long.valueOf(param.getMaterialId()));
                tagRelaPO.setTagId(item);
                tagRelaPO.setType(param.getTagGroupType());
                tagRelaPO.setIsDelete(Const.ZERO);
                newTagRelaPOList.add(tagRelaPO);
            }
        });
        if (CollectionUtils.isNotEmpty(needDelRelaIdList)){
            LambdaQueryWrapper<TagRelaPO> delQueryWrapper = new LambdaQueryWrapper<>();
            delQueryWrapper.eq(TagRelaPO::getBizId,Long.valueOf(param.getMaterialId()))
                    .in(TagRelaPO::getTagId,oldTagIds);
            List<TagRelaPO> delTagRelaS = tagRelaManager.list(delQueryWrapper);
            delTagRelaS.forEach(item->{
                item.setIsDelete(Const.ONE);
            });
            //解除关联
            resp = tagRelaManager.updateBatchById(delTagRelaS);
        }
       if (CollectionUtils.isNotEmpty(newTagRelaPOList)){
           //新增关联
           resp = tagRelaManager.saveBatch(newTagRelaPOList);
       }
       return resp;
    }

    @Override
    public MaterialBO detail(String materialId) {
        Assert.isTrue(StringUtils.isNumeric(materialId), "错误的素材id");
        Long bizId = Long.valueOf(materialId);
        // 身份校验
        LambdaQueryWrapper<MaterialPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MaterialPO::getIsDeleted, Const.ZERO);
        queryWrapper.eq(MaterialPO::getBizId, bizId);
        MaterialPO exist = getOne(queryWrapper);
        Assert.notNull(exist, "素材不存在");

        MaterialBO materialBO = MaterialHelper.convertPO2DTO(exist);

        //构造素材的标签信息
        LambdaQueryWrapper<TagRelaPO> relaQueryWrapper = new LambdaQueryWrapper<>();
        relaQueryWrapper.eq(TagRelaPO::getBizId,Long.valueOf(materialId))
                .eq(TagRelaPO::getIsDelete,Const.ZERO);
        List<TagRelaPO> tagRelaPOList = tagRelaManager.list(relaQueryWrapper);
        if(CollectionUtils.isEmpty(tagRelaPOList)){
            return materialBO;
        }

        LambdaQueryWrapper<TagPO> tagPOQueryWrapper = new LambdaQueryWrapper<>();
        tagPOQueryWrapper.in(TagPO::getTagId,tagRelaPOList.stream().map(TagRelaPO::getTagId).collect(Collectors.toList()))
                .eq(TagPO::getIsDelete,Const.ZERO);
        List<TagPO> tagPOList = tagManager.list(tagPOQueryWrapper);
        List<TagInfoDTO> tagInfoDTOS = tagPOList.stream().map(item -> {
            TagInfoDTO tagInfoDTO = new TagInfoDTO();
            tagInfoDTO.setTagId(item.getTagId().toString());
            tagInfoDTO.setTagName(item.getName());
            tagInfoDTO.setOrder(item.getOrdered());
            tagInfoDTO.setTagGroupId(item.getTagGroupId().toString());
            return tagInfoDTO;
        }).collect(Collectors.toList());
        materialBO.setTagInfoDTOList(tagInfoDTOS);
        return materialBO;
    }

    @Override
    public IPage<MaterialBO> pageQuery(RcMaterialPageQueryParam bo) {
        RcMaterialTypeEnum rcMaterialTypeEnum = RcMaterialTypeEnum.parse(bo.getMaterialType());
        Assert.notNull(rcMaterialTypeEnum, "请选择素材类型");
        if (StringUtils.isBlank(bo.getCategoryId())) {
            bo.setCategoryId(DefaultCategoryEnum.ALL.getCode());
        }
        //如果根据标签查询素材，提取标签关联的素材id
        List<Long> bizIds = new ArrayList<>();
        if (StringUtils.isNotBlank(bo.getTagId()) && !TagDefaultEnum.ALL.getCode().equals(bo.getTagId())) {
            LambdaQueryWrapper<TagRelaPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TagRelaPO::getTagId, Long.valueOf(bo.getTagId())).eq(TagRelaPO::getIsDelete, Const.ZERO)
                    .orderByDesc(TagRelaPO::getId);
            List<TagRelaPO> tagRelaPOList = tagRelaManager.list(queryWrapper);
            if (CollectionUtils.isEmpty(tagRelaPOList)) {
                return new Page<MaterialBO>(bo.getPageIndex(), bo.getPageSize());
            }

            bizIds.addAll(tagRelaPOList.stream().map(TagRelaPO::getBizId).collect(Collectors.toList()));
        }
        LambdaQueryWrapper<MaterialPO> wrapper = Wrappers.lambdaQuery();
        if (RcMaterialTypeEnum.ARTICLE.getCode().equals(bo.getMaterialType())) {
            wrapper.select(MaterialPO.class, f -> !f.getColumn().equals("content"));
        }
        if (CollectionUtils.isNotEmpty(bizIds)){
            wrapper.in(MaterialPO::getBizId,bizIds);
        }

        // 分类
        if (StringUtils.equals(bo.getCategoryId(), DefaultCategoryEnum.UNCLASSIFIED.getCode())) {
            wrapper.eq(MaterialPO::getCategoryIds, bo.getCategoryId());
        } else {
            wrapper.like(!StringUtils.equals(bo.getCategoryId(), DefaultCategoryEnum.ALL.getCode()),
                    MaterialPO::getCategoryIds, bo.getCategoryId());
        }

        // 标题左匹配
        wrapper.like(StringUtils.isNotBlank(bo.getTitle()), MaterialPO::getTitle, bo.getTitle());
        // 素材类型
        wrapper.eq(Objects.nonNull(bo.getMaterialType()), MaterialPO::getMaterialType, bo.getMaterialType());

        // 未删除
        wrapper.eq(MaterialPO::getIsDeleted, Const.ZERO);
        wrapper.eq(Objects.nonNull(bo.getPublishStatus()), MaterialPO::getPublishStatus, bo.getPublishStatus());
        // 排序
        wrapper.orderByDesc(MaterialPO::getId);
        IPage<MaterialPO> page = baseMapper.selectPage(convert(bo), wrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page.convert(MaterialHelper::convertPO2DTO);
        }
        return page.convert(MaterialHelper::convertPO2DTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDelete(RcMaterialDeleteParam bo) {
        Assert.notEmpty(bo.getMaterialIds(), "素材id不能为空");
        List<Long> bizIds = bo.getMaterialIds().stream().map(Long::parseLong).collect(Collectors.toList());

        //查询发布状态的素材，只有取消发布状态的素材才可以删除
        List<MaterialPO> existReleasedMaterialList = baseMapper.selectList(
                Wrappers.lambdaQuery(MaterialPO.class).in(MaterialPO::getBizId, bizIds)
                        .eq(MaterialPO::getPublishStatus, RcMaterialPublishStatusEnum.RELEASED.getStatus()));
        Assert.isTrue(CollectionUtils.isEmpty(existReleasedMaterialList), "存在已发布的素材，请先将状态改为取消发布再进行删除操作");

        //逻辑删除es素材
        materialSearchManager.batchDeleteEs(bo.getMaterialIds());
        LambdaUpdateWrapper<MaterialPO> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(MaterialPO::getBizId, bizIds).set(MaterialPO::getIsDeleted, Const.ONE);

        LambdaUpdateWrapper<TagRelaPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(TagRelaPO::getBizId,bizIds)
                .eq(TagRelaPO::getIsDelete,Const.ZERO)
                .set(TagRelaPO::getIsDelete,Const.ONE);
        //解除素材标签关联
        tagRelaManager.update(updateWrapper);
        return update(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateCategory(RcUpdateMaterialCategoryParam bo) {
        Assert.notEmpty(bo.getMaterialIds(), "素材id不能为空");
        Assert.isTrue(StringUtils.isNumeric(bo.getCategoryId()), "错误的分组id");
        List<Long> bizIds = bo.getMaterialIds().stream().map(Long::parseLong).collect(Collectors.toList());
        LambdaUpdateWrapper<MaterialPO> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(MaterialPO::getBizId, bizIds).set(MaterialPO::getCategoryIds, bo.getCategoryId());
        return update(wrapper);
    }

    @Override
    public List<MaterialBO> batchDetail(List<Long> materialIds) {
        Assert.notEmpty(materialIds, "素材id不可为空");

        List<MaterialPO> materials = this.lambdaQuery().in(MaterialPO::getBizId, materialIds).list();
        Assert.notEmpty(materials, "素材不存在");

        return materials.stream().map(po -> MaterialHelper.convertPO2DTO(po)).collect(Collectors.toList());
    }

    @Override
    public Boolean publishOrCancel(Long materialId) {
        Assert.notNull(materialId, "素材模板id不存在");
        MaterialPO tpl = baseMapper.selectOne(
                Wrappers.lambdaQuery(MaterialPO.class).eq(MaterialPO::getBizId, materialId)
                        .eq(MaterialPO::getIsDeleted, Const.ZERO));
        Assert.notNull(tpl, "素材模板不存在");

        Integer needToBePublistStatus = RcMaterialPublishStatusEnum.CANCEL_RELEASED.getStatus()
                .equals(tpl.getPublishStatus()) ?
                RcMaterialPublishStatusEnum.RELEASED.getStatus() :
                RcMaterialPublishStatusEnum.CANCEL_RELEASED.getStatus();
        //更新es上的发布状态
        materialSearchManager.publishOrCancel(String.valueOf(materialId),needToBePublistStatus);

        return this.update(Wrappers.lambdaUpdate(MaterialPO.class).eq(MaterialPO::getBizId, materialId)
                .eq(MaterialPO::getModifyDt, tpl.getModifyDt()).set(MaterialPO::getPublishStatus, needToBePublistStatus)
                .set(MaterialPO::getModifyDt, new Date()).set(MaterialPO::getModifyBy, operatorUtil.getOperator()));
    }

}
