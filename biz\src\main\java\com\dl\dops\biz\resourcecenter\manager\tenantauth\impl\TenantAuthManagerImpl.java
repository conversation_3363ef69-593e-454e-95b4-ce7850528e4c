package com.dl.dops.biz.resourcecenter.manager.tenantauth.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.common.util.DateUtil;
import com.dl.dops.biz.resourcecenter.dal.tenant.TenantAuthMapper;
import com.dl.dops.biz.resourcecenter.dal.tenant.po.TenantAuthPO;
import com.dl.dops.biz.resourcecenter.manager.tenantauth.TenantAuthManager;
import com.dl.dops.biz.resourcecenter.manager.tenantauth.consts.KeyConst;
import com.dl.dops.biz.resourcecenter.manager.tenantauth.enums.TenantAuthStatusEnum;
import com.dl.dops.resourcecenter.tenantauth.dto.RcTenantAuthTokenDTO;
import com.dl.dops.resourcecenter.tenantauth.util.RsaUtil;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.framework.core.interceptor.expdto.CertificateException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Date;

@Service
public class TenantAuthManagerImpl extends ServiceImpl<TenantAuthMapper, TenantAuthPO> implements TenantAuthManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantAuthManagerImpl.class);

    @Override
    public RcTenantAuthTokenDTO encrptAndValidToken(String token, String tenantCode) throws CertificateException {
        Assert.isTrue(StringUtils.isNotBlank(token), "token不能为空");
        Assert.isTrue(StringUtils.isNotBlank(tenantCode), "租户号不能为空");

        TenantAuthPO tenantAuthPO = baseMapper
                .selectOne(Wrappers.lambdaQuery(TenantAuthPO.class).eq(TenantAuthPO::getTenantCode, tenantCode));
        Assert.notNull(tenantAuthPO, "该租户未授权");
        Assert.isTrue(TenantAuthStatusEnum.ENABLE.getCode().equals(tenantAuthPO.getStatus()), "该租户已无法访问资源中心");
        String decrptStr = "";
        try {
            decrptStr = RsaUtil.decryptByPrivateKey(KeyConst.RC_PRIVATE_KEY, token);
        } catch (Exception e) {
            LOGGER.error("资源中心token解密失败，tenantCode:{},token:{}", tenantCode, token);
            throw BusinessServiceException.getInstance("资源中心token解密失败");
        }
        RcTenantAuthTokenDTO tokenDTO = JSONUtil.toBean(decrptStr, RcTenantAuthTokenDTO.class);
        if (!StringUtils.equalsIgnoreCase(tenantCode, tokenDTO.getTenantCode())) {
            throw BusinessServiceException.getInstance("token错误");
        }
        //判断token是否是今日生成的
        if (!DateUtil.format(new Date(), DateUtil.Y_M_D)
                .equals(DateUtil.format(tokenDTO.getTokenCreateDt(), DateUtil.Y_M_D))) {
            throw new CertificateException("token已失效，请重新登录");
        }

        return tokenDTO;
    }
}
