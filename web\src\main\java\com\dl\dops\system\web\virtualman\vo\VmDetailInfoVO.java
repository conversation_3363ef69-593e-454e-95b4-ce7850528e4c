package com.dl.dops.system.web.virtualman.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Date;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class VmDetailInfoVO {

    @ApiModelProperty("数字人名唯一标识bizId")
    String vmBizId;

    @ApiModelProperty("数字人名称")
    String name;

    @ApiModelProperty("数字人头像地址")
    String headImg;

    @ApiModelProperty("数字人来源渠道")
    Integer channel;

    @ApiModelProperty("数字人来源渠道名称")
    String channelStr;

    @ApiModelProperty("数字人来源编码")
    String vmCode;

    @ApiModelProperty("数字人性别：1 男；2 女")
    Integer gender;

    @ApiModelProperty("数字人来源有效期")
    Date expireDate;

    @ApiModelProperty("数字人启用状态： 0 否 ；1 启用")
    Integer enableState;

    @ApiModelProperty("数字人授权租户名")
    List<String> tenantList;

    @ApiModelProperty("数字人场景名")
    List<String> vmSceneNameList;

    @ApiModelProperty("数字人是否支持语速设置：0 否；1 是")
    Integer enableSpeed;

    @ApiModelProperty("数字人默认语速值")
    Float defaultSpeed;

    @ApiModelProperty(value = "仿真人外部声音代码")
    String vmVoiceKey;

    @ApiModelProperty(value = "关联合克隆音的内部声音代码")
    String voiceBizId;
}
