package com.dl.dops.biz.resourcecenter.manager.material;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialBO;
import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialSearchBO;

import java.util.List;

/**
 * @ClassName MaterialSearchManager
 * @Description
 * <AUTHOR>
 * @Date 2022/6/30 16:00
 * @Version 1.0
 **/
public interface MaterialSearchManager {

    /**
     * 分页搜索素材
     *
     * @param searchParamBO
     * @return
     */
    IPage<MaterialBO> search(MaterialSearchBO searchParamBO);

    /**
     * 批量逻辑删除es素材
     * @param bizIds
     */
    void batchDeleteEs(List<String> bizIds);

    /**
     * 更改素材发布状态
     * @param bizId
     * @param needToBePublistStatus
     */
    void publishOrCancel(String bizId ,Integer needToBePublistStatus);
}
