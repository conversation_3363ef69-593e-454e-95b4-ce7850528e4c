package com.dl.dops.resourcecenter.sop.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-05 14:16
 */
@ApiModel("查询sop列表")
@Data
public class RcSopPageQueryParam extends AbstractPageParam implements Serializable {
    private static final long serialVersionUID = 4976398284876450950L;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("sop类型,1个人sop 2群sop")
    private Integer sopType;

    @ApiModelProperty("所属一级分类id")
    private String category1;

    @ApiModelProperty("所属二级分类id")
    private String category2;

}
