package com.dl.dops.web.common.controllers.tenant;

import com.dl.dops.biz.common.forest.basicservice.dto.TenantInfoDTO;
import com.dl.dops.biz.common.forest.basicservice.dto.TenantListDTO;
import com.dl.dops.biz.common.forest.basicservice.param.AddTenantParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.DelTenantParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.TenantInfoParam;
import com.dl.dops.biz.common.forest.basicservice.param.TenantParam;
import com.dl.dops.biz.common.forest.basicservice.param.UpdateTenantParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.UpdateTenantStatusParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.UpdateTenantTrialParamDTO;
import com.dl.dops.biz.common.service.tenant.TenantInfoService;
import com.dl.dops.web.common.controllers.tenant.vo.TenantSimpleInfoVO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-15 14:55
 */
@Api("基础服务-租户管理")
@RestController
@RequestMapping({ "/dops/basicservice/tenant", "/dops/tenant" })
public class TenantController {

    @Resource
    private TenantInfoService tenantInfoService;

    @PostMapping("/info")
    @ApiOperation("查询租户信息")
    public ResultModel<TenantInfoDTO> info(@RequestBody @Validated TenantInfoParam param) {
        return ResultModel.success(tenantInfoService.info(param.getTenantCode()));
    }

    @PostMapping("/page")
    @ApiOperation("分页查询租户信息")
    public ResultPageModel<TenantListDTO> page(@RequestBody TenantParam p) {
        ResultPageModel<TenantListDTO> tenantListDTOResultPageModel = tenantInfoService.pageTenant(p);
        return tenantListDTOResultPageModel;
    }

    @PostMapping("/listall")
    @ApiOperation("查询所有租户信息")
    public ResultModel<List<TenantSimpleInfoVO>> listall() {
        List<TenantInfoDTO> tenantInfoDTOS = tenantInfoService.listAll();
        List<TenantSimpleInfoVO> reslut = tenantInfoDTOS.stream().map(dto -> {
            TenantSimpleInfoVO tenantInfoVO = new TenantSimpleInfoVO();
            tenantInfoVO.setTenantCode(dto.getTenantCode());
            tenantInfoVO.setName(dto.getName());
            tenantInfoVO.setStatus(dto.getStatus());
            return tenantInfoVO;
        }).collect(Collectors.toList());
        return ResultModel.success(reslut);
    }

    @ApiOperation("创建租户")
    @PostMapping("/add")
    public ResultModel<String> addTenant(@RequestBody @Validated AddTenantParamDTO param) {
        return ResultModel.success(tenantInfoService.add(param));
    }

    @ApiOperation("修改租户")
    @PostMapping("/update")
    public ResultModel updateTenant(@RequestBody @Validated UpdateTenantParamDTO param) {
        tenantInfoService.update(param);
        return ResultModel.success(null);
    }

    @ApiOperation("修改租户状态")
    @PostMapping("/update/status/{id}/{status}")
    public ResultModel updateStatus(@PathVariable Long id, @PathVariable Integer status) {
        UpdateTenantStatusParamDTO param = new UpdateTenantStatusParamDTO();
        param.setStatus(status);
        param.setId(id);
        tenantInfoService.updateStatus(param);
        return ResultModel.success(null);
    }

    @ApiOperation("删除租户")
    @PostMapping("/del/{id}")
    public ResultModel delTenant(@PathVariable Long id) {
        DelTenantParamDTO param = new DelTenantParamDTO();
        param.setId(id);
        tenantInfoService.del(param);
        return ResultModel.success(null);
    }

    @ApiOperation("修改租户是否试用")
    @PostMapping("/update/trial")
    public ResultModel updateTrial(@RequestBody @Validated UpdateTenantTrialParamDTO param) {
        tenantInfoService.updateTrial(param);
        return ResultModel.success(null);
    }

}
