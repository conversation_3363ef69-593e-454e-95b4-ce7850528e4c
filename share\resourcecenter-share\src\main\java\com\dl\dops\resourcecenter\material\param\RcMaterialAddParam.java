package com.dl.dops.resourcecenter.material.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-08 14:25
 */
@Data
public class RcMaterialAddParam implements Serializable {
    private static final long serialVersionUID = 5558332528548542221L;
    private String materialId;

    @ApiModelProperty("标题")
    @Length(max = 100)
    private String title;

    @ApiModelProperty("摘要")
    @Length(max = 100)
    private String remark;

    @ApiModelProperty("logo图地址")
    @Length(max = 200)
    private String logoImg;

    @ApiModelProperty("分类id")
    @Deprecated
    private String categoryId = "0";

    @ApiModelProperty("标签id")
    private List<String> tagIds;

    @ApiModelProperty("标签组类型")
    private Integer tagGroupType;

    @ApiModelProperty("素材类型")
    @NotNull
    private Integer materialType;

    @ApiModelProperty("文章类型：0 自建文章（默认）；1 微信公众号文章")
    private Integer articleType = 0;

    @ApiModelProperty("微信公众号文章地址")
    private String mpArticleSourceUrl;

    @ApiModelProperty("文件大小，单位KB")
    private Integer size;

    @ApiModelProperty("素材内容")
    private String content;

    @ApiModelProperty("添加来源：0资源中心，1本地上传")
    private Integer createFrom = 0;

    @ApiModelProperty("发布状态，1-已发布，2-取消发布")
    private Integer publishStatus = 2;

}
