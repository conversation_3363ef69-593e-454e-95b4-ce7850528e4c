<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="20220613135500" author="wangtc">

        <preConditions onFail="CONTINUE" onError="HALT">
            <not>
                <tableExists tableName="message_wwb785b406889c0028"/>
            </not>
        </preConditions>
        <comment>建表test</comment>
        <!-- 建表 -->
        <createTable tableName="message_wwb785b406889c0028" remarks="会话存档配置信息表">
            <column name="id" type="${bigInt.type}" remarks="唯一id" autoIncrement="true">
                <constraints nullable="false"
                             unique="true"
                             primaryKey="true"/>
            </column>
            <column name="msgid" type="${string.type}(200)" remarks="消息id">
                <constraints nullable="false"
                             unique="true"
                             primaryKey="false"/>
            </column>

            <column name="publickey_ver" type="${string.type}(200)" remarks="会话密钥版本号"/>
            <column name="seq" type="${bigInt.type}" remarks="序列号"/>
            <column name="msg_seq" type="${bigInt.type}" remarks="消息会话级别序列号"/>
            <column name="msgtype" type="${string.type}(50)" remarks="消息类型"/>
            <column name="msgtime" type="${bigInt.type}" remarks="消息发生时间"/>
            <column name="action" type="${string.type}(50)" remarks="消息动作类型"/>
            <column name="msgfrom" type="${string.type}(100)" remarks="发送人"/>
            <column name="msgfromName" type="${string.type}(200)" remarks="发送人名称"/>
            <column name="tolist" type="${text.type}" remarks="接收人"/>
            <column name="tolistname" type="${text.type}" remarks="接收人名称"/>
            <column name="json" type="${text.type}" remarks="消息json"/>
            <column name="roomid" type="${string.type}(200)" remarks="群id"/>
            <column name="createdt" type="${date.type}" remarks="创建时间"/>

            <column name="encrypt_chat_msg" type="${text.type}" remarks="消息密文"/>
            <column name="encrypt_random_key" type="${text.type}" remarks="消息随机密钥"/>
            <column name="content" type="${text.type}" remarks="消息待搜索内容"/>
            <column name="content_json" type="${text.type}" remarks="消息内容"/>
        </createTable>
        <rollback>
            <dropTable tableName="message_wwb785b406889c0028"/>
        </rollback>
        <!-- mysql innodb -->
        <modifySql dbms="mysql">
            <append value=" engine innodb"/>
        </modifySql>

    </changeSet>

</databaseChangeLog>