package com.dl.dops.biz.resourcecenter.manager.pack.helper;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dl.dops.biz.common.enums.SymbolE;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackAddBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackBranchBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackChainPathBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackElementAttachmentBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackElementBO;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackBranchPO;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackChainPathPO;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackElementPO;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackPO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackBO;
import com.dl.dops.resourcecenter.pack.enums.RcPackStatusEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-08 16:28
 */
public class PackHelper {

    public static PackPO cnvPackAddBO2PO(PackAddBO input, Long packId) {
        PackPO result = new PackPO();
        result.setSource(input.getSource());
        result.setPackId(packId);
        result.setCategory1(input.getCategory1());
        result.setCategory2(input.getCategory2());
        result.setSuggest(input.getSuggest());
        result.setSceneOverview(input.getSceneOverview());
        result.setDetailedDescription(input.getDetailedDescription());
        result.setSceneOverview(input.getSceneOverview());
        result.setTitle(input.getTitle());
        result.setDomain(input.getDomain());
        result.setStatus(RcPackStatusEnum.DRAFT.getStatus());
        result.setScene(input.getScene());
        return result;
    }

    public static PackBO cnvPackPO2DTO(PackPO input) {
        if (Objects.isNull(input)) {
            return null;
        }
        PackBO result = new PackBO();
        result.setPackId(input.getPackId());
        result.setTitle(input.getTitle());
        result.setSceneOverview(input.getSceneOverview());
        result.setCategory1(input.getCategory1());
        result.setCategory2(input.getCategory2());
        result.setDomain(input.getDomain());
        result.setDetailedDescription(input.getDetailedDescription());
        result.setSuggest(input.getSuggest());
        result.setStatus(input.getStatus());
        result.setScene(input.getScene());
        result.setCreateDt(input.getCreateDt());
        result.setModifyDt(input.getModifyDt());
        result.setCreateBy(input.getCreateBy());
        result.setModifyBy(input.getModifyBy());
        result.setCreatorName(input.getCreatorName());
        result.setModifyName(input.getModifyName());
        return result;
    }

    public static PackChainPathBO cnvPackChainPathPO2DTO(PackChainPathPO input) {
        if (Objects.isNull(input)) {
            return null;
        }
        PackChainPathBO result = new PackChainPathBO();
        result.setChainPathId(input.getChainPathId());
        result.setName(input.getName());
        result.setPackId(input.getPackId());
        result.setSort(input.getSort());
        return result;
    }

    public static PackElementBO cnnPackElementPO2DTO(PackElementPO input) {
        PackElementBO result = new PackElementBO();
        result.setElementId(input.getElementId());
        result.setPackId(input.getPackId());
        result.setChainPathId(input.getChainPathId());
        result.setBranchId(input.getBranchId());
        result.setContent(input.getContent());
        result.setExtData(input.getExtData());
        result.setSort(input.getSort());
        result.setTitle(input.getTitle());
        result.setType(input.getType());
        result.setRemark(input.getRemark());
        result.setAttachments(input.getAttachments());
        if (StringUtils.isNotBlank(input.getAttachments())) {
            List<PackElementAttachmentBO> attachmentList = new ArrayList<>();
            String[] arr = input.getAttachments().split(SymbolE.COMMA.getValue());
            for (int i = 0; i < arr.length; i++) {
                String str = arr[i];
                PackElementAttachmentBO attachmentDTO = JSONUtil.toBean(str, PackElementAttachmentBO.class);
                attachmentList.add(attachmentDTO);
            }
            result.setAttachmentList(attachmentList);
        }
        return result;
    }

    public static PackBranchBO cnvPackBranchPO2DTO(PackBranchPO input) {
        PackBranchBO result = new PackBranchBO();
        result.setChainPathId(input.getChainPathId());
        result.setBranchId(input.getBranchId());
        result.setSort(input.getSort());
        result.setName(input.getName());
        return result;
    }
}
