package com.dl.dops.biz.resourcecenter.manager.tag;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagPO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagAddSelfBO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagSelfBO;
import com.dl.dops.resourcecenter.tag.dto.TagSearchRespDTO;
import com.dl.dops.resourcecenter.tag.param.TagRelaParam;
import com.dl.dops.resourcecenter.tag.param.TagSearchParam;

import java.util.List;

public interface TagManager extends IService<TagPO>, CommonService {
    String add(TagAddSelfBO tagAddSelfBO);

    String edit(TagSelfBO selfBO);

    String delete(TagSelfBO selfBO);

    void batchMarkTag(TagRelaParam param);

    List<TagSearchRespDTO> search(TagSearchParam param);
}
