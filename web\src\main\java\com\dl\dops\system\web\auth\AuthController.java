package com.dl.dops.system.web.auth;

import com.dl.dops.biz.common.annotation.AdminAuth;
import com.dl.dops.biz.common.annotation.NotLogin;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.biz.system.dal.user.po.UserPO;
import com.dl.dops.biz.system.manager.user.ISysAuthCacheService;
import com.dl.dops.biz.system.manager.user.UserManager;
import com.dl.dops.biz.system.manager.user.bo.LoginParamBO;
import com.dl.dops.biz.system.manager.user.bo.UpdatePwdParamBO;
import com.dl.dops.biz.system.manager.user.dto.BasicUserDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.framework.core.interceptor.expdto.CertificateException;
import io.jsonwebtoken.MalformedJwtException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@Slf4j
@RestController
@RequestMapping("/dops/auth")
@Api("登入登出")
public class AuthController extends AbstractController {

    @Autowired
    private HttpServletResponse response;

    @Autowired
    private UserManager userManager;

    @Autowired
    private ISysAuthCacheService iSysAuthCacheService;

    @PostMapping("/login")
    @ApiOperation("账号密码登录")
    @NotLogin
    public ResultModel<BasicUserDTO> login(@Validated @RequestBody LoginParamBO paramBO) throws CertificateException {
        UserPO po = new UserPO();
        po.setAccount(paramBO.getAccount().trim());
        po.setPassword(paramBO.getPassword().trim());
        BasicUserDTO dto = userManager.login(po);
        if (dto == null) {
            throw new CertificateException("用户密码错误");
        }
        dto.setUserId(-1L);
        return ResultModel.success(dto);
    }

    @PostMapping("/logout")
    @ApiOperation("登出")
    public ResultModel<String> logout(@RequestHeader(Const.TOKEN_HEADER_NAME) String token) {
        log.info("删除用户的token" + token);
        BasicUserDTO dto;
        try {
            dto = userManager.parseJwtToken(token);
            userManager.logoutJwtToken(token);
        } catch (MalformedJwtException e) {
            log.warn("JWT token is illegal");
            throw BusinessServiceException.getInstance("token illegal");
        }
        log.info("用户" + "id为" + dto.getUserId() + ">>>>>>>>>>>>>>>>>>>已登出");
        return ResultModel.success("成功退出");
    }

    @PostMapping("/updpwd")
    @ApiOperation("用户密码修改")
    public ResultModel updpwd(@RequestBody @Validated UpdatePwdParamBO bo) {
        //修改密码
        if (userManager.updatePassword(bo)) {
            //退出登录
            userManager.logoutJwtToken(getToken());
        }
        return ResultModel.success("修改密码成功");
    }

    @AdminAuth
    @GetMapping("/refresh")
    @ApiOperation("刷新权限缓存")
    public ResultModel refreshCache() {
        iSysAuthCacheService.refreshMenuFunctionCache();
        return ResultModel.success("刷新权限缓存");
    }

}
