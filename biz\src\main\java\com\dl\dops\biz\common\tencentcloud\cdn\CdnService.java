package com.dl.dops.biz.common.tencentcloud.cdn;

import com.tencentcloudapi.cdn.v20180606.CdnClient;
import com.tencentcloudapi.cdn.v20180606.models.BriefDomain;
import com.tencentcloudapi.cdn.v20180606.models.DescribeDomainsRequest;
import com.tencentcloudapi.cdn.v20180606.models.DescribeDomainsResponse;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;

public class CdnService {

    public static void main(String[] args) throws TencentCloudSDKException {
        Credential credential = new Credential("AKIDEH4aqrb4GCqDJ9i01lFSO3SaR1LPCixw",
                "jBMHvHMAhyxfBQO4UdV5PRCgxxg9LGuH");
        CdnClient client = new CdnClient(credential, "");
        DescribeDomainsRequest request = new DescribeDomainsRequest();
        DescribeDomainsResponse response = client.DescribeDomains(request);
        BriefDomain[] domains = response.getDomains();
        for (BriefDomain domain : domains) {
            System.out.println("=============================================");
            System.out.println("getAppId:" + domain.getAppId());
            System.out.println("getDomain:" + domain.getDomain());
            System.out.println("getArea:" + domain.getArea());
            System.out.println("getDisable:" + domain.getDisable());
            System.out.println("getStatus:" + domain.getStatus());
            System.out.println("getCname:" + domain.getCname());
            System.out.println("getProduct:" + domain.getProduct());
        }

    }
}
