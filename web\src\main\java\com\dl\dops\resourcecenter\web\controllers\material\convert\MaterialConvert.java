package com.dl.dops.resourcecenter.web.controllers.material.convert;

import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialBO;
import com.dl.dops.resourcecenter.material.dto.RcMaterialDTO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-06-14 15:36
 */
public class MaterialConvert {

    public static RcMaterialDTO cnvCntMaterialDTO2VO(MaterialBO input) {
        RcMaterialDTO result = new RcMaterialDTO();
        result.setMaterialId(input.getMaterialId());
        result.setTitle(input.getTitle());
        result.setSize(input.getSize());
        result.setLogoImg(input.getLogoImg());
        result.setMaterialType(input.getMaterialType());
        result.setRemark(input.getRemark());
        result.setCategoryId(input.getCategoryIds());
        result.setCategoryIds(input.getCategoryIds());
        result.setCreateBy(input.getCreateBy());
        result.setModifyBy(input.getModifyBy());
        result.setArticleType(input.getArticleType());
        result.setMpArticleSourceUrl(input.getMpArticleSourceUrl());
        result.setCreateTime(input.getCreateTime());
        result.setPublishStatus(input.getPublishStatus());
        result.setContent(input.getContent());
        result.setTagInfoDTOList(input.getTagInfoDTOList());
        return result;
    }
}
