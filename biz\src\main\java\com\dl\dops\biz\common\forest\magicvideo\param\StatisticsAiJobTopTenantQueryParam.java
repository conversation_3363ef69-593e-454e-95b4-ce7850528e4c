package com.dl.dops.biz.common.forest.magicvideo.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-27 17:17
 */
@Data
public class StatisticsAiJobTopTenantQueryParam {

    @NotNull(message = "最小时间不能为空")
    @ApiModelProperty("最小时间")
    private Date minDt;

    @NotNull(message = "最大时间不能为空")
    @ApiModelProperty("最大时间")
    private Date maxDt;

    @NotNull(message = "ai任务类型不能为空")
    @ApiModelProperty("ai任务类型，1-数字人，2-TTS")
    private Integer aiJobType;

    @Min(value = 1, message = "查询租户数不能为0")
    @Max(value = 10, message = "查询租户数过多")
    @ApiModelProperty("查询前几名。默认为4")
    private Integer topNumber;
}
