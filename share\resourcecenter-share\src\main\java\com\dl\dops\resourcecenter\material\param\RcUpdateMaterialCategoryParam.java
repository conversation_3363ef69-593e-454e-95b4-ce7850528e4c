package com.dl.dops.resourcecenter.material.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("批量更新素材分类")
public class RcUpdateMaterialCategoryParam implements Serializable {
    @ApiModelProperty("素材id")
    @Size(min = 1, max = 20)
    private List<String> materialIds;

    @ApiModelProperty("分类id")
    @NotBlank
    private String categoryId;

}
