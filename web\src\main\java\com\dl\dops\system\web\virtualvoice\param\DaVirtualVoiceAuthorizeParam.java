package com.dl.dops.system.web.virtualvoice.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class DaVirtualVoiceAuthorizeParam implements Serializable {

    private static final long serialVersionUID = 3435015445940055700L;

    @ApiModelProperty(value = "数字声音唯一标识")
    @NotEmpty(message = "数字声音唯一标识必填")
    private String bizId;

    /**
     * 授权集合
     */
    @ApiModelProperty(value = "授权集合")
    private List<String> tenantCodeList;
}