package com.dl.dops.resourcecenter.pack.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("编辑服务包")
public class RcPackUpdateParam implements Serializable {
    private static final long serialVersionUID = -3706689534426833821L;
    @ApiModelProperty("服务包id")
    @NotBlank
    private String packId;

    @ApiModelProperty("标题")
    @Length(min = 1, max = 100)
    private String title;

    @ApiModelProperty("所属一级分类id")
    @NotBlank(message = "所属一级分类不能为空")
    private String category1;

    @ApiModelProperty("所属二级分类id")
    private String category2;

    @ApiModelProperty("场景概述")
    @Length(max = 150, message = "场景概述最多150个字")
    private String sceneOverview;

    @ApiModelProperty("所属行业")
    private Integer domain;

    @ApiModelProperty("详细描述")
    @Length(max = 200, message = "详细描述最多200个字")
    private String detailedDescription;

    @ApiModelProperty("运营投放建议")
    @Length(max = 200, message = "运营投放建议最多200个字")
    private String suggest;

    @ApiModelProperty("链路列表")
    @NotEmpty(message = "链路定义不能为空")
    private List<RcPackChainPathSaveParam> chainPathList;
}
