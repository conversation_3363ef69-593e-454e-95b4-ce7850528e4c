package com.dl.dops.system.web.voicetrain.helper;

import com.dl.aiservice.share.voiceclone.VoiceTrainJobDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultDTO;
import com.dl.aiservice.share.voiceclone.VoiceTrainResultPageQueryDTO;
import com.dl.dops.system.web.voicetrain.param.VoiceTrainResultPageQueryParam;
import com.dl.dops.system.web.voicetrain.vo.VoiceTrainJobVO;
import com.dl.dops.system.web.voicetrain.vo.VoiceTrainResultVO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-07 10:21
 */
public class VoiceTrainHelper {

    public static VoiceTrainResultPageQueryDTO cnvVoiceTrainResultPageQueryParam2DTO(
            VoiceTrainResultPageQueryParam queryParam) {
        VoiceTrainResultPageQueryDTO queryDTO = new VoiceTrainResultPageQueryDTO();
        queryDTO.setName(queryParam.getName());
        queryDTO.setExtModelCode(queryParam.getExtModelCode());
        queryDTO.setChannel(queryParam.getChannel());
        queryDTO.setStatus(queryParam.getStatus());
        queryDTO.setPageIndex(queryParam.getPageIndex());
        queryDTO.setPageSize(queryParam.getPageSize());
        return queryDTO;
    }

    public static VoiceTrainResultVO cnvTrainResultDTO2VO(VoiceTrainResultDTO input) {
        VoiceTrainResultVO result = new VoiceTrainResultVO();
        result.setBizId(input.getBizId() + "");
        result.setName(input.getName());
        result.setExtModelCode(input.getExtModelCode());
        result.setChannel(input.getChannel());
        result.setUserId(input.getUserId() + "");
        result.setTenantCode(input.getTenantCode());
        result.setTrainType(input.getTrainType());
        result.setSampleLink(input.getSampleLink());
        result.setStatus(input.getStatus());
        result.setTrainedNum(input.getTrainedNum());
        result.setLatestFailReason(input.getLatestFailReason());
        return result;
    }

    public static VoiceTrainJobVO cnvVoiceTrainJobDTO2VO(VoiceTrainJobDTO input) {
        VoiceTrainJobVO result = new VoiceTrainJobVO();
        result.setTenantCode(input.getTenantCode());
        result.setChannel(input.getChannel());
        result.setTrainType(input.getJobType());
        result.setGender(input.getGender());
        result.setTrainJobId(input.getTrainJobId() + "");
        result.setExtJobId(input.getExtJobId());
        result.setExtModelCode(input.getExtModelCode());
        result.setStatus(input.getStatus());
        result.setTrainName(input.getTrainName());
        result.setFailCode(input.getFailCode());
        result.setFailReason(input.getFailReason());
        result.setSampleLink(input.getSampleLink());
        result.setSource(input.getSource());
        result.setCreateDt(input.getCreateDt());
        return result;
    }
}
