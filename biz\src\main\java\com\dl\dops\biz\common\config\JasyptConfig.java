package com.dl.dops.biz.common.config;

import com.dl.framework.common.encryptor.SM4StringEncryptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JasyptConfig {

    @Value("${sm4.salt}")
    private String salt;

    @Bean("sm4StringEncryptor")
    public SM4StringEncryptor sm4StringEncryptor() {
        SM4StringEncryptor stringEncryptor = new SM4StringEncryptor();
        stringEncryptor.setSalt(salt);
        return stringEncryptor;
    }

    public static void main(String[] args) {
        SM4StringEncryptor stringEncryptor = new SM4StringEncryptor();
        stringEncryptor.setSalt("da024ustkidtclu3");
        System.out.println(stringEncryptor.encrypt("elastic"));
        System.out.println(stringEncryptor.encrypt("Softdl--=="));
    }

}
