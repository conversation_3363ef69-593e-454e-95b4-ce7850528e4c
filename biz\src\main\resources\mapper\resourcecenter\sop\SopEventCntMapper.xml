<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.dops.biz.resourcecenter.dal.sop.SopEventCntMapper">
    <resultMap id="resultMap" type="com.dl.dops.biz.resourcecenter.dal.sop.po.SopEventCntPO">
        <id column="id" property="id"/>
        <result column="event_id" property="eventId"/>
        <result column="cnt_type" property="cntType"/>
        <result column="cnt_id" property="cntId"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_dt" property="createDt"/>
        <result column="create_by" property="createBy"/>
        <result column="modify_dt" property="modifyDt"/>
        <result column="modify_by" property="modifyBy"/>
    </resultMap>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into sop_event_cnt
        (event_id,cnt_type,cnt_id,is_deleted,create_dt,create_by,modify_dt,modify_by)
        values
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.eventId},#{item.cntType},#{item.cntId},0,#{item.createDt},#{item.createBy},#{item.modifyDt},#{item.modifyBy})
        </foreach>
    </insert>

    <update id="batchLogicDeleteByIds">
        update sop_event_cnt
        set is_deleted = 1,
        modify_by = #{operatorId},
        modify_dt = now()
        where id in
        (
        <foreach collection="list" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </update>

    <update id="batchLogicDeleteByEventIds">
        update sop_event_cnt
        set is_deleted = 1,
        modify_by = #{operatorId},
        modify_dt = now()
        where event_id in
        (
        <foreach collection="list" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </update>
</mapper>