package com.dl.dops.biz.resourcecenter.manager.pack;

import cn.easyes.core.biz.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackPO;
import com.dl.dops.biz.resourcecenter.es.pack.po.EsIndexRcPack;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackAddBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackSearchBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackUpdateBO;

public interface PackManager extends IService<PackPO>, CommonService {
    /**
     * 新增服务包
     *
     * @param bo
     * @return
     */
    Long add(PackAddBO bo);

    /**
     * 编辑服务包
     *
     * @param bo
     * @return
     */
    Boolean edit(PackUpdateBO bo);

    /**
     * 服务包基本信息
     *
     * @param
     * @return
     */
    PackBO info(Long packId);

    /**
     * 分页查询服务包
     *
     * @param bo
     * @return
     */
    IPage<PackBO> pageQuery(PackSearchBO bo);

    /**
     * 从es中分页查询服务包
     *
     * @param bo
     * @return
     */
    PageInfo<EsIndexRcPack> pageFromEs(PackSearchBO bo);

}
