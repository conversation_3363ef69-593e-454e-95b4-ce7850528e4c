package com.dl.dops.biz.resourcecenter.dal.tag.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

import java.util.Date;

@TableName("tag_group")
@Data
public class TagGroupPO extends BasePO {

    @TableId
    private Long id;

    @TableField("tag_group_id")
    private Long tagGroupId;

    @TableField("name")
    private String name;

    @TableField("type")
    private Integer type;

    @TableField("ordered")
    private Integer ordered;

    @TableField("delete_dt")
    private Date deleteDt;

    @TableField("is_deleted")
    private Integer isDelete;
}
