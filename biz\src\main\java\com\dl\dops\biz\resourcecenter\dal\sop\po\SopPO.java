package com.dl.dops.biz.resourcecenter.dal.sop.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

@Data
@TableName("sop")
public class SopPO extends BasePO {
    private static final long serialVersionUID = 3056089526674548412L;
    @TableId
    public Long id;

    @TableField("sop_id")
    private Long sopId;

    @TableField("name")
    private String name;

    @TableField("remark")
    private String remark;

    @TableField("category1")
    private Long category1;

    @TableField("category2")
    private Long category2;

    @TableField("status")
    private Integer status;

    @TableField("sop_type")
    private Integer sopType;

    @TableField("is_deleted")
    private Integer isDeleted;

    @TableField("creator_name")
    private String creatorName;

    @TableField("delete_dt")
    private Long deleteDt;

    @TableField("source")
    private String source;

    @TableField("modify_name")
    private String modifyName;

}
