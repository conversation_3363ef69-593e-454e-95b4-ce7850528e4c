<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="20220607181956" author="wangtc">
        <!-- 预判断 -->
        <preConditions onFail="CONTINUE">
            <not>
                <tableExists tableName="liquibase_test3"/>
            </not>
        </preConditions>
        <comment>建表test</comment>
        <!-- 建表 -->
        <createTable tableName="liquibase_test3" remarks="测试">
            <column name="ID" type="${bigInt.type}" remarks="唯一id" autoIncrement="true">
                <constraints nullable="false"
                             unique="true"
                             primaryKey="true"/>
            </column>
            <column name="CATEGORY_NAME" type="${string.type}(100)" remarks="分类名称"
                    defaultValue="">
                <constraints nullable="false"
                             unique="false"
                             primaryKey="false"/>
            </column>
            <column name="REMARK" type="${text.type} " remarks="分类描述">
                <constraints nullable="false"
                             unique="false"
                             primaryKey="false"/>
            </column>
            <column name="CREATE_BY" type="${string.type}(200)" remarks="创建人"
                    defaultValue="">
                <constraints nullable="false"
                             unique="false"
                             primaryKey="false"/>
            </column>
            <column name="CREATE_DT" type="${date.type}" remarks="创建日期"/>
            <column name="TENANT_CODE" type="${string.type}(64)" remarks="租户"
                    defaultValue="">
                <constraints nullable="false"
                             unique="false"
                             primaryKey="false"/>
            </column>
        </createTable>
        <rollback>
            <dropTable tableName="liquibase_test3"/>
        </rollback>
        <!-- mysql innodb -->
        <modifySql dbms="mysql">
            <append value=" engine innodb DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"/>
            <replace replace="VARCHAR(100)" with="VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci"/>
        </modifySql>

    </changeSet>

</databaseChangeLog>