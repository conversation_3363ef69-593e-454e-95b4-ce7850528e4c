package com.dl.dops.biz.common.forest.magicvideo.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-01 17:31
 */
@Data
public class AssetProdInfoPageParam extends AbstractPageParam {

    /**
     * 代码
     */
    @ApiModelProperty("代码")
    private String secuCode;

    /**
     * 产品简称
     */
    @ApiModelProperty("产品简称")
    private String prodShortname;

    /**
     * 分类
     *
     * @see: com.dl.magicvideo.biz.manager.data.enums.AssetProdCategoryEnum
     */
    @NotBlank(message = "分类不能为空")
    @ApiModelProperty("分类，股票-STOCK")
    private String secucategory;

}
