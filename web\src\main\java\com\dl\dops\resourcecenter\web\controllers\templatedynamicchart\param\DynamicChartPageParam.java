package com.dl.dops.resourcecenter.web.controllers.templatedynamicchart.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@ApiModel("动态图表")
@Data
public class DynamicChartPageParam extends AbstractPageParam {

    /**
     * 模板ID
     */
    @ApiModelProperty("模板ID")
    @NotNull(message = "模板ID不能为空")
    private String templateId;

    @ApiModelProperty("名称搜索")
    @Size(max = 50)
    private String name;

    @ApiModelProperty("图表类型")
    @NotNull(message = "模板ID不能为空")
    private Integer type;
}
