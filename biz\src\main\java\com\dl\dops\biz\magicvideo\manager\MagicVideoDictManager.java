package com.dl.dops.biz.magicvideo.manager;

import cn.hutool.json.JSONUtil;
import com.dl.dops.biz.common.forest.magicvideo.MagicVideoClient;
import com.dl.dops.biz.common.forest.magicvideo.dto.AssetProdInfoVO;
import com.dl.dops.biz.common.forest.magicvideo.param.AssetProdInfoListParam;
import com.dl.dops.biz.common.forest.magicvideo.param.AssetProdInfoPageParam;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-20 15:18
 */
@Component
public class MagicVideoDictManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(MagicVideoDictManager.class);

    @Resource
    private MagicVideoClient magicVideoClient;

    /**
     * 分页查询产品
     *
     * @param param
     * @return
     */
    public ResultPageModel<AssetProdInfoVO> pageAssetProdInfo(AssetProdInfoPageParam param) {
        ResultPageModel<AssetProdInfoVO> result = magicVideoClient.pageAssetProdInfo(param);
        if (!result.isSuccess()) {
            LOGGER.error("分页查询产品失败,param:{},,, resultModel = {}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("分页查询产品失败");
        }
        return result;
    }

    /**
     * 查询产品列表
     *
     * @param param
     * @return
     */
    public List<AssetProdInfoVO> listAssetProdInfo(AssetProdInfoListParam param) {
        ResultModel<List<AssetProdInfoVO>> result = magicVideoClient.listAssetProdInfo(param);
        if (!result.isSuccess()) {
            LOGGER.error("查询产品列表失败,param:{},,, resultModel = {}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("查询产品列表失败");
        }
        return result.getDataResult();
    }

}
