package com.dl.dops.resourcecenter.script.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @describe: ScriptUploadFailedVO
 * @author: zhousx
 * @date: 2022/6/21 11:39
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT,
        fillPatternType = FillPatternTypeEnum.NO_FILL,
        borderBottom = BorderStyleEnum.NONE,
        borderLeft = BorderStyleEnum.NONE,
        borderRight = BorderStyleEnum.NONE,
        borderTop = BorderStyleEnum.NONE)
@HeadFontStyle(fontHeightInPoints = 18)
@ContentFontStyle(fontHeightInPoints = 11)
@ContentStyle(verticalAlignment = VerticalAlignmentEnum.CENTER,
        horizontalAlignment = HorizontalAlignmentEnum.LEFT,
        fillPatternType = FillPatternTypeEnum.NO_FILL,
        wrapped = BooleanEnum.TRUE)
public class RcScriptUploadFailedDTO implements Serializable {
    private static final long serialVersionUID = -6472294003207251516L;
    @ColumnWidth(40)
    @ExcelProperty({ "填写须知：", "1、请管理员使用此模版导入，从第8行开始为导入的话术内容；", "2、问题、话术内容均不能为空，为空将导致单条内容导入失败；一级与二级分类若为空，则默认进入“未分类”的类型；",
            "3、单个【问题】最多支持15个汉字，单个【一级/二级分类】最多支持10个汉字，单条【话术内容】最多支持500个汉字，超过部分将被截断；", "4、分类名称需按系统已维护分类填写，未匹配到的分类话术将导入失败。",
            "5、单次最多导入1000条话术，超出数量的话术将导入失败。", "问题" })
    private String question;

    @ColumnWidth(25)
    @ExcelProperty({ "填写须知：", "1、请管理员使用此模版导入，从第8行开始为导入的话术内容；", "2、问题、话术内容均不能为空，为空将导致单条内容导入失败；一级与二级分类若为空，则默认进入“未分类”的类型；",
            "3、单个【问题】最多支持15个汉字，单个【一级/二级分类】最多支持10个汉字，单条【话术内容】最多支持500个汉字，超过部分将被截断；", "4、分类名称需按系统已维护分类填写，未匹配到的分类话术将导入失败。",
            "5、单次最多导入1000条话术，超出数量的话术将导入失败。", "一级分类" })
    private String category1;

    @ColumnWidth(25)
    @ExcelProperty({ "填写须知：", "1、请管理员使用此模版导入，从第8行开始为导入的话术内容；", "2、问题、话术内容均不能为空，为空将导致单条内容导入失败；一级与二级分类若为空，则默认进入“未分类”的类型；",
            "3、单个【问题】最多支持15个汉字，单个【一级/二级分类】最多支持10个汉字，单条【话术内容】最多支持500个汉字，超过部分将被截断；", "4、分类名称需按系统已维护分类填写，未匹配到的分类话术将导入失败。",
            "5、单次最多导入1000条话术，超出数量的话术将导入失败。", "二级分类" })
    private String category2;

    @ColumnWidth(85)
    @ExcelProperty({ "填写须知：", "1、请管理员使用此模版导入，从第8行开始为导入的话术内容；", "2、问题、话术内容均不能为空，为空将导致单条内容导入失败；一级与二级分类若为空，则默认进入“未分类”的类型；",
            "3、单个【问题】最多支持15个汉字，单个【一级/二级分类】最多支持10个汉字，单条【话术内容】最多支持500个汉字，超过部分将被截断；", "4、分类名称需按系统已维护分类填写，未匹配到的分类话术将导入失败。",
            "5、单次最多导入1000条话术，超出数量的话术将导入失败。", "话术内容" })
    private String content;

    @ColumnWidth(40)
    @ExcelProperty({ "填写须知：", "1、请管理员使用此模版导入，从第8行开始为导入的话术内容；", "2、问题、话术内容均不能为空，为空将导致单条内容导入失败；一级与二级分类若为空，则默认进入“未分类”的类型；",
            "3、单个【问题】最多支持15个汉字，单个【一级/二级分类】最多支持10个汉字，单条【话术内容】最多支持500个汉字，超过部分将被截断；", "4、分类名称需按系统已维护分类填写，未匹配到的分类话术将导入失败。",
            "5、单次最多导入1000条话术，超出数量的话术将导入失败。", "导入失败原因" })
    private String reason;
}
