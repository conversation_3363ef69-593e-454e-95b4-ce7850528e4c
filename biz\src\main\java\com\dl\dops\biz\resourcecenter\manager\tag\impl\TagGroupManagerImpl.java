package com.dl.dops.biz.resourcecenter.manager.tag.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.resourcecenter.dal.tag.TagGroupMapper;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagGroupPO;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagPO;
import com.dl.dops.biz.resourcecenter.dal.tag.po.TagRelaPO;
import com.dl.dops.biz.resourcecenter.manager.tag.TagGroupManager;
import com.dl.dops.biz.resourcecenter.manager.tag.TagManager;
import com.dl.dops.biz.resourcecenter.manager.tag.TagRelaManager;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagGroupAddBO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagGroupDeleteBO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagGroupEditBO;
import com.dl.dops.biz.resourcecenter.manager.tag.bo.TagPageBO;
import com.dl.dops.biz.resourcecenter.manager.tag.helper.TagHelper;
import com.dl.dops.resourcecenter.tag.dto.TagGroupDetailDTO;
import com.dl.dops.resourcecenter.tag.dto.TagGroupPageDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
@Service
@Slf4j
@Component
public class TagGroupManagerImpl extends ServiceImpl<TagGroupMapper, TagGroupPO> implements TagGroupManager {

    @Autowired
    private HostTimeIdg hostTimeIdg;

    @Autowired
    private TagManager tagManager;

    @Autowired
    private TagRelaManager tagRelaManager;

    @Override
    public Long add(TagGroupAddBO groupAddBO) {
        //校验标签组名称是否已存在
        Assert.isTrue(groupAddBO.getTagAddBOList().size() <= 20, "一个标签组标签数量不能大于20个");
        LambdaQueryWrapper<TagGroupPO> queryWrapper = Wrappers.lambdaQuery(TagGroupPO.class);
        queryWrapper.eq(TagGroupPO::getType, groupAddBO.getType()).eq(TagGroupPO::getName, groupAddBO.getTagGroupName())
                .eq(TagGroupPO::getIsDelete, Const.ZERO);
        TagGroupPO resp = this.getOne(queryWrapper);
        Assert.isNull(resp, "该标签组名称已存在");

        //查询已有标签组的最大ordered
        Optional<TagGroupPO> maxOrderedTagGroupOpt = this
                .list(Wrappers.lambdaQuery(TagGroupPO.class).eq(TagGroupPO::getType, groupAddBO.getType())
                        .eq(TagGroupPO::getIsDelete, Const.ZERO).orderByDesc(TagGroupPO::getOrdered)).stream()
                .findFirst();
        Integer currentMaxOrdered = maxOrderedTagGroupOpt.isPresent() ?
                maxOrderedTagGroupOpt.get().getOrdered() :
                Const.ZERO;

        Long tagGroupId = hostTimeIdg.generateId().longValue();
        TagGroupPO tagGroupPO = new TagGroupPO();
        tagGroupPO.setName(groupAddBO.getTagGroupName());
        tagGroupPO.setTagGroupId(tagGroupId);
        tagGroupPO.setOrdered(currentMaxOrdered + 1);
        tagGroupPO.setType(groupAddBO.getType());
        tagGroupPO.setIsDelete(Const.ZERO);
        //保存标签组信息
        this.save(tagGroupPO);

        List<TagPO> tagPOList = new ArrayList<>();
        groupAddBO.getTagAddBOList().forEach(item -> {
            TagPO tagPO = new TagPO();
            tagPO.setTagId(hostTimeIdg.generateId().longValue());
            tagPO.setTagGroupId(tagGroupPO.getTagGroupId());
            tagPO.setName(item.getTagName());
            tagPO.setOrdered(item.getOrder());
            tagPO.setIsDelete(Const.ZERO);
            tagPOList.add(tagPO);
        });
        //保存标签列表
        tagManager.saveBatch(tagPOList);
        return tagGroupId;
    }

    @Override
    public Long edit(TagGroupEditBO tagGroupEditBO) {
        //判断重名
        LambdaQueryWrapper<TagGroupPO> wrapper = Wrappers.lambdaQuery(TagGroupPO.class);
        wrapper.eq(TagGroupPO::getName,tagGroupEditBO.getName()).eq(TagGroupPO::getIsDelete,Const.ZERO);
        TagGroupPO resp = this.getOne(wrapper);
        Assert.isNull(resp,"该标签组名称已存在");
        //更新
        LambdaQueryWrapper<TagGroupPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagGroupPO::getTagGroupId,tagGroupEditBO.getTagGroupId())
                .eq(TagGroupPO::getIsDelete,Const.ZERO);
        TagGroupPO tagGroupPO = this.getOne(queryWrapper);
        tagGroupPO.setOrdered(tagGroupEditBO.getOrder());
        tagGroupPO.setName(tagGroupEditBO.getName());
        this.updateById(tagGroupPO);
        return tagGroupPO.getTagGroupId();
    }

    @Override
    public IPage<TagGroupPageDTO> page(TagPageBO listBO) {
        IPage<TagGroupPO> groupPOPage = new Page<>(listBO.getPageIndex(), listBO.getPageSize());
        LambdaQueryWrapper<TagGroupPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TagGroupPO::getType, listBO.getType()).eq(TagGroupPO::getIsDelete, Const.ZERO);
        IPage<TagGroupPO> respPage = this.page(groupPOPage, wrapper);
        if (CollectionUtils.isEmpty(respPage.getRecords())) {
            return new Page<>();
        }

        List<TagPO> tagPOList = tagManager.list(Wrappers.lambdaQuery(TagPO.class).in(TagPO::getTagGroupId,
                groupPOPage.getRecords().stream().map(TagGroupPO::getTagGroupId).collect(Collectors.toList()))
                .eq(TagPO::getIsDelete, Const.ZERO));
        if (CollectionUtils.isEmpty(tagPOList)) {
            return TagHelper.buildPagePo2PageDTO(respPage, null);
        }

        Map<Long, List<TagPO>> tagListMap = tagPOList.stream().collect(Collectors.groupingBy(TagPO::getTagGroupId));
        Page<TagGroupPageDTO> tagGroupPageDTOPage = TagHelper.buildPagePo2PageDTO(respPage, tagListMap);
        return tagGroupPageDTOPage;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String delete(TagGroupDeleteBO deleteBO) {
        log.info("deleteBO:{}", JSONUtil.toJsonStr(deleteBO));
        //删除标签组
        LambdaUpdateWrapper<TagGroupPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TagGroupPO::getTagGroupId, Long.valueOf(deleteBO.getTagGroupId()))
                .eq(TagGroupPO::getIsDelete, Const.ZERO).set(TagGroupPO::getModifyBy, deleteBO.getOperatorId())
                .set(TagGroupPO::getModifyDt, new Date()).set(TagGroupPO::getDeleteDt, new Date())
                .set(TagGroupPO::getIsDelete, Const.ONE);
        this.update(wrapper);
        LambdaQueryWrapper<TagPO> tagQueryWrapper = new LambdaQueryWrapper<>();
        tagQueryWrapper.eq(TagPO::getTagGroupId, deleteBO.getTagGroupId()).eq(TagPO::getIsDelete, Const.ZERO);
        List<TagPO> tagPOList = tagManager.list(tagQueryWrapper);
        if (CollectionUtils.isEmpty(tagPOList)) {
            return deleteBO.getTagGroupId();
        }
        tagPOList.forEach(item -> {
            item.setIsDelete(Const.ONE);
        });
        //删除标签
        log.info("删除的标签 tagPOList:{}", JSONUtil.toJsonStr(tagPOList));
        tagManager.updateBatchById(tagPOList);
        //删除标签关联关系
        LambdaQueryWrapper<TagRelaPO> tagRelaQueryWrapper = new LambdaQueryWrapper<>();
        tagRelaQueryWrapper
                .in(TagRelaPO::getBizId, tagPOList.stream().map(item -> item.getTagId()).collect(Collectors.toList()))
                .eq(TagRelaPO::getIsDelete, Const.ZERO);
        List<TagRelaPO> tagRelaPOList = tagRelaManager.list(tagRelaQueryWrapper);
        if (CollectionUtils.isEmpty(tagRelaPOList)) {
            return deleteBO.getTagGroupId();
        }
        tagRelaPOList.forEach(item -> {
            item.setIsDelete(Const.ONE);
        });
        log.info("删除的关联关系 tagRelaPOList:{}", JSONUtil.toJsonStr(tagRelaPOList));
        tagRelaManager.updateBatchById(tagRelaPOList);
        tagRelaManager.batchUpdateBizModifyDt(tagRelaPOList.get(0).getType(),
                tagRelaPOList.stream().map(TagRelaPO::getBizId).collect(Collectors.toList()));
        return deleteBO.getTagGroupId();
    }

    @Override
    public TagGroupDetailDTO detail(Long tagGroupId, Integer needCountTagRela) {
        LambdaQueryWrapper<TagGroupPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagGroupPO::getTagGroupId, tagGroupId).eq(TagGroupPO::getIsDelete, Const.ZERO);
        TagGroupPO tagGroupPO = this.getOne(queryWrapper);

        LambdaQueryWrapper<TagPO> tagWrapper = new LambdaQueryWrapper<>();
        tagWrapper.eq(TagPO::getTagGroupId, tagGroupId).eq(TagPO::getIsDelete, Const.ZERO);
        List<TagPO> tagPOList = tagManager.list(tagWrapper);

        //无需查询标签关联数
        if (Const.ZERO.equals(needCountTagRela)) {
            return TagHelper.buildTagGroupDetailDTO(tagGroupPO, tagPOList, Collections.emptyMap());
        }

        //查询标签对应的记录的数量
        QueryWrapper<TagRelaPO> wrapper = new QueryWrapper<>();
        wrapper.in("tag_id", tagPOList.stream().map(TagPO::getTagId).collect(Collectors.toList())).groupBy("tag_id");
        wrapper.select("tag_id, count(1) as num");
        List<Map<String, Object>> counts = tagRelaManager.listMaps(wrapper);
        Map<Long, Integer> countMap = counts.stream().collect(Collectors
                .toMap(e -> Long.parseLong(String.valueOf(e.get("tag_id"))),
                        e -> Integer.parseInt(String.valueOf(e.get("num")))));
        TagGroupDetailDTO tagGroupDTO = TagHelper.buildTagGroupDetailDTO(tagGroupPO, tagPOList, countMap);

        return tagGroupDTO;
    }

}
