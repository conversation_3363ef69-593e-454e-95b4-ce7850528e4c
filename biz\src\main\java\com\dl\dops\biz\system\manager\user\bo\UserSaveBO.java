package com.dl.dops.biz.system.manager.user.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 11:22
 */
@Data
public class UserSaveBO {

    @ApiModelProperty("userId")
    private Long userId;

    @NotBlank
    @ApiModelProperty(value = "账户")
    private String account;

    @ApiModelProperty(value = "密码")
    private String password;

    private String userName;
}
