package com.dl.dops.biz.resourcecenter.manager.pack.bo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("编辑服务包")
public class PackUpdateBO {

    /**
     * 服务包id
     */
    private Long packId;

    /**
     * 标题
     */
    private String title;

    /**
     * 所属一级分类id
     */
    private Long category1;

    /**
     * 所属二级分类id
     */
    private Long category2;

    /**
     * 场景概述
     */
    private String sceneOverview;

    /**
     * 适用行业
     */
    private Integer domain;

    /**
     * 详细描述
     */
    private String detailedDescription;

    /**
     * 运营投放建议
     */
    private String suggest;

    /**
     * 来源
     */
    private String source;
}
