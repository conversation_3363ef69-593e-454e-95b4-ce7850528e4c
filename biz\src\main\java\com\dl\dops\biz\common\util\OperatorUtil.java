package com.dl.dops.biz.common.util;

import org.springframework.stereotype.Component;

/**
 * biz端的操作人holder
 */
@Component
public class OperatorUtil {

    private static ThreadLocal<Long> userIdHolder = new ThreadLocal<>();

    private static ThreadLocal<String> userNameHolder = new ThreadLocal<>();

    public void init(Long userId, String userName) {
        userIdHolder.set(userId);
        userNameHolder.set(userName);
    }

    public Long getOperator() {
        return userIdHolder.get();
    }

    public String getOperatorName() {
        return userNameHolder.get();
    }

    public void remove() {
        userIdHolder.remove();
        userNameHolder.remove();
    }
}
