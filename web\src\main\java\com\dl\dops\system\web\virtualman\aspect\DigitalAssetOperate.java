package com.dl.dops.system.web.virtualman.aspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DigitalAssetOperate {

    String value() default "";

    String bizId() default "";

    int operateType() default 0;

    int specialFlag() default 0;
}
