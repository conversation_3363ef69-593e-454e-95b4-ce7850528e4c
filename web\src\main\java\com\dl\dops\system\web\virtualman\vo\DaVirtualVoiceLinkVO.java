package com.dl.dops.system.web.virtualman.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-30 09:29
 */
@Data
public class DaVirtualVoiceLinkVO {

    /**
     * 语速
     */
    @ApiModelProperty("语速")
    private String speed;

    /**
     * 音量
     */
    @ApiModelProperty("音量")
    private String volume;

    /**
     * 试听链接
     */
    @ApiModelProperty("试听链接")
    private String sampleLink;

    /**
     * 音频时长(单位：毫秒)
     */
    @ApiModelProperty("音频时长(单位：毫秒)")
    private Long duration;

}
