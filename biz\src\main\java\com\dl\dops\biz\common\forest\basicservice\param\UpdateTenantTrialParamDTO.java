package com.dl.dops.biz.common.forest.basicservice.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-27 13:55
 */
@Data
public class UpdateTenantTrialParamDTO {

    @NotBlank(message = "租户编码不能为空")
    @ApiModelProperty("租户编码")
    private String tenantCode;

    @NotNull(message = "是否试用不能为空")
    @ApiModelProperty("是否试用，0-否，1-是")
    private Integer trialStatus;

    /**
     * 操作人id
     */
    @ApiModelProperty(hidden = true)
    private Long operatorId;

}
