package com.dl.dops.resourcecenter.web.controllers.material.process;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.net.URLDecoder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.enums.SymbolE;
import com.dl.dops.biz.common.tencentcloud.cos.CosFileUploadManager;
import com.dl.dops.biz.common.util.DateUtil;
import com.dl.dops.biz.common.util.spider.HttpTool;
import com.dl.dops.biz.common.util.spider.Resp;
import com.dl.dops.biz.common.util.spider.SpiderUtil;
import com.dl.dops.biz.resourcecenter.dal.material.po.MaterialPO;
import com.dl.dops.biz.resourcecenter.manager.material.MaterialManager;
import com.dl.framework.common.idg.HostTimeIdg;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.Charsets;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpGet;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName ArticleSpiderProcess
 * @Description
 * <AUTHOR>
 * @Date 2022/8/25 10:17
 * @Version 1.0
 **/
@Slf4j
@Component
public class ArticleSpiderProcess {

    private final static String NODE_VIDEO =
            "<video src=\"%s\" poster=\"%s\" webkit-playsinline=\"isiPhoneShowPlaysinline\" "
                    + "playsinline=\"isiPhoneShowPlaysinline\" preload=\"metadata\" crossorigin=\"anonymous\" "
                    + "controlslist=\"nodownload\" class=\"video_fill\"> 您的浏览器不支持 video 标签 </video>";
    private final static String GET_MP4 =
            "https://mp.weixin.qq.com/mp/videoplayer?action=get_mp_video_play_url&preview=0&__biz"
                    + "=MjM5Mjc5NDAzMw==&mid=2650985977&idx=1&vid=%s";

    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private MaterialManager materialManager;
    @Autowired
    private CosFileUploadManager cosFileUploadManager;

    public String transform(String sourceUrl, String imgUrl) {
        File file = writeToFile(imgUrl, hostTimeIdg.generateId().toString());
        if (Objects.isNull(file)) {
            log.warn("该文章无封面图片，url={}", sourceUrl);
        }
        String result = cosFileUploadManager.uploadFile(file, null);
        FileUtil.del(file);
        return result;
    }

    public void crawlArticleCnt(Long materialId, String mpArticleSourceUrl, Document doc) {
        if (Objects.isNull(materialId)) {
            log.error("微信公众号文章新增失败，materialId为空，source_url={}", mpArticleSourceUrl);
            return;
        }

        Element articlePart = doc.getElementById(SpiderUtil.KEY_JS_ARTICLE);
        if (Objects.isNull(articlePart)) {
            log.error("不存在要抓取的内容！url={}", mpArticleSourceUrl);
            return;
        }
        Elements allElements = articlePart.getAllElements();
        for (Element e : allElements) {
            if (Objects.isNull(e) || !e.children().isEmpty()) {
                continue;
            }
            String tagName = e.tagName();
            switch (tagName) {
            case "img":
                String attr = e.attr("data-src");
                if (StringUtils.isNotBlank(attr)) {
                    String imgUrl = transform(mpArticleSourceUrl, attr);
                    if (StringUtils.isNotBlank(imgUrl)) {
                        e.attr("src", imgUrl);
                    }
                }
                break;
            case "iframe":
                String mpVid = e.attr("data-mpvid");
                if (StringUtils.isBlank(mpVid)) {
                    break;
                }
                HttpGet post = new HttpGet(String.format(GET_MP4, mpVid));
                String respString = HttpTool.getRespString(post);
                JSONObject jsonObject = JSON.parseObject(respString);
                JSONArray urlArray = jsonObject.getJSONArray("url_info");
                List<Object> filesize = urlArray.stream()
                        .sorted(Comparator.comparing(x -> (Integer) ((JSONObject) x).get("filesize")))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filesize)) {
                    break;
                }
                Object o = filesize.get(filesize.size() - 1);
                String mp4Url = (String) ((JSONObject) o).get("url");
                String videoCover = URLDecoder.decode(e.attr("data-cover"), Charsets.UTF_8);
                String imgUrl = transform(mpArticleSourceUrl, videoCover);
                Element parent = e.parent();
                e.remove();
                parent.append(String.format(NODE_VIDEO, mp4Url, imgUrl));
            }
        }
        materialManager.lambdaUpdate().set(MaterialPO::getContent, articlePart.html())
                .eq(MaterialPO::getBizId, materialId).update();
    }

    public void crawlArticleCnt(Long materialId, String richText, Integer publishStatus) {
        if (Objects.isNull(materialId)) {
            return;
        }
        Resp<Document> docResp = SpiderUtil.getDocumentByCnt(richText);
        if (!docResp.isSuccess()) {
            log.error("不存在要抓取的内容！msg={};materialId={}", docResp.getMsg(), materialId);
            return;
        }
        Document docBody = docResp.getBody();
        Elements allElements = docBody.select(SpiderUtil.IMG);
        if (Objects.isNull(allElements) || allElements.size() <= Const.ZERO) {
            return;
        }
        for (Element e : allElements) {
            String attr = e.attr(SpiderUtil.IMG_DATA_SRC);
            if (SpiderUtil.checkWxArtImgUrl(attr).isSuccess()) {
                String imgUrl = transform(null, attr);
                if (StringUtils.isNotBlank(imgUrl)) {
                    e.attr(SpiderUtil.IMG_SRC, imgUrl);
                }
            }
        }
        materialManager.lambdaUpdate()
                .set(MaterialPO::getContent, docBody.html())
                .set(MaterialPO::getPublishStatus, publishStatus)
                .eq(MaterialPO::getBizId, materialId)
                .update();
    }

    private File writeToFile(String fileUrl, String fileName) {
        if (StringUtils.isBlank(fileUrl)) {
            return null;
        }
        String fileType = getFileType(fileUrl);
        if (StringUtils.isNotBlank(fileType) && StringUtils.isBlank(FileUtil.getSuffix(fileName))) {
            fileName = fileName + SymbolE.DOT.getValue() + fileType;
        }
        HttpURLConnection conn = null;
        FileOutputStream outStream = null;
        OutputStream bufferOs = null;
        File file = null;
        try {
            // 构建post请求
            HttpGet post = new HttpGet(fileUrl);
            byte[] data = readInputStream(HttpTool.getRespInputStream(post));
            if (data.length <= Const.ZERO) {
                return null;
            }
            //创建一个文件对象用来保存图片，默认保存当前工程根目录，起名叫Copy.jpg
            file = new File(dir(), fileName);
            //创建输出流
            outStream = new FileOutputStream(file, Boolean.TRUE);
            bufferOs = new BufferedOutputStream(outStream);
            //写入数据
            bufferOs.write(data);
            bufferOs.flush();
        } catch (Exception e) {
            log.error("", e);
        } finally {
            if (Objects.nonNull(conn)) {
                conn.disconnect();
            }
            IOUtils.closeQuietly(outStream);
            IOUtils.closeQuietly(bufferOs);
        }
        return file;
    }

    private String getFileType(String query) {
        if (Strings.isNullOrEmpty(query)) {
            return StringUtils.EMPTY;
        }
        if (!StringUtils.contains(query, SymbolE.QUESTION_MARK.getValue())) {
            return StringUtils.EMPTY;
        }
        Optional<String> opt = Arrays
                .stream(StringUtils.split(query, SymbolE.QUESTION_MARK.getValue())[1].split(SymbolE.AND.getValue()))
                .filter(x -> x.contains("wx_fmt=")).findFirst();
        if (opt.isPresent()) {
            String result = opt.get();
            return StringUtils.substring(result, result.indexOf(SymbolE.EQUAL.getValue()) + 1, result.length());
        }
        return StringUtils.EMPTY;
    }

    private File dir() {
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.YMD);
        String subDir = sdf.format(new Date());
        File dir = new File(subDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }

    private byte[] readInputStream(InputStream inStream) {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        //创建一个Buffer字符串
        byte[] buffer = new byte[6024];
        //每次读取的字符串长度，如果为-1，代表全部读取完毕
        int len;
        try {
            //使用一个输入流从buffer里把数据读取出来
            while ((len = inStream.read(buffer)) != -1) {
                //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
                outStream.write(buffer, 0, len);
            }
        } catch (Exception e) {
            log.error("", e);
        } finally {
            try {
                if (Objects.nonNull(inStream)) {
                    //关闭输入流
                    inStream.close();
                }
            } catch (Exception ee) {
                log.error("", ee);
            }
        }
        //把outStream里的数据写入内存
        return outStream.toByteArray();
    }
}
