package com.dl.dops.biz.resourcecenter.dal.sop;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.dops.biz.common.annotation.BaseDao;
import com.dl.dops.biz.resourcecenter.dal.sop.po.SopEventPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@BaseDao
@DS("resourcecenter")
public interface SopEventMapper extends BaseMapper<SopEventPO> {
    
    void batchInsert(@Param("list") List<SopEventPO> list);

    void batchLogicDeleteByIds(@Param("list") List<Long> list, @Param("operatorId") Long operatorId);

    void batchUpdate(@Param("list") List<SopEventPO> list);
}
