package com.dl.dops.magicvideo.web.music;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dl.dops.biz.magicvideo.manager.MagicVideoManager;
import com.dl.dops.magicvideo.music.bo.BackgroundMusicBO;
import com.dl.dops.magicvideo.music.bo.BackgroundMusicPageBO;
import com.dl.dops.magicvideo.music.dto.BackGroundMusicInternalDTO;
import com.dl.dops.magicvideo.music.param.BackgroundMusicAddParam;
import com.dl.dops.magicvideo.music.param.BackgroundMusicDelParam;
import com.dl.dops.magicvideo.music.param.BackgroundMusicPageParam;
import com.dl.dops.magicvideo.music.param.BackgroundMusicUpdateParam;
import com.dl.dops.magicvideo.web.music.convert.BackgroundMusicConvert;
import com.dl.dops.magicvideo.web.music.vo.BackgroundMusicVO;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/dops/background/music")
@Api("背景音乐")
public class BackgroundMusicController extends AbstractController {

    @Autowired
    private MagicVideoManager magicVideoManager;

    @PostMapping("/add")
    @ApiOperation("新增背景音乐")
    public ResultModel<String> add(@RequestBody BackgroundMusicAddParam param){
        BackgroundMusicBO bo = new BackgroundMusicBO();
        bo.setType(param.getType());
        bo.setUrl(param.getUrl());
        bo.setName(param.getName());
        bo.setUserId(getUserId());
        String bizId = magicVideoManager.addBackgroundMusic(bo);
        return ResultModel.success(bizId);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询背景音乐")
    public ResultPageModel<BackgroundMusicVO> page(@RequestBody BackgroundMusicPageParam param){
        BackgroundMusicPageBO bo = new BackgroundMusicPageBO();
        bo.setName(param.getName());
        bo.setType(param.getType());
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        IPage<BackGroundMusicInternalDTO> resp = magicVideoManager.pageQueryBackgroundMusic(
                bo);
        List<BackgroundMusicVO> data = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(resp.getRecords())){
            data = resp.getRecords().stream()
                    .map(BackgroundMusicConvert::cnvBackGroundMusicInternalDTO2VO).collect(Collectors.toList());
        }
        return pageQueryModel(resp,data);
    }

    @PostMapping("/update")
    @ApiOperation("更新背景音乐")
    public ResultModel<Boolean> update(@RequestBody BackgroundMusicUpdateParam param){
        BackgroundMusicBO bo = new BackgroundMusicBO();
        bo.setBizId(Long.valueOf(param.getBizId()));
        bo.setName(param.getName());
        bo.setUrl(param.getUrl());
        bo.setType(param.getType());
        bo.setUserId(getUserId());
        Boolean resp = magicVideoManager.updateBackgroundMusic(bo);
        return ResultModel.success(resp);
    }

    @PostMapping("/deleted")
    @ApiOperation("删除背景音乐")
    public ResultModel<Boolean> deleted(@RequestBody BackgroundMusicDelParam param){
        BackgroundMusicBO bo = new BackgroundMusicBO();
        bo.setBizId(Long.valueOf(param.getBizId()));
        bo.setUserId(getUserId());
        Boolean resp = magicVideoManager.delBackgroundMusic(bo);
        return ResultModel.success(resp);
    }
}
