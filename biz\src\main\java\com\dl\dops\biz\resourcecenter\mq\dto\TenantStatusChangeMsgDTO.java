package com.dl.dops.biz.resourcecenter.mq.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-30 10:33
 */
@Data
public class TenantStatusChangeMsgDTO implements Serializable {
    private static final long serialVersionUID = 4570239906488546850L;
    /**
     * 租户名称
     */
    private String name;

    /**
     * 租户id
     */
    private String tenantCode;

    /**
     * 状态 0未启用 1正常 2停用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createDt;

    /**
     * 修改时间
     */
    private Date modifyDt;

}