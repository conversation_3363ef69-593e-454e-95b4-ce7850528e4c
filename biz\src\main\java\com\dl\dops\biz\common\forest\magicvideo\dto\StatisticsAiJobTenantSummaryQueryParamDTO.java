package com.dl.dops.biz.common.forest.magicvideo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-08-20 16:43
 */
@Data
public class StatisticsAiJobTenantSummaryQueryParamDTO implements Serializable {
    private static final long serialVersionUID = 3771523892329146322L;

    @ApiModelProperty("最小时间")
    private Date minDt;

    @ApiModelProperty("最大时间")
    private Date maxDt;

    @NotEmpty(message = "ai任务类型列表不能为空")
    @ApiModelProperty("ai任务类型列表，1-数字人，2-TTS，3-数字人的tts")
    private List<Integer> aiJobTypeList;

    @NotBlank(message = "租户编码不能为空")
    @ApiModelProperty("租户编码")
    private String tenantCode;

}
