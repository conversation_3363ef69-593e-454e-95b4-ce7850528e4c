package com.dl.dops.system.web.role.param;

import com.dl.dops.biz.common.validation.ValidateStrategy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("角色信息")
public class RoleParam {

    @NotNull(groups = ValidateStrategy.update.class)
    @Min(value = 1, groups = ValidateStrategy.update.class)
    @ApiModelProperty("角色ID")
    private Long roleId;

    @Size(min = 2, max = 20)
    @ApiModelProperty("角色名称")
    private String roleName;
}

