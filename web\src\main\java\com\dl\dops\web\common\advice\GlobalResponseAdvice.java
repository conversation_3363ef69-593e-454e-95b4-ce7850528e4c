package com.dl.dops.web.common.advice;

import com.dl.dops.biz.common.filter.RepeatedlyRequestWrapper;
import com.dl.framework.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestControllerAdvice
public class GlobalResponseAdvice implements ResponseBodyAdvice {
    @Autowired
    private HttpServletRequest request;

    private static ThreadLocal<Long> TIME = new ThreadLocal<>();

    @Override
    public boolean supports(MethodParameter methodParameter, Class aClass) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class aClass,
            ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        log.info("接口:{}, 请求参数:{}, 返回参数:{}", request.getRequestURI(), RepeatedlyRequestWrapper.getBodyString(request),
                JsonUtils.toJSON(o));
        return o;
    }


}
