<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="20220609143100" author="wangtc">

        <!-- 条件判断 -->
        <!-- OnFail/OnError 值可能配置的值
        Value       Description

        HALT        立即停止执行整个更改日志。 [默认]
        CONTINUE    跳过* changeSet 。 下次更新时将再次尝试执行更改集。 继续 changelog *。
        MARK_RAN    跳过更改集，但将其标记为已执行。继续更改日志。
        WARN        输出警告并继续照常执行* changeSet * / * changelog *。
        -->
        <preConditions onFail="CONTINUE" onError="HALT">
            <!--
            可以使用nestable <and>、<or>和<not>标记将条件逻辑应用于前置条件。如果没有指定条件标记，则默认为AND。
            <preConditions onFail="WARN">
                <dbms type="oracle" />
                <runningAs username="SYSTEM" />
            </preConditions>

            如果使数据更改可以在oracle和mysql中可以执行，需要用到or表达式
            <preConditions>
            <or>
                <dbms type="oracle" />
                <dbms type="mysql" />
            </or>
            </preConditions>
            -->
            <tableExists tableName="sys_auth_menu"/>
            <and>
                <sqlCheck expectedResult="1">select count(*) from sys_auth_menu where id = 2 and tenant_code='DL'
                </sqlCheck>
            </and>
        </preConditions>
        <comment>修改DL菜单顺序 统计分析-活动统计</comment>
        <update tableName="sys_auth_menu">
            <column name="sort" value="6"/>
            <where>
                id = 2 and tenant_code='DL'
            </where>
        </update>
    </changeSet>

</databaseChangeLog>