package com.dl.dops.system.web.virtualman.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @describe: TtsParam
 * @author: zhousx
 * @date: 2023/5/6 13:49
 */
@Data
public class TtsParam {

    @ApiModelProperty("渠道")
    private Integer channel;

    @ApiModelProperty("声音键")
    private String voiceKey;

    @ApiModelProperty(value = "脚本")
    @NotBlank(message = "脚本不能为空")
    private String text;

    @ApiModelProperty(value = "语速 0.5~1.5")
    private String speed;

    @ApiModelProperty(value = "音量;取值范围 0.0-1.0，1.0 表示最大音量")
    private String volume;

    @ApiModelProperty(value = "声音类型， 1 克隆音；2 合成音。")
    private Integer voiceType;

}
