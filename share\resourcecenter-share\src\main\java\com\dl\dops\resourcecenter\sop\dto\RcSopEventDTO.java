package com.dl.dops.resourcecenter.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-07 13:26
 */
@Data
@ApiModel("资源中心sop事件视图对象")
public class RcSopEventDTO implements Serializable {

    private static final long serialVersionUID = -5852618262500285399L;
    @ApiModelProperty("事件id")
    private String eventId;

    @ApiModelProperty("sopId")
    private String sopId;

    @ApiModelProperty("触达形式")
    private Integer reachWay;

    @ApiModelProperty("触达形式名称")
    private String reachWayName;

    @ApiModelProperty("事项名称")
    private String name;

    @ApiModelProperty("时间规则类型")
    private Integer ruleType;

    @ApiModelProperty("规则内容")
    private String ruleContent;

    @ApiModelProperty("发送内容")
    private String content;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("有效期")
    private Integer validityPeriod;

}
