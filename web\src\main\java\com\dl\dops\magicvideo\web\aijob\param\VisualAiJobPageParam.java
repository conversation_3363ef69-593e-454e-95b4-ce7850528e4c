package com.dl.dops.magicvideo.web.aijob.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-15 16:05
 */
@Data
public class VisualAiJobPageParam extends AbstractPageParam {

    @NotBlank(message = "租户编号不能为空")
    @ApiModelProperty("租户编号")
    private String tenantCode;

    @ApiModelProperty("日期,传入则优先使用该日期的0点至24点")
    private Date date;

    @ApiModelProperty("最小时间")
    private Date minDt;

    @ApiModelProperty("最大时间")
    private Date maxDt;
}
