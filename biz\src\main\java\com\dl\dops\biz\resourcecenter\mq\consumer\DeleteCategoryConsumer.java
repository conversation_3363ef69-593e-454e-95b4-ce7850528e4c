package com.dl.dops.biz.resourcecenter.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.enums.SymbolE;
import com.dl.dops.biz.resourcecenter.mq.dto.DeleteCategoryMsgDTO;
import com.dl.dops.biz.resourcecenter.dal.material.po.MaterialPO;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackPO;
import com.dl.dops.biz.resourcecenter.dal.script.po.ScriptPO;
import com.dl.dops.biz.resourcecenter.dal.sop.po.SopPO;
import com.dl.dops.biz.resourcecenter.manager.material.MaterialManager;
import com.dl.dops.biz.resourcecenter.manager.pack.PackManager;
import com.dl.dops.biz.resourcecenter.manager.script.ScriptManager;
import com.dl.dops.biz.resourcecenter.manager.sop.SopManager;
import com.dl.dops.resourcecenter.category.enums.RcCategoryLevelEnum;
import com.dl.dops.resourcecenter.category.enums.RcCategoryTypeEnum;
import com.dl.dops.resourcecenter.material.enums.RcMaterialTypeEnum;
import com.dl.dops.resourcecenter.pack.enums.RcPackSceneEnum;
import com.dl.dops.resourcecenter.sop.enums.RcSopTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 分类删除事件的消费者
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-05 17:59
 */
@Component
public class DeleteCategoryConsumer {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeleteCategoryConsumer.class);

    @Resource
    private MaterialManager materialManager;

    @Resource
    private ScriptManager scriptManager;

    @Resource
    private SopManager sopManager;

    @Resource
    private PackManager packManager;

    @StreamListener("deleterccategoryconsumer")
    public void consume(String msg) {
        LOGGER.info("收到任务完成的消息，input:{}", msg);
        DeleteCategoryMsgDTO msgDTO = JSONUtil.toBean(msg, DeleteCategoryMsgDTO.class);

        RcCategoryTypeEnum categoryTypeEnum = RcCategoryTypeEnum.parse(msgDTO.getCategoryType());
        if (Objects.isNull(categoryTypeEnum)) {
            LOGGER.error("该分类不存在!  msg：{}", msg);
            return;
        }
        LOGGER.debug("开始处理分类删除，msg:{}", msg);
        switch (categoryTypeEnum) {
        case RS_BIZ_TYPE_ARTICLE:
            this.handlerMaterial(msgDTO, RcMaterialTypeEnum.ARTICLE);
            return;
        case RS_BIZ_TYPE_WEBPAGE:
            this.handlerMaterial(msgDTO, RcMaterialTypeEnum.WEBPAGE);
            return;
        case RS_BIZ_TYPE_VIDEO:
            this.handlerMaterial(msgDTO, RcMaterialTypeEnum.VIDEO);
            return;
        case RS_BIZ_TYPE_FILE:
            this.handlerMaterial(msgDTO, RcMaterialTypeEnum.FILE);
            return;
        case RS_BIZ_TYPE_TEXT:
            this.handlerMaterial(msgDTO, RcMaterialTypeEnum.TEXT);
            return;
        case RS_BIZ_TYPE_IMAGE:
            this.handlerMaterial(msgDTO, RcMaterialTypeEnum.IMAGE);
            return;
        case RS_ROOM_SOP:
            this.handlerSop(msgDTO, RcSopTypeEnum.GROUP);
            return;
        case RS_CUSTOMER_SOP:
            this.handlerSop(msgDTO, RcSopTypeEnum.INDIVIDUAL);
            return;
        case RS_ENTERPRISE_SCRIPT:
            this.handlerScript(msgDTO);
            return;
        case RS_OPERATE_ASSISTENT_PACK:
            this.handlerPack(msgDTO, RcPackSceneEnum.OPERATE_ASSISTENT);
            return;
        case RS_VIDEO_ASSISTENT_PACK:
            this.handlerPack(msgDTO, RcPackSceneEnum.VIDEO_ASSISTENT);
            return;
        default:
            LOGGER.warn("暂未处理该分类类型的删除事件。msg:{}", msg);
        }
        LOGGER.debug("分类删除处理完毕，msg:{}", msg);
    }

    private void handlerMaterial(DeleteCategoryMsgDTO msgDTO, RcMaterialTypeEnum materialTypeEnum) {
        //1.分页查询出分类不为0的素材
        LambdaQueryWrapper<MaterialPO> queryWrapper = Wrappers.lambdaQuery(MaterialPO.class);
        queryWrapper.eq(MaterialPO::getMaterialType, materialTypeEnum.getCode())
                .ne(MaterialPO::getCategoryIds, Const.ZERO_STR).eq(MaterialPO::getIsDeleted, 0);

        Page<MaterialPO> pageQuery = new Page<MaterialPO>(Const.ONE, Const.FIFTY);
        IPage<MaterialPO> pageResult = materialManager.getBaseMapper().selectPage(pageQuery, queryWrapper);
        Date now = new Date();

        //2.拼接分类,若是一级分类，则直接拼接一级分类id；若是二级分类，则以"一级分类id-二级分类id"的格式拼接
        String categoryPairing;
        if (RcCategoryLevelEnum.LEVEL_ONE.getCode().equals(msgDTO.getCategoryLevel())) {
            categoryPairing = String.valueOf(msgDTO.getCategoryBizId());
        } else {
            categoryPairing = msgDTO.getParentBizId() + SymbolE.MINUS.getValue() + msgDTO.getCategoryBizId();
        }
        String finalCategoryPairing = categoryPairing;

        List<MaterialPO> updatePOList = new ArrayList<>();
        while (pageResult.getCurrent() <= pageResult.getPages()) {
            //3.遍历素材
            pageResult.getRecords().stream().forEach(po -> {
                //根据,将分类字符串拆分成分类对儿列表
                List<String> categoryPairingList = Arrays.asList(po.getCategoryIds().split(SymbolE.COMMA.getValue()));

                //剩余的分类对儿
                List<String> leftCategoryPairingList = new ArrayList<>();
                AtomicBoolean needUpdate = new AtomicBoolean(false);

                categoryPairingList.stream().forEach(paring -> {
                    //若删除的是一级分类，则根据打头匹配
                    if (RcCategoryLevelEnum.LEVEL_ONE.getCode().equals(msgDTO.getCategoryLevel())) {
                        if (paring.startsWith(finalCategoryPairing)) {
                            needUpdate.set(true);
                        } else {
                            leftCategoryPairingList.add(paring);
                        }
                        //若删除的是二级分类，则判断对儿与对儿是否相等
                    } else {
                        if (StringUtils.equals(paring, finalCategoryPairing)) {
                            needUpdate.set(true);
                        } else {
                            leftCategoryPairingList.add(paring);
                        }
                    }
                });

                //不需要更新，直接进入下一个
                if (!needUpdate.get()) {
                    return;
                }

                //拼接剩余的分类对儿
                StringBuffer categorySbf = new StringBuffer();
                if (CollectionUtils.isEmpty(leftCategoryPairingList)) {
                    categorySbf.append(Const.ZERO_STR);
                } else {
                    //多个分类对儿间用,分隔
                    for (int i = 0; i < leftCategoryPairingList.size(); i++) {
                        categorySbf.append(leftCategoryPairingList.get(i));
                        if (i < leftCategoryPairingList.size() - 1) {
                            categorySbf.append(SymbolE.COMMA.getValue());
                        }
                    }
                }
                po.setCategoryIds(categorySbf.toString());
                po.setModifyDt(now);
                po.setModifyBy(msgDTO.getOperatorUserId());
                updatePOList.add(po);
            });

            if (pageResult.getCurrent() == pageResult.getPages()) {
                break;
            }
            pageQuery.setCurrent(pageQuery.getCurrent() + 1);
            pageResult = materialManager.getBaseMapper().selectPage(pageQuery, queryWrapper);
        }

        //4.批量更新
        if (CollectionUtils.isNotEmpty(updatePOList)) {
            materialManager.saveOrUpdateBatch(updatePOList);
        }
    }

    private void handlerScript(DeleteCategoryMsgDTO msgDTO) {
        //1.分页查询
        LambdaQueryWrapper<ScriptPO> queryWrapper = Wrappers.lambdaQuery(ScriptPO.class);
        queryWrapper.eq(ScriptPO::getIsDeleted, 0);
        if (RcCategoryLevelEnum.LEVEL_ONE.getCode().equals(msgDTO.getCategoryLevel())) {
            queryWrapper.eq(ScriptPO::getCategory1, msgDTO.getCategoryBizId());
        } else {
            queryWrapper.eq(ScriptPO::getCategory2, msgDTO.getCategoryBizId());
        }
        Page<ScriptPO> pageQuery = new Page<ScriptPO>(Const.ONE, Const.FIFTY);
        IPage<ScriptPO> pageResult = scriptManager.getBaseMapper().selectPage(pageQuery, queryWrapper);
        Date now = new Date();

        List<ScriptPO> updatePOList = new ArrayList<>();
        while (pageResult.getCurrent() <= pageResult.getPages()) {
            //2.设置分类为0
            List<ScriptPO> scriptPOList = pageResult.getRecords();
            scriptPOList.forEach(po -> {
                po.setCategory1(0L);
                po.setCategory2(0L);
                po.setModifyDt(now);
                po.setModifyBy(msgDTO.getOperatorUserId());
            });
            updatePOList.addAll(scriptPOList);

            if (pageResult.getCurrent() == pageResult.getPages()) {
                break;
            }
            pageQuery.setCurrent(pageQuery.getCurrent() + 1);
            pageResult = scriptManager.getBaseMapper().selectPage(pageQuery, queryWrapper);
        }
        //3.批量更新
        if (CollectionUtils.isNotEmpty(updatePOList)) {
            scriptManager.saveOrUpdateBatch(updatePOList);
        }
    }

    private void handlerSop(DeleteCategoryMsgDTO msgDTO, RcSopTypeEnum sopTypeEnum) {
        //1.分页查询
        LambdaQueryWrapper<SopPO> queryWrapper = Wrappers.lambdaQuery(SopPO.class);
        queryWrapper.eq(SopPO::getSopType, sopTypeEnum.getCode()).eq(SopPO::getIsDeleted, 0);
        if (RcCategoryLevelEnum.LEVEL_ONE.getCode().equals(msgDTO.getCategoryLevel())) {
            queryWrapper.eq(SopPO::getCategory1, msgDTO.getCategoryBizId());
        } else {
            queryWrapper.eq(SopPO::getCategory2, msgDTO.getCategoryBizId());
        }
        Page<SopPO> pageQuery = new Page<SopPO>(Const.ONE, Const.FIFTY);
        IPage<SopPO> pageResult = sopManager.getBaseMapper().selectPage(pageQuery, queryWrapper);
        Date now = new Date();

        List<SopPO> updatePOList = new ArrayList<>();
        while (pageResult.getCurrent() <= pageResult.getPages()) {
            //2.设置分类为0
            List<SopPO> sopPOList = pageResult.getRecords();
            sopPOList.forEach(po -> {
                po.setCategory1(0L);
                po.setCategory2(0L);
                po.setModifyDt(now);
                po.setModifyBy(msgDTO.getOperatorUserId());
            });
            updatePOList.addAll(sopPOList);

            if (pageResult.getCurrent() == pageResult.getPages()) {
                break;
            }
            pageQuery.setCurrent(pageQuery.getCurrent() + 1);
            pageResult = sopManager.getBaseMapper().selectPage(pageQuery, queryWrapper);
        }
        //3.批量更新
        if (CollectionUtils.isNotEmpty(updatePOList)) {
            sopManager.saveOrUpdateBatch(updatePOList);
        }
    }

    private void handlerPack(DeleteCategoryMsgDTO msgDTO, RcPackSceneEnum packSceneEnum) {
        //1.分页查询
        LambdaQueryWrapper<PackPO> queryWrapper = Wrappers.lambdaQuery(PackPO.class);
        queryWrapper.eq(PackPO::getScene, packSceneEnum.getScene()).eq(PackPO::getIsDeleted, 0);
        if (RcCategoryLevelEnum.LEVEL_ONE.getCode().equals(msgDTO.getCategoryLevel())) {
            queryWrapper.eq(PackPO::getCategory1, msgDTO.getCategoryBizId());
        } else {
            queryWrapper.eq(PackPO::getCategory2, msgDTO.getCategoryBizId());
        }
        Page<PackPO> pageQuery = new Page<PackPO>(Const.ONE, Const.FIFTY);
        IPage<PackPO> pageResult = packManager.getBaseMapper().selectPage(pageQuery, queryWrapper);
        Date now = new Date();

        List<PackPO> updatePOList = new ArrayList<>();
        while (pageResult.getCurrent() <= pageResult.getPages()) {
            //2.设置分类为0
            List<PackPO> packPOList = pageResult.getRecords();
            packPOList.forEach(po -> {
                po.setCategory1(0L);
                po.setCategory2(0L);
                po.setModifyDt(now);
                po.setModifyBy(msgDTO.getOperatorUserId());
            });
            updatePOList.addAll(packPOList);

            if (pageResult.getCurrent() == pageResult.getPages()) {
                break;
            }
            pageQuery.setCurrent(pageQuery.getCurrent() + 1);
            pageResult = packManager.getBaseMapper().selectPage(pageQuery, queryWrapper);
        }
        //3.批量更新
        if (CollectionUtils.isNotEmpty(updatePOList)) {
            packManager.saveOrUpdateBatch(updatePOList);
        }
    }
}
