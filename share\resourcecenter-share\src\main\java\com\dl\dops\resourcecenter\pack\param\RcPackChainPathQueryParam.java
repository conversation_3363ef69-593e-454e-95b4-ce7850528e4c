package com.dl.dops.resourcecenter.pack.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-14 16:30
 */
@Data
@ApiModel("服务包链路查询参数")
public class RcPackChainPathQueryParam implements Serializable {

    private static final long serialVersionUID = -2497371972960331755L;
    @ApiModelProperty("服务包id")
    @NotBlank(message = "服务包id不能为空")
    private String packId;

    @ApiModelProperty("链路id")
    @NotBlank(message = "链路id不能为空")
    private String chainPathId;

}
