package com.dl.dops.biz.resourcecenter.dal.script.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

/**
 * @TableName script_material
 */
@TableName(value = "script_material")
@Data
public class ScriptMaterialPO extends BasePO {
    private static final long serialVersionUID = 3789137964008615483L;
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 话术id
     */
    private Long scriptId;

    /**
     * 素材id
     */
    private Long materialId;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDeleted;
}