package com.dl.dops.magicvideo.web.statistics;

import com.dl.dops.biz.common.forest.magicvideo.dto.StatisticsAiJobDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.StatisticsAiJobTenantDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.StatisticsAiJobTenantSummaryDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.StatisticsAiJobTenantSummaryQueryParamDTO;
import com.dl.dops.biz.common.forest.magicvideo.param.StatisticsAiJobPageParam;
import com.dl.dops.biz.common.forest.magicvideo.param.StatisticsAiJobTopTenantQueryParam;
import com.dl.dops.biz.magicvideo.manager.MagicVideoAiJobManager;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-15 15:39
 */
@RestController
@RequestMapping("/dops/magicvideo/statistics/aijob")
@Api("ai任务统计管理")
public class StatisticsAiJobController {

    @Resource
    private MagicVideoAiJobManager magicVideoAiJobManager;

    @PostMapping("/page")
    @ApiOperation("分页查询ai任务数据统计")
    public ResultPageModel<StatisticsAiJobDTO> pageAiJobStatistics(@RequestBody StatisticsAiJobPageParam param) {
        return magicVideoAiJobManager.pageAiJobStatistics(param);
    }

    @PostMapping("/querytoptenantstatistics")
    @ApiOperation("查询前几名租户的ai任务数据统计")
    public ResultModel<List<StatisticsAiJobTenantDTO>> queryTopTenantStatistics(
            @RequestBody StatisticsAiJobTopTenantQueryParam param) {
        return magicVideoAiJobManager.queryTopTenantStatistics(param);
    }

    @PostMapping("/specifictenantsummary")
    @ApiOperation("指定租户的ai任务数据统计汇总")
    public ResultModel<StatisticsAiJobTenantSummaryDTO> specificTenantSummary(
            @RequestBody @Validated StatisticsAiJobTenantSummaryQueryParamDTO param) {
        return magicVideoAiJobManager.specificTenantSummary(param);
    }

}
