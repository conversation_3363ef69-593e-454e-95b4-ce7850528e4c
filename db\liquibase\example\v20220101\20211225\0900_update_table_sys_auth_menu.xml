<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">

    <!-- id 20220607134456 yyyyMMddHHmmss 年月日时分秒 -->
    <changeSet id="20220607134456" author="wangtc">

        <!-- 条件判断 -->
        <!-- OnFail/OnError 值可能配置的值
        Value       Description

        HALT        立即停止执行整个更改日志。 [默认]
        CONTINUE    跳过* changeSet 。 下次更新时将再次尝试执行更改集。 继续 changelog *。
        MARK_RAN    跳过更改集，但将其标记为已执行。继续更改日志。
        WARN        输出警告并继续照常执行* changeSet * / * changelog *。
        -->
        <preConditions onFail="CONTINUE" onError="HALT">
            <!--
            可以使用nestable <and>、<or>和<not>标记将条件逻辑应用于前置条件。如果没有指定条件标记，则默认为AND。
            <preConditions onFail="WARN">
                <dbms type="oracle" />
                <runningAs username="SYSTEM" />
            </preConditions>

            如果使数据更改可以在oracle和mysql中可以执行，需要用到or表达式
            <preConditions>
            <or>
                <dbms type="oracle" />
                <dbms type="mysql" />
            </or>
            </preConditions>
            -->
            <tableExists tableName="sys_auth_menu"/>
            <and>
                <sqlCheck expectedResult="0">select count(*) from sys_auth_menu where id in (2,21)</sqlCheck>
            </and>
        </preConditions>
        <comment>增加菜单 统计分析-活动统计</comment>
        <insert tableName="sys_auth_menu">
            <column name="id">2</column>
            <column name="parent_id">0</column>
            <column name="name">统计分析</column>
            <column name="menu_level">1</column>
            <!-- <![CDATA[]]> 不解析内容 -->
            <column name="url"><![CDATA[/stat]]></column>
            <!-- value="" 空串  <column name="xx"></column> 空值NULL -->
            <column name="icon" value=""/>
            <column name="sort">5</column>
            <column name="tenant_code">DC</column>
            <!-- valueComputed  通过函数/存储过程 计算出来结果-->
            <column name="create_dt" valueComputed="${sysdate.type}"/>
            <column name="modify_dt" valueComputed="${sysdate.type}"/>
            <column name="create_by">0</column>
            <column name="modify_by">0</column>
        </insert>
        <insert tableName="sys_auth_menu">
            <column name="id">21</column>
            <column name="parent_id">2</column>
            <column name="name">活动统计</column>
            <column name="menu_level">2</column>
            <column name="url"><![CDATA[/statistics/activity]]></column>
            <column name="icon" value=""/>
            <column name="sort">1</column>
            <column name="tenant_code">DC</column>
            <column name="create_dt" valueComputed="${sysdate.type}"/>
            <column name="modify_dt" valueComputed="${sysdate.type}"/>
            <column name="create_by">0</column>
            <column name="modify_by">0</column>
        </insert>
        <!-- 回滚脚本 -->
        <rollback>
            <delete tableName="sys_auth_menu">
                <where>
                    id in (2,21)
                </where>
            </delete>
        </rollback>
    </changeSet>

</databaseChangeLog>