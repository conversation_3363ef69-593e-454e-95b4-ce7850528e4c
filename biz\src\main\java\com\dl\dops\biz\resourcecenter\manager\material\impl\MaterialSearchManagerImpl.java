package com.dl.dops.biz.resourcecenter.manager.material.impl;

import cn.easyes.core.biz.PageInfo;
import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.easyes.core.toolkit.EsWrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.resourcecenter.es.material.EsIndexRcMaterialMapper;
import com.dl.dops.biz.resourcecenter.es.material.po.EsIndexRcMaterial;
import com.dl.dops.biz.resourcecenter.manager.category.enums.DefaultCategoryEnum;
import com.dl.dops.biz.resourcecenter.manager.material.MaterialSearchManager;
import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialBO;
import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialSearchBO;
import com.dl.dops.resourcecenter.tag.TagDefaultEnum;
import com.dl.framework.common.utils.JsonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName MaterialSearchManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/7/5 16:21
 * @Version 1.0
 **/
@Service
@Slf4j
public class MaterialSearchManagerImpl implements MaterialSearchManager {

    @Autowired
    private EsIndexRcMaterialMapper esIndexRcMaterialMapper;

    @Autowired
    private OperatorUtil operatorUtil;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    private final static String MATERIAL_INDEX = "dl_rc_material";

    @Override
    public IPage<MaterialBO> search(MaterialSearchBO searchParamBO) {
        paramsCheck(searchParamBO);
        LambdaEsQueryWrapper<EsIndexRcMaterial> wrapper = EsWrappers.lambdaQuery(EsIndexRcMaterial.class)
                .eq(EsIndexRcMaterial::getIsDeleted, Boolean.FALSE);
        wrapper.and(StringUtils.isNotBlank(searchParamBO.getTitle()),
                w -> w.like(EsIndexRcMaterial::getTitle, searchParamBO.getTitle()).or()
                        .like(EsIndexRcMaterial::getRemark, searchParamBO.getTitle()).or()
                        .match(EsIndexRcMaterial::getTitle, searchParamBO.getTitle()).or()
                        .match(EsIndexRcMaterial::getRemark, searchParamBO.getTitle()));
        wrapper.in(CollectionUtils.isNotEmpty(searchParamBO.getMaterialTypes()), EsIndexRcMaterial::getMaterialType,
                searchParamBO.getMaterialTypes());

        //无标签
        if (TagDefaultEnum.UNTAG.getCode().equals(searchParamBO.getTagId())) {
            wrapper.isNull(EsIndexRcMaterial::getTagIds);
            //指定标签
        } else if (!TagDefaultEnum.ALL.getCode().equals(searchParamBO.getTagId())) {
            wrapper.in("tag_ids", searchParamBO.getTagId());
        }

        // 分类
        if (StringUtils.equals(searchParamBO.getCategoryId(), DefaultCategoryEnum.UNCLASSIFIED.getCode())) {
            wrapper.eq(EsIndexRcMaterial::getCategoryIds, searchParamBO.getCategoryId());
        } else {
            wrapper.like(StringUtils.isNotBlank(searchParamBO.getCategoryId()) && !StringUtils
                            .equals(searchParamBO.getCategoryId(), DefaultCategoryEnum.ALL.getCode()),
                    EsIndexRcMaterial::getCategoryIds, searchParamBO.getCategoryId());
        }

        wrapper.eq(Objects.nonNull(searchParamBO.getPublishStatus()), EsIndexRcMaterial::getPublishStatus,
                searchParamBO.getPublishStatus());

        wrapper.orderByDesc(EsIndexRcMaterial::getModifyDt);
        PageInfo<EsIndexRcMaterial> searchPage = esIndexRcMaterialMapper
                .pageQuery(wrapper, searchParamBO.getPageIndex(), searchParamBO.getPageSize());
        Page<MaterialBO> page = new Page<MaterialBO>(searchParamBO.getPageIndex(), searchParamBO.getPageSize(),
                searchPage.getTotal());
        List<EsIndexRcMaterial> data = searchPage.getList();
        if (CollectionUtils.isNotEmpty(data)) {
            page.setRecords(data.stream().map(this::convert).collect(Collectors.toList()));
        } else {
            page.setRecords(Lists.newArrayList());
        }
        return page;
    }

    @Override
    public void batchDeleteEs(List<String> bizIds) {

       BulkRequest bulkRequest = new BulkRequest();

        bizIds.forEach(item->{

            UpdateRequest updateRequest = new UpdateRequest();
            updateRequest.index(MATERIAL_INDEX);
            updateRequest.id(item);
            Map<String,Object> hashMap = new HashMap<>();
            hashMap.put("is_deleted",Boolean.TRUE);
            updateRequest.doc(hashMap);
            bulkRequest.add(updateRequest);

        });

        //设置Es刷新策略
        bulkRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);

        try {

            restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);

        } catch (IOException e) {
            log.error("同步es数据失败！");
            log.error("bulkRequest = {}", JsonUtils.toJSON(bulkRequest));
        }

    }
    @Override
    public void publishOrCancel(String bizId ,Integer needToBePublistStatus) {

        UpdateRequest updateRequest = new UpdateRequest();
        updateRequest.index(MATERIAL_INDEX);
        updateRequest.id(bizId);
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("publish_status", Long.valueOf(needToBePublistStatus));
        updateRequest.doc(hashMap);
        //本次请求刷新策略为立即刷新
        updateRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        try {
            restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("同步es数据失败！");
            log.error("bulkRequest = {}", JsonUtils.toJSON(updateRequest));
        }
    }

    private MaterialBO convert(EsIndexRcMaterial source) {
        if (Objects.isNull(source)) {
            return null;
        }
        MaterialBO materialBO = new MaterialBO();
        materialBO.setMaterialId(source.getBizId().toString());
        materialBO.setTitle(source.getTitle());
        materialBO.setRemark(source.getRemark());
        materialBO.setContent(source.getContent());
        materialBO.setLogoImg(source.getLogoImg());
        materialBO.setCreateBy(source.getCreateBy());
        materialBO.setCreator(source.getCreatorName());
        materialBO.setModifyBy(source.getModifyBy());
        materialBO.setCreateTime(source.getCreateDt());
        String materialType = source.getMaterialType();
        materialBO.setMaterialType(Integer.valueOf(materialType));
        materialBO.setSize(source.getSize());
        materialBO.setPublishStatus(source.getPublishStatus());
        return materialBO;
    }

    private void paramsCheck(MaterialSearchBO searchParamBO) {
        Assert.notNull(searchParamBO, "入参不能为空");
        Assert.hasLength(searchParamBO.getTitle(), "请输入关键字");
    }

}
