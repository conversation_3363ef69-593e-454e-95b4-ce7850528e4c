package com.dl.dops.biz.common.forest.basicservice;

import com.dl.dops.biz.common.forest.basicservice.dto.BasicServiceMenuObjectDTO;
import com.dl.dops.biz.common.forest.basicservice.interceptor.BasicServiceInterceptor;
import com.dl.dops.biz.common.forest.basicservice.param.BasicServiceSysMenuParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.BasicServiceTenantMenuSaveParamDTO;
import com.dl.framework.common.model.ResultModel;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-15 15:42
 */
@BaseRequest(interceptor = BasicServiceInterceptor.class)
public interface MenuClient {

    /**
     * 查询可分配给租户的系统菜单列表
     *
     * @param param
     * @return
     */
    @Post(url = "/internal/menu/tenant/list")
    ResultModel<BasicServiceMenuObjectDTO> tenantMenuList(@JSONBody BasicServiceSysMenuParamDTO param);

    /**
     * 保存指定租户的菜单和功能项
     *
     * @param param
     * @return
     */
    @Post(url = "/internal/menu/tenant/save")
    ResultModel<Boolean> saveTenantMenu(@JSONBody BasicServiceTenantMenuSaveParamDTO param);

}
