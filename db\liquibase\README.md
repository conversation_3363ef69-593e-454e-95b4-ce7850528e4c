# dbscript 数据库脚本

## liquibase

官网
https://www.liquibase.org/  
文档
https://docs.liquibase.com/


1、介绍
============

#### 追踪、版本控制、部署数据库变更以及快速管理数据库模式变更

对表(table)、列(column)、数据类型(data type)、视图(view)、存储过程(stored procedures)、关系(relationships)、主键(primary key)、外键(foreign key)
等进行管理。


2、脚本命名规范
============
![命名](https://public-resource-1309667514.cos.ap-shanghai.myqcloud.com/inner/example1.png)

1、用版本计划名称命名一级脚本目录。 如v20220630

2、日期（yyyyMMdd）命名二级目录，表示当日脚本目录。 如20220607

3、脚本文件命名。4位时间+动作+表名+.xml 如 1800_create_table_test.xml 表示18：00 创建表test脚本


3、编写规范
============
详见exmaple目录