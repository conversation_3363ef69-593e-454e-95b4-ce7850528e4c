package com.dl.dops.resourcecenter.pack.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 20:42
 */
@Data
@ApiModel("服务包分支详情")
public class RcPackBranchDetailDTO extends RcPackBranchDTO implements Serializable {

    private static final long serialVersionUID = 9080098456369880320L;
    @ApiModelProperty("服务包元素列表")
    private List<RcPackElementDTO> packElementList;

}
