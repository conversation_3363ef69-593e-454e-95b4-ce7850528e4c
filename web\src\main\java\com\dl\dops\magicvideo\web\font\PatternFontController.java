package com.dl.dops.magicvideo.web.font;

import com.dl.dops.biz.magicvideo.manager.MagicVideoManager;
import com.dl.dops.magicvideo.font.dto.PatternFontDTO;
import com.dl.dops.magicvideo.font.param.PatternFontPageParam;
import com.dl.dops.magicvideo.font.param.PatternFontParam;
import com.dl.dops.magicvideo.web.font.convert.PatternFontConvert;
import com.dl.dops.magicvideo.web.font.vo.PatternFontVO;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.stream.Collectors;

@RestController
@RequestMapping("/dops/pattern/font")
public class PatternFontController extends AbstractController {

    @Autowired
    private MagicVideoManager magicVideoManager;

    @PostMapping("/add")
    public ResultModel<String> add(@RequestBody PatternFontParam param){
        param.setUserId(getUserId());
        return ResultModel.success(magicVideoManager.addPatternFont(param));
    }

    @PostMapping("/update")
    public ResultModel<Boolean> update(@RequestBody PatternFontParam param){
        param.setUserId(getUserId());
        return magicVideoManager.updatePatternFont(param);
    }

    @PostMapping("/page")
    public ResultPageModel<PatternFontVO> page(@RequestBody PatternFontPageParam param){
        ResultPageModel<PatternFontDTO> resultPageModel = magicVideoManager.pageQueryPatternFont(param);
        if(CollectionUtils.isEmpty(resultPageModel.getDataResult())){
            return new ResultPageModel<>();
        }
        return pageQueryModel(resultPageModel,resultPageModel.getDataResult().stream().map(
                PatternFontConvert::cnvPatternFontDTO2VO).collect(Collectors.toList()));
    }

    @PostMapping("/detail")
    public ResultModel<PatternFontVO> detail(@RequestBody PatternFontParam param){
        ResultModel<PatternFontDTO> resultModel = magicVideoManager.patternFontDetail(param);
        return ResultModel.success(PatternFontConvert.cnvPatternFontDTO2VO(resultModel.getDataResult()));
    }

    @PostMapping("/delete")
    public ResultModel<Boolean> delete(@RequestBody PatternFontParam param){
        return magicVideoManager.deletePatternFont(param);
    }
}
