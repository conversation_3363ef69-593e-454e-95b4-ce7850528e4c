package com.dl.dops.biz.resourcecenter.es.tag.po;

import cn.easyes.annotation.TableField;
import cn.easyes.annotation.TableName;
import cn.easyes.common.enums.FieldType;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-04-04 17:45
 */
@Data
@TableName(value = "dl_rc_tag")
public class EsIndexRcTag {

    @TableField(value = "tag_id")
    private String tagId;
    @TableField(value = "name")
    private String name;
    @TableField(value = "ordered", fieldType = FieldType.INTEGER)
    private Integer ordered;
    @TableField(value = "is_deleted", fieldType = FieldType.BOOLEAN)
    private Boolean isDeleted;
    @TableField(value = "tag_group_id", fieldType = FieldType.LONG)
    private Long tagGroupId;
    @TableField(value = "group_name")
    private String groupName;
    @TableField(value = "group_ordered", fieldType = FieldType.INTEGER)
    private Integer groupOrdered;
    @TableField(value = "group_type", fieldType = FieldType.INTEGER)
    private Integer groupType;
    @TableField(value = "modify_dt", fieldType = FieldType.DATE)
    private Date modifyDt;
}
