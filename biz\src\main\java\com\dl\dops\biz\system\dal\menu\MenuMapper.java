/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dl.dops.biz.system.dal.menu;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.dops.biz.common.annotation.BaseDao;
import com.dl.dops.biz.system.dal.menu.po.MenuFunctionTempPO;
import com.dl.dops.biz.system.dal.menu.po.MenuPO;

import java.util.List;

@BaseDao
@DS("dops")
public interface MenuMapper extends BaseMapper<MenuPO> {
    /**
     * 获取菜单功能列表
     *
     * @return
     */
    List<MenuFunctionTempPO> listMenuFunction();

}
