package com.dl.dops.resourcecenter.sop.enums;

import java.util.Objects;

/**
 * sop事件内容，内容类型枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-07 15:41
 */
public enum RcSopEventCntContentTypeEnum {

    MATERIAL(1, "素材"),
    POSTER(2, "海报");

    private Integer code;

    private String desc;

    RcSopEventCntContentTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RcSopEventCntContentTypeEnum parse(Integer value) {
        if (Objects.isNull(value)) {
            return null;
        }
        for (RcSopEventCntContentTypeEnum typeEnum : RcSopEventCntContentTypeEnum.values()) {
            if (typeEnum.code.equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }
}
