package com.dl.dops.resourcecenter.category.enums;

import java.util.Objects;

/**
 * @ClassName RcCategoryTypeEnum
 * @Description 资源中心分类Type枚举类
 * @Version 1.0
 **/
public enum RcCategoryTypeEnum {

    BIZ_TYPE_DEFAULT(0, "默认分类"),
    //资源中心的分类由本枚举单独维护，为防止与财营助攻手的分类类型弄混，1-17以及51请勿再使用。
    RS_BIZ_TYPE_POSTER(18, "资源中心-海报分类"),
    RS_BIZ_TYPE_ARTICLE(19, "资源中心-文章分类"),
    RS_BIZ_TYPE_WEBPAGE(20, "资源中心-网页分类"),
    RS_BIZ_TYPE_VIDEO(21, "资源中心-视频分类"),
    RS_BIZ_TYPE_FILE(22, "资源中心-文件分类"),
    RS_BIZ_TYPE_TEXT(23, "资源中心-文本分类"),
    RS_BIZ_TYPE_IMAGE(24, "资源中心-图片分类"),
    RS_ROOM_SOP(25, "资源中心-群SOP分类"),
    RS_CUSTOMER_SOP(26, "资源中心-个人SOP分类"),
    RS_ENTERPRISE_SCRIPT(27, "资源中心-企业话术分类"),
    RS_OPERATE_ASSISTENT_PACK(28, "资源中心-助你营服务包分类"),
    RS_VIDEO_ASSISTENT_PACK(29, "资源中心-快视频服务包分类"),;

    private Integer code;
    private String desc;

    RcCategoryTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RcCategoryTypeEnum parse(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (RcCategoryTypeEnum categoryTypeEnum : RcCategoryTypeEnum.values()) {
            if (code.equals(categoryTypeEnum.getCode())) {
                return categoryTypeEnum;
            }
        }
        return null;
    }
}
