<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.dops.biz.resourcecenter.dal.pack.PackChainPathMapper">
    <resultMap id="resultMap" type="com.dl.dops.biz.resourcecenter.dal.pack.po.PackChainPathPO">
        <id column="id" property="id"/>
        <result column="pack_id" property="packId"/>
        <result column="chain_path_id" property="chainPathId"/>
        <result column="name" property="name"/>
        <result column="sort" property="sort"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_dt" property="createDt"/>
        <result column="create_by" property="createBy"/>
        <result column="modify_dt" property="modifyDt"/>
        <result column="modify_by" property="modifyBy"/>
    </resultMap>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update pack_chain_path
            set
            <if test="item.name != null and item.name != '' ">
                name = #{item.name},
            </if>
            <if test="item.sort != null">
                sort = #{item.sort},
            </if>
            <if test="item.modifyBy != null">
                modify_by = #{item.modifyBy},
            </if>
            modify_dt = now()
            where chain_path_id = #{item.chainPathId} and is_deleted = 0
        </foreach>
    </update>

    <update id="batchLogicDeleteByChainPathIds">
        update pack_chain_path
        set is_deleted = 1,
        modify_by = #{operatorId},
        modify_dt = now()
        where chain_path_id in
        (
        <foreach collection="list" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </update>

</mapper>