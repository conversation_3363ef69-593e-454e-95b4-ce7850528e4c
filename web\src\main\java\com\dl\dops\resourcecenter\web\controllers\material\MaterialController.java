package com.dl.dops.resourcecenter.web.controllers.material;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.service.CosPathService;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.common.util.spider.Resp;
import com.dl.dops.biz.common.util.spider.SpiderUtil;
import com.dl.dops.biz.resourcecenter.dal.material.po.MaterialPO;
import com.dl.dops.biz.resourcecenter.manager.material.MaterialManager;
import com.dl.dops.biz.resourcecenter.manager.material.MaterialSearchManager;
import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialBO;
import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialSearchBO;
import com.dl.dops.resourcecenter.material.dto.RcArticleSpiderDTO;
import com.dl.dops.resourcecenter.material.dto.RcMaterialDTO;
import com.dl.dops.resourcecenter.material.enums.RcMaterialTypeEnum;
import com.dl.dops.resourcecenter.material.param.RcMaterialAddParam;
import com.dl.dops.resourcecenter.material.param.RcMaterialDeleteParam;
import com.dl.dops.resourcecenter.material.param.RcMaterialDetailParam;
import com.dl.dops.resourcecenter.material.param.RcMaterialListParam;
import com.dl.dops.resourcecenter.material.param.RcMaterialPageQueryParam;
import com.dl.dops.resourcecenter.material.param.RcMaterialUpdateParam;
import com.dl.dops.resourcecenter.material.param.RcMpArticleUrlParam;
import com.dl.dops.resourcecenter.material.param.RcUpdateMaterialCategoryParam;
import com.dl.dops.resourcecenter.tag.TagDefaultEnum;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.resourcecenter.web.controllers.material.convert.MaterialConvert;
import com.dl.dops.resourcecenter.web.controllers.material.process.ArticleSpiderProcess;
import com.dl.dops.resourcecenter.web.controllers.material.util.WordToHtmlUtil;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping({ "/resource/material", "/dops/resource/material" })
@Api("资源中心 - 素材")
public class MaterialController extends AbstractController {
    @Autowired
    private MaterialManager materialManager;
    @Autowired
    private ArticleSpiderProcess articleSpiderProcess;
    @Autowired
    private ExecutorService spiderExecutor;
    @Autowired
    private MaterialSearchManager materialSearchManager;
    @Autowired
    private WordToHtmlUtil wordToHtmlUtil;
    @Autowired
    private OperatorUtil operatorUtil;
    @Autowired
    private CosPathService cosPathService;

    @PostMapping("/add")
    @ApiOperation("添加素材")
    public ResultModel<String> add(@RequestBody @Validated RcMaterialAddParam param) {
        @NotNull Integer materialType = param.getMaterialType();
        cosPathService.preProcessObjectStorageUrl(param::getLogoImg, param::setLogoImg);
        if (RcMaterialTypeEnum.isMaterialContentStoredInObjectStorage(materialType)) {
            cosPathService.preProcessObjectStorageUrl(param::getContent, param::setContent);
        }

        //添加非微信公众号的素材
        if (!Objects.equals(param.getMaterialType(), RcMaterialTypeEnum.ARTICLE.getCode())) {
            Long materialId = materialManager.add(param, null);
            return ResultModel.success(materialId + "");
        }
        Long materialId;
        if (Objects.equals(Const.ZERO, param.getArticleType())) {//添加自建文章
            Elements wxArticleImg = SpiderUtil.getWxArticleImg(param.getContent());
            Integer publishMidState = null;
            if (Objects.nonNull(wxArticleImg) && wxArticleImg.size() > Const.ZERO) {
                publishMidState = Const.ZERO;
            }
            materialId = materialManager.add(param, publishMidState);
            try {
                spiderExecutor.execute(() -> articleSpiderProcess.crawlArticleCnt(materialId, param.getContent(),
                        param.getPublishStatus()));
            } catch (Exception e) {
                log.error("", e);
            }
        } else {//添加微信公众号的文章
            String result = SpiderUtil.checkUrl(param.getMpArticleSourceUrl());
            Assert.isTrue(StringUtils.isBlank(result), result);
            Resp<Document> resp = SpiderUtil.getDocument(param.getMpArticleSourceUrl());
            Assert.isTrue(resp.isSuccess(), resp.getMsg());
            materialId = materialManager.add(param, null);
            Long userId = operatorUtil.getOperator();
            String userName = operatorUtil.getOperatorName();
            try {
                spiderExecutor.execute(() -> {
                    operatorUtil.remove();
                    operatorUtil.init(userId, userName);
                    articleSpiderProcess.crawlArticleCnt(materialId, param.getMpArticleSourceUrl(), resp.getBody());
                });
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return ResultModel.success(materialId + "");
    }

    @PostMapping("/article/spider")
    @ApiOperation("公众号文章抓取")
    public ResultModel<RcArticleSpiderDTO> articleSpider(@RequestBody @Validated RcMpArticleUrlParam param) {
        Resp<JSONObject> resp = SpiderUtil.getArticle(param.getMpArticleSourceUrl());
        Assert.isTrue(resp.isSuccess(), resp.getMsg());
        JSONObject body = resp.getBody();
        RcArticleSpiderDTO article = new RcArticleSpiderDTO();
        article.setSummary(body.getString(SpiderUtil.KEY_DESCRIPTION));
        article.setCoverImg(body.getString(SpiderUtil.KEY_COVER_URL));
        article.setTitle(body.getString(SpiderUtil.KEY_TITLE));
        article.setCoverImg(articleSpiderProcess.transform(param.getMpArticleSourceUrl(), article.getCoverImg()));
        return ResultModel.success(article);
    }

    @PostMapping("/edit")
    @ApiOperation("编辑素材")
    public ResultModel<Void> edit(@RequestBody @Validated RcMaterialUpdateParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getMaterialId()), "错误的素材id");
        LambdaQueryWrapper<MaterialPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MaterialPO::getBizId, Long.valueOf(param.getMaterialId()));
        MaterialPO exist = materialManager.getOne(queryWrapper);
        Assert.notNull(exist, "素材不存在");

        @NotNull Integer materialType = exist.getMaterialType();
        // 20230313-COS迁移调整，预处理素材输入的logo图像和内容，将COS的URL转为纯路径
        if (StringUtils.isNotEmpty(param.getLogoImg())) {
            // logo图片有传值，则检查图片地址，解析图片路径
            cosPathService.preProcessObjectStorageUrl(param::getLogoImg, param::setLogoImg);
        }
        if (RcMaterialTypeEnum.isMaterialContentStoredInObjectStorage(materialType)) {
            // 如果素材内容是存储到COS的，则检查内容地址，解析路径
            cosPathService.preProcessObjectStorageUrl(param::getContent, param::setContent);
        }
        //处理非文章类的素材
        if (!Objects.equals(exist.getMaterialType(), RcMaterialTypeEnum.ARTICLE.getCode())) {
            materialManager.edit(param, null);
            return ResultModel.success(null);
        }
        Long userId = operatorUtil.getOperator();
        String userName = operatorUtil.getOperatorName();
        if (Objects.equals(Const.ZERO, exist.getArticleType())) {
            //非微信公众号的文章
            Elements wxArticleImg = SpiderUtil.getWxArticleImg(param.getContent());
            Integer publishMidState = null;
            if (Objects.nonNull(wxArticleImg) && wxArticleImg.size() > Const.ZERO) {
                publishMidState = Const.ZERO;
            }
            materialManager.edit(param, publishMidState);
            try {
                spiderExecutor.execute(() -> {
                    operatorUtil.remove();
                    operatorUtil.init(userId, userName);
                    articleSpiderProcess
                            .crawlArticleCnt(exist.getBizId(), param.getContent(), exist.getPublishStatus());
                });
            } catch (Exception e) {
                log.error("", e);
            }
        } else { //文章 - 公众号
            materialManager.edit(param, null);
            Resp<Document> resp = SpiderUtil.getDocument(param.getMpArticleSourceUrl());
            Assert.isTrue(resp.isSuccess(), resp.getMsg());
            try {
                spiderExecutor.execute(() -> {
                    operatorUtil.remove();
                    operatorUtil.init(userId, userName);
                    articleSpiderProcess
                            .crawlArticleCnt(exist.getBizId(), param.getMpArticleSourceUrl(), resp.getBody());
                });
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return ResultModel.success(null);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询素材")
    public ResultPageModel<RcMaterialDTO> page(@RequestBody @Validated RcMaterialPageQueryParam bo) {
        //没传标题且查询的是非无标签的，走db查询。
        if (StringUtils.isBlank(bo.getTitle()) && !(TagDefaultEnum.UNTAG.getCode().equals(bo.getTagId()))) {
            return pageQueryModel(materialManager.pageQuery(bo),
                    cosPathService.postProcessObjectStorageMaterialDTOFunction(MaterialConvert::cnvCntMaterialDTO2VO));
        }
        //走es
        MaterialSearchBO searchParamBO = new MaterialSearchBO();
        searchParamBO.setTitle(bo.getTitle());
        searchParamBO.setMaterialTypes(Arrays.asList(bo.getMaterialType()));
        searchParamBO.setPageIndex(bo.getPageIndex());
        searchParamBO.setPageSize(bo.getPageSize());
        searchParamBO.setPublishStatus(bo.getPublishStatus());
        searchParamBO.setTagId(bo.getTagId());
        searchParamBO.setCategoryId(bo.getCategoryId());
        return pageQueryModel(materialSearchManager.search(searchParamBO),
                cosPathService.postProcessObjectStorageMaterialDTOFunction(MaterialConvert::cnvCntMaterialDTO2VO));
    }

    @PostMapping("/detail")
    @ApiOperation("查询素材详情")
    public ResultModel<RcMaterialDTO> detail(@RequestBody @Validated RcMaterialDetailParam param) {
        return ResultModel.success(cosPathService.postProcessObjectStorageMaterialDTO(
                MaterialConvert.cnvCntMaterialDTO2VO(materialManager.detail(param.getMaterialId()))));
    }

    @PostMapping("/delete")
    @ApiOperation("删除素材")
    public ResultModel<Void> delete(@RequestBody @Validated RcMaterialDeleteParam bo) {
        materialManager.batchDelete(bo);
        return ResultModel.success(null);
    }

    @Deprecated
    @PostMapping("/updatecategory")
    @ApiOperation("批量更新分类")
    public ResultModel<Void> updateCategory(@RequestBody @Validated RcUpdateMaterialCategoryParam bo) {
        materialManager.batchUpdateCategory(bo);
        return ResultModel.success(null);
    }

    @PostMapping("/word2html")
    @ApiOperation("word转html")
    public ResultModel<String> upload(MultipartFile file) {
        String docType = file.getOriginalFilename();
        if (docType.endsWith(".doc")) {
            return ResultModel.success(wordToHtmlUtil.docToHtmlText(file));
        } else if (docType.endsWith(".docx")) {
            return ResultModel.success(wordToHtmlUtil.docxToHtmlText(file));
        }
        throw BusinessServiceException.getInstance("文件格式错误，请上传.doc或.docx文件");
    }

    @PostMapping("/publishorcancel")
    @ApiOperation("素材发布或取消发布")
    public ResultModel publishOrCancel(@RequestBody @Validated RcMaterialDetailParam param) {
        materialManager.publishOrCancel(Long.valueOf(param.getMaterialId()));
        return ResultModel.success(null);
    }

    @PostMapping("/list")
    @ApiOperation("查询资源中心素材列表")
    public ResultModel<List<RcMaterialDTO>> list(@RequestBody RcMaterialListParam param) {
        if (CollectionUtils.isEmpty(param.getMaterialIdList())) {
            return ResultModel.success(null);
        }
        List<MaterialBO> materialBOList = materialManager
                .batchDetail(param.getMaterialIdList().stream().map(Long::valueOf).collect(Collectors.toList()));
        return ResultModel.success(materialBOList.stream()
                .map(cosPathService.postProcessObjectStorageMaterialDTOFunction(MaterialConvert::cnvCntMaterialDTO2VO))
                .collect(Collectors.toList()));
    }
}
