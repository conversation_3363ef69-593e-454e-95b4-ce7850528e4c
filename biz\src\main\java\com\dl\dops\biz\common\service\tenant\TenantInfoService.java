package com.dl.dops.biz.common.service.tenant;


import com.dl.dops.biz.common.forest.basicservice.dto.TenantInfoDTO;
import com.dl.dops.biz.common.forest.basicservice.dto.TenantListDTO;
import com.dl.dops.biz.common.forest.basicservice.param.AddTenantParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.DelTenantParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.TenantParam;
import com.dl.dops.biz.common.forest.basicservice.param.UpdateTenantParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.UpdateTenantStatusParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.UpdateTenantTrialParamDTO;
import com.dl.framework.common.model.ResultPageModel;

import java.util.List;

/**
 * @ClassName ISysTenantInfoService
 * @Description
 * <AUTHOR>
 * @Date 2022/4/9 11:06
 * @Version 1.0
 **/
public interface TenantInfoService {

    /**
     * 查询租户信息
     *
     * @param tenantCode
     * @return
     */
    TenantInfoDTO info(String tenantCode);

    /**
     * 查询所有租户
     *
     * @return
     */
    List<TenantInfoDTO> listAll();

    /**
     * 分页获取租户列表
     *
     * @param p
     * @return
     */
    ResultPageModel<TenantListDTO> pageTenant(TenantParam p);

    String add(AddTenantParamDTO param);

    void update(UpdateTenantParamDTO param);

    void updateStatus(UpdateTenantStatusParamDTO param);

    void del(DelTenantParamDTO param);

    /**
     * 更新租户是否试用
     *
     * @param param
     */
    void updateTrial(UpdateTenantTrialParamDTO param);

}
