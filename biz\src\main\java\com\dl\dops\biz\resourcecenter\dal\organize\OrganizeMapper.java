package com.dl.dops.biz.resourcecenter.dal.organize;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.dl.dops.biz.common.annotation.BaseDao;
import com.dl.dops.biz.resourcecenter.dal.organize.po.OrganizePO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Entity com.dl.dops.biz.resourcecenter.dal.organize.po.OrganizePO
 */
@BaseDao
@DS("resourcecenter")
public interface OrganizeMapper extends BaseMapper<OrganizePO> {

}




