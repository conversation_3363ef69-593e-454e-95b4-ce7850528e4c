package com.dl.dops.system.web.virtualman.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class VmAddParam {

    @NotBlank(message = "数字人名称必填")
    @ApiModelProperty(value = "数字人名称", required = true)
    String name;

    @NotBlank(message = "数字人头像必填")
    @ApiModelProperty(value = "数字人头像地址", required = true)
    String headImg;

    @NotNull(message = "数字人厂商必填")
    @ApiModelProperty(value = "数字人来源渠道", required = true)
    Integer channel;

    @NotBlank(message = "数字人来源ID必填")
    @ApiModelProperty(value = "数字人来源编码", required = true)
    String vmCode;

    @ApiModelProperty("数字人性别：1 男；2 女")
    Integer gender;

    @ApiModelProperty("数字人来源有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date expireDate;

    @ApiModelProperty("数字人默认语速值")
    Float defaultSpeed;

    @ApiModelProperty(value = "仿真人外部声音代码")
    private String vmVoiceKey;

    @ApiModelProperty(value = "关联合克隆音的内部声音代码")
    private Long voiceBizId;
}
