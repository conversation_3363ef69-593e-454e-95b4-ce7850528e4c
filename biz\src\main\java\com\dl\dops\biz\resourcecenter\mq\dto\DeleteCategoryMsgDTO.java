package com.dl.dops.biz.resourcecenter.mq.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 分类删除的消息传输对象
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-05 17:21
 */
@Data
public class DeleteCategoryMsgDTO implements Serializable {
    private static final long serialVersionUID = -4305364844851658202L;

    /**
     * 分类bizid
     */
    private Long categoryBizId;

    /**
     * 分类类型
     *
     * @see:com.dl.wealthcenter.biz.manager.sys.category.enums.CategoryTypeEnum
     */
    private Integer categoryType;

    /**
     * 分类级别
     *
     * @see:com.dl.wealthcenter.biz.manager.sys.category.enums.CategoryLevelEnum
     */
    private Integer categoryLevel;

    /**
     * 上级分类bizId（if exist）
     */
    private Long parentBizId;

    /**
     * 子分类bizId列表（if exist）
     */
    private List<Long> subCategoryBizIdList;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 操作人id
     */
    private Long operatorUserId;
}
