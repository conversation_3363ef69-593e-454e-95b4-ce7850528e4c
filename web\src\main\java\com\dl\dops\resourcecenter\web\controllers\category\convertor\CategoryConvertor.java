package com.dl.dops.resourcecenter.web.controllers.category.convertor;

import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryBO;
import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryDetailBO;
import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryParamBO;
import com.dl.dops.resourcecenter.category.dto.RcCategoryDTO;
import com.dl.dops.resourcecenter.category.dto.RcCategoryDetailDTO;
import com.dl.dops.resourcecenter.category.param.RcCategoryParam;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName CategoryConvertor
 * @Description
 * <AUTHOR>
 * @Date 2022/4/12 16:11
 * @Version 1.0
 **/
public class CategoryConvertor {

    public static RcCategoryDTO convertBO2DTO(CategoryBO cate) {
        if (Objects.isNull(cate)) {
            return null;
        }
        RcCategoryDTO rcCategoryDTO = new RcCategoryDTO();
        rcCategoryDTO.setId(cate.getId());
        rcCategoryDTO.setName(cate.getName());
        rcCategoryDTO.setCategoryType(cate.getCategoryType());
        rcCategoryDTO.setCategoryLevel(cate.getCategoryLevel());
        rcCategoryDTO.setParentId(cate.getParentId());
        rcCategoryDTO.setLink(cate.getLink());
        return rcCategoryDTO;
    }

    public static RcCategoryDetailDTO convertDetailBO2DTO(CategoryDetailBO cate) {
        if (Objects.isNull(cate)) {
            return null;
        }
        RcCategoryDetailDTO rcCategoryDTO = new RcCategoryDetailDTO();
        rcCategoryDTO.setId(cate.getId());
        rcCategoryDTO.setName(cate.getName());
        rcCategoryDTO.setCategoryType(cate.getCategoryType());
        rcCategoryDTO.setCategoryLevel(cate.getCategoryLevel());
        List<CategoryDetailBO> childList = cate.getChildList();
        if (CollectionUtils.isNotEmpty(childList)) {
            rcCategoryDTO.setChildList(
                    childList.stream().map(x -> convertDetailBO2DTO(x)).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        rcCategoryDTO.setParentId(cate.getParentId());
        rcCategoryDTO.setLink(cate.getLink());
        return rcCategoryDTO;
    }

    public static CategoryParamBO convertParam2DTO(RcCategoryParam param) {
        if (Objects.isNull(param)) {
            return null;
        }

        CategoryParamBO categoryParamBO = new CategoryParamBO();
        categoryParamBO.setId(param.getId());
        categoryParamBO.setName(param.getName());
        categoryParamBO.setCategoryType(param.getCategoryType());
        categoryParamBO.setCategoryLevel(param.getCategoryLevel());
        categoryParamBO.setParentId(param.getParentId());
        return categoryParamBO;
    }
}
