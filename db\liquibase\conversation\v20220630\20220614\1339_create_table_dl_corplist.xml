<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="20220613134000" author="wangtc">

        <preConditions onFail="CONTINUE" onError="HALT">
            <not>
                <tableExists tableName="dl_corplist"/>
            </not>
        </preConditions>
        <comment>建表test</comment>
        <!-- 建表 -->
        <createTable tableName="dl_corplist" remarks="会话存档配置信息表">
            <column name="id" type="${bigInt.type}" remarks="唯一id" autoIncrement="true">
                <constraints nullable="false"
                             unique="true"
                             primaryKey="true"/>
            </column>
            <column name="corpId" type="${string.type}(200)" remarks="企微企业id"
                    defaultValue="">
                <constraints nullable="false"
                             unique="false"
                             primaryKey="false"/>
            </column>
            <column name="secret" type="${string.type}(200) " remarks="企微会话存档secret">
                <constraints nullable="false"
                             unique="false"
                             primaryKey="false"/>
            </column>
            <column name="corpname" type="${string.type}(200)" remarks="企业名称"
                    defaultValue="">
                <constraints nullable="false"
                             unique="false"
                             primaryKey="false"/>
            </column>
            <column name="prikey" type="${text.type}" remarks="会话存档版本私钥"/>
            <column name="limits" type="${integer.type}" remarks="每次拉取数量"/>
            <column name="timeout" type="${integer.type}" remarks="超时时间"/>
            <column name="status" type="${tinyint.type}" remarks="启用状态"/>
            <column name="seq" type="${bigInt.type}" remarks="唯一id"/>
        </createTable>
        <rollback>
            <dropTable tableName="dl_corplist"/>
        </rollback>
        <!-- mysql innodb -->
        <modifySql dbms="mysql">
            <append value=" engine innodb"/>
        </modifySql>

    </changeSet>

</databaseChangeLog>