package com.dl.dops.biz.resourcecenter.manager.material;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.resourcecenter.dal.material.po.MaterialPO;
import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialBO;
import com.dl.dops.resourcecenter.material.param.*;

import java.util.List;

public interface MaterialManager extends IService<MaterialPO>, CommonService {

    /**
     * 新增素材
     * @param param
     * @param publishMidState 发布中间态，特殊情况有值
     * @return
     */
    Long add(RcMaterialAddParam param, Integer publishMidState);

    /**
     * 编辑素材
     *
     * @param param
     * @param publishMidState 发布中间态，特殊情况有值
     * @return
     */
    Boolean edit(RcMaterialUpdateParam param, Integer publishMidState);

    /**
     * 素材详情
     *
     * @return
     */
    MaterialBO detail(String materialId);

    /**
     * 查询素材
     *
     * @param bo
     * @return
     */
    IPage<MaterialBO> pageQuery(RcMaterialPageQueryParam bo);

    /**
     * 删除素材
     *
     * @param bo
     * @return
     */
    Boolean batchDelete(RcMaterialDeleteParam bo);

    /**
     * 批量修改分组
     *
     * @param bo
     * @return
     */
    Boolean batchUpdateCategory(RcUpdateMaterialCategoryParam bo);

    /**
     * 批量查询素材详情
     *
     * @param materialIds
     * @return
     */
    List<MaterialBO> batchDetail(List<Long> materialIds);

    /**
     * 素材模板发布或取消发布
     *
     * @param materialId
     * @return
     */
    Boolean publishOrCancel(Long materialId);
}
