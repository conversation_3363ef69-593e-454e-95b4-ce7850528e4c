package com.dl.dops.resourcecenter.material.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("删除素材")
public class RcMaterialDeleteParam implements Serializable {

    @ApiModelProperty("素材id列表")
    @Size(min = 1, max = 20)
    private List<String> materialIds;

}
