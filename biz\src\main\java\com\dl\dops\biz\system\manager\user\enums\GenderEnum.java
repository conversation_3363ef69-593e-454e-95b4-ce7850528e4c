package com.dl.dops.biz.system.manager.user.enums;

import java.util.Objects;

/**
 * @ClassName GenderEnum
 * @Description
 * <AUTHOR>
 * @Date 2022/4/14 11:03
 * @Version 1.0
 **/
public enum GenderEnum {

    UNKNOWN(0, "未知"),
    MALE(1, "男"),
    FEMALE(2, "女");

    private Integer code;
    private String desc;

    GenderEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static GenderEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return UNKNOWN;
        }

        for (GenderEnum gender : GenderEnum.values()) {
            if (gender.getCode() == code) {
                return gender;
            }
        }

        return UNKNOWN;
    }
}
