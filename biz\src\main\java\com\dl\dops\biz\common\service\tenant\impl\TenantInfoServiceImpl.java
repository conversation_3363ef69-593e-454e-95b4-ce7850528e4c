package com.dl.dops.biz.common.service.tenant.impl;

import cn.hutool.json.JSONUtil;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.forest.basicservice.TenantClient;
import com.dl.dops.biz.common.forest.basicservice.dto.TenantInfoDTO;
import com.dl.dops.biz.common.forest.basicservice.dto.TenantListDTO;
import com.dl.dops.biz.common.forest.basicservice.param.AddTenantParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.DelTenantParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.TenantInfoQueryParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.TenantParam;
import com.dl.dops.biz.common.forest.basicservice.param.UpdateTenantParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.UpdateTenantStatusParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.UpdateTenantTrialParamDTO;
import com.dl.dops.biz.common.forest.magicvideo.MagicVideoClient;
import com.dl.dops.biz.common.forest.magicvideo.param.CreateAccountParam;
import com.dl.dops.biz.common.service.tenant.TenantInfoService;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName SysTenantInfoServiceImp
 * @Description
 * <AUTHOR>
 * @Date 2022/4/11 9:46
 * @Version 1.0
 **/
@Service
public class TenantInfoServiceImpl implements TenantInfoService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantInfoServiceImpl.class);

    @Resource
    private TenantClient tenantClient;

    @Resource
    private OperatorUtil operatorUtil;

    @Resource
    private MagicVideoClient magicVideoClient;

    private static final Long DEFAULT_BALANCE = 20L;

    @Override
    public TenantInfoDTO info(String tenantCode) {
        Assert.isTrue(StringUtils.isNotBlank(tenantCode), "租户编号不能为空");
        TenantInfoQueryParamDTO param = new TenantInfoQueryParamDTO();
        param.setTenantCode(tenantCode);
        ResultModel<TenantInfoDTO> resultModel = tenantClient.info(param);
        if (!Const.ZERO_STR.equals(resultModel.getCode()) || !Const.ZERO_STR.equals(resultModel.getFlag())) {
            LOGGER.error("查询租户信息失败!,tenantCode:{},resultModel:{}", tenantCode, JSONUtil.toJsonStr(resultModel));
            return null;
        }
        return resultModel.getDataResult();
    }

    @Override
    public List<TenantInfoDTO> listAll() {
        ResultModel<List<TenantInfoDTO>> resultModel = tenantClient.listAll();
        if (!Const.ZERO_STR.equals(resultModel.getCode()) || !Const.ZERO_STR.equals(resultModel.getFlag())) {
            LOGGER.error("查询所有租户信息失败!,resultModel:{}", JSONUtil.toJsonStr(resultModel));
            return Collections.emptyList();
        }
        return resultModel.getDataResult();
    }

    @Override
    public ResultPageModel<TenantListDTO> pageTenant(TenantParam p) {
        ResultPageModel<TenantListDTO> resultModel = tenantClient.pageTenant(p);
        if (!resultModel.isSuccess()) {
            LOGGER.error("查询租户列表失败!,resultModel:{}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询租户列表失败");
        }
        return resultModel;
    }

    @Override
    public String add(AddTenantParamDTO param) {
        param.setOperatorId(operatorUtil.getOperator());
        ResultModel<String> resultModel = tenantClient.addTenant(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("添加租户失败!,resultModel:{}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("添加租户失败," + resultModel.getMessage());
        }
        return resultModel.getDataResult();
    }

    @Override
    public void update(UpdateTenantParamDTO param) {
        param.setOperatorId(operatorUtil.getOperator());
        ResultModel resultModel = tenantClient.updateTenant(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("修改租户失败!,resultModel:{}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("修改租户失败," + resultModel.getMessage());
        }
    }

    @Override
    public void updateStatus(UpdateTenantStatusParamDTO param) {
        param.setOperatorId(operatorUtil.getOperator());
        ResultModel resultModel = tenantClient.updateStatus(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("修改租户状态失败!,resultModel:{}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("修改租户状态失败," + resultModel.getMessage());
        }
    }

    @Override
    public void del(DelTenantParamDTO param) {
        param.setOperatorId(operatorUtil.getOperator());
        ResultModel resultModel = tenantClient.delTenant(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("删除租户状态失败!,resultModel:{}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("删除租户状态失败," + resultModel.getMessage());
        }
    }

    @Override
    public void updateTrial(UpdateTenantTrialParamDTO param) {
        //1.调用basic-service 更新租户是否试用
        param.setOperatorId(operatorUtil.getOperator());
        ResultModel resultModel = tenantClient.updateTrial(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("修改租户是否试用失败!,resultModel:{}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("修改租户是否试用失败," + resultModel.getMessage());
        }
        //非试用，直接返回
        if (!Const.ONE.equals(param.getTrialStatus())) {
            return;
        }

        //2.调用magic-video 创建试用账户
        CreateAccountParam createAccountParam = new CreateAccountParam();
        createAccountParam.setTenantCode(param.getTenantCode());
        createAccountParam.setInitBalance(DEFAULT_BALANCE);
        ResultModel createAccountResult = magicVideoClient.createTrialAccount(createAccountParam);
        if (!createAccountResult.isSuccess()) {
            LOGGER.error("创建租户试用账户失败!,createAccountResult:{}", JSONUtil.toJsonStr(createAccountResult));
            throw BusinessServiceException.getInstance("创建租户试用账户失败," + createAccountResult.getMessage());
        }
    }
}
