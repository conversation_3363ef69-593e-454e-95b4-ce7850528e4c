package com.dl.dops.resourcecenter.web.controllers.productinstitute;

import com.dl.dops.biz.common.annotation.NotLogin;
import com.dl.dops.biz.resourcecenter.dal.productinstitute.po.ProductIssueInstitutePO;
import com.dl.dops.biz.resourcecenter.manager.productinstitute.ProductIssueInstituteManager;
import com.dl.dops.resourcecenter.productinstitute.dto.ProductIssueInstituteDTO;
import com.dl.dops.resourcecenter.productinstitute.param.ListProductIssueInstituteDetailsParam;
import com.dl.dops.resourcecenter.productinstitute.param.ListProductIssueInstituteParam;
import com.dl.dops.resourcecenter.script.dto.RcScriptDTO;
import com.dl.dops.resourcecenter.script.dto.RcScriptUploadResultDTO;
import com.dl.dops.resourcecenter.script.param.RcScriptAddParam;
import com.dl.dops.resourcecenter.script.param.RcScriptEditParam;
import com.dl.dops.resourcecenter.script.param.RcScriptPageQueryParam;
import com.dl.dops.resourcecenter.web.controllers.script.ScriptProcess;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping({"/dops/resource/productissueinstitute"})
@Api("资源中心 - 产品机构")
public class ProductIssueInstituteController {

    @Resource
    private ProductIssueInstituteManager productInstituteManager;

    @PostMapping({"/list", "/tenantauth/list"})
    @ApiOperation("查询产品机构列表")
    public ResultModel<List<ProductIssueInstituteDTO>> listProductIssueInstitute(@RequestBody @Validated ListProductIssueInstituteParam param) {
        String nameSearchPattern = param.getNamePattern();
        boolean hasNameSearchPattern = StringUtils.isNotBlank(nameSearchPattern);
        List<ProductIssueInstituteDTO> lstRes = productInstituteManager.lambdaQuery()
                .like(hasNameSearchPattern, ProductIssueInstitutePO::getFullName, nameSearchPattern)
                .or()
                .like(hasNameSearchPattern, ProductIssueInstitutePO::getAbbrName, nameSearchPattern)
                .list()
                .stream()
                .map(this::convertFromPO)
                .collect(Collectors.toList());
        return ResultModel.success(lstRes);
    }

    private ProductIssueInstituteDTO convertFromPO(ProductIssueInstitutePO po) {
        if (po == null) {
            return null;
        }

        ProductIssueInstituteDTO t = new ProductIssueInstituteDTO();
        BeanUtils.copyProperties(po, t);
        t.setInstituteId(po.getInstituteId().toString());
        t.setId(po.getId().toString());
        return t;
    }

    @GetMapping({"/info", "/tenantauth/info"})
    @ApiOperation("查询单个产品机构")
    public ResultModel<ProductIssueInstituteDTO> info(@RequestParam("instituteId") String instituteId) {
        ProductIssueInstitutePO institutePO = productInstituteManager.lambdaQuery()
                .eq(ProductIssueInstitutePO::getInstituteId, Long.valueOf(instituteId))
                .one();
        return ResultModel.success(convertFromPO(institutePO));
    }

    @PostMapping({"/listdetails", "/tenantauth/listdetails"})
    @ApiOperation("查询多个产品机构")
    public ResultModel<List<ProductIssueInstituteDTO>> listDetails(@RequestBody @Validated ListProductIssueInstituteDetailsParam param) {
        List<ProductIssueInstituteDTO> lstResult = productInstituteManager.lambdaQuery()
                .in(ProductIssueInstitutePO::getInstituteId, param.getInstituteIds())
                .list()
                .stream()
                .map(this::convertFromPO)
                .collect(Collectors.toList());
        return ResultModel.success(lstResult);
    }

    @PostMapping({"/noauth/list"})
    @ApiOperation("查询产品机构列表-非登录")
    @NotLogin
    public ResultModel<List<ProductIssueInstituteDTO>> listProductIssueInstituteNotLogin(@RequestBody @Validated ListProductIssueInstituteParam param) {
        return listProductIssueInstitute(param);
    }

    @GetMapping({"/noauth/info"})
    @ApiOperation("查询单个产品机构-非登录")
    @NotLogin
    public ResultModel<ProductIssueInstituteDTO> infoNotLogin(@RequestParam("instituteId") String instituteId) {
        return info(instituteId);
    }

    @PostMapping({"/noauth/listdetails"})
    @ApiOperation("查询多个产品机构-非登录")
    @NotLogin
    public ResultModel<List<ProductIssueInstituteDTO>> listDetailsNotLogin(@RequestBody @Validated ListProductIssueInstituteDetailsParam param) {
        return listDetails(param);
    }
    
}
