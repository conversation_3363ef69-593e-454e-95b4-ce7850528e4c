package com.dl.dops.biz.resourcecenter.manager.pack.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 20:19
 */
@Data
public class PackElementBO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 服务包id
     */
    private Long packId;

    /**
     * 元素id
     */
    private Long elementId;

    /**
     * 链路id
     */
    private Long chainPathId;

    /**
     * 分支id
     */
    private Long branchId;

    /**
     * 元素类型
     *
     * @see：PackElementTypeEnum
     */
    private Integer type;

    /**
     * 排序
     */
    private int sort;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 附件
     */
    private String attachments;

    /**
     * 附件列表
     */
    private List<PackElementAttachmentBO> attachmentList;

    /**
     * 扩展数据
     */
    private String extData;

    private String remark;
}
