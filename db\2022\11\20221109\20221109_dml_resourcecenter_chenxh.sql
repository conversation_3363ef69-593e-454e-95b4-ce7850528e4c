--测试环境数据迁移
--类目
insert into dl_resource.category (biz_id, category_type, name, category_level, parent_id, link, create_dt, create_by,
                                  modify_dt, modify_by, is_deleted)
select sc.biz_id,
       sc.category_type,
       sc.name,
       sc.category_level,
       sc.parent_id,
       sc.link,
       sc.create_dt,
       sc.create_by,
       sc.modify_dt,
       sc.modify_by,
       sc.is_deleted
from dl_test.sys_category sc
where sc.tenant_code = 'DL'
  and sc.category_type in (18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29);


--素材
insert into dl_resource.material (biz_id, title, remark, logo_img, category_ids, material_type, article_type,
                                  source_url, content, size, publish_status, create_dt, create_by, modify_dt, modify_by,
                                  create_from)
select cm.biz_id,
       cm.title,
       cm.remark,
       cm.logo_img,
       cm.category_ids,
       cm.material_type,
       cm.article_type,
       cm.source_url,
       cm.content,
       cm.size,
       cm.publish_status,
       cm.create_dt,
       cm.create_by,
       cm.modify_dt,
       cm.modify_by,
       cm.create_from
from dl_test.cnt_material cm
where cm.tenant_code = 'DL'
  and cm.is_tpl = 1;


--话术
insert into dl_resource.script (script_id, question, content, category1, category2, publish_status, create_dt,
                                create_by, modify_dt, modify_by, is_deleted)
select cs.script_id,
       cs.question,
       cs.content,
       cs.category1,
       cs.category2,
       cs.publish_status,
       cs.create_dt,
       cs.create_by,
       cs.modify_dt,
       cs.modify_by,
       cs.is_deleted
from dl_test.cnt_script cs
where cs.tenant_code = 'DL'
  and cs.is_tpl = 1;

insert into dl_resource.script_material (script_id, material_id, is_deleted, create_dt, create_by, modify_dt, modify_by)
SELECT csm.script_id, csm.material_id, csm.is_deleted, csm.create_dt, csm.create_by, csm.modify_dt, csm.modify_by
from dl_test.cnt_script_material csm
where csm.script_id in (
    SELECT cs.script_id from dl_test.cnt_script cs where cs.tenant_code = 'DL' and cs.is_tpl = 1);


--sop
insert into dl_resource.sop (sop_id, name, remark, category1, category2, status, sop_type, create_dt, create_by,
                             modify_dt, modify_by, creator_name, is_deleted, delete_dt)
SELECT ss.sop_id,
       ss.name,
       ss.remark,
       ss.category1,
       ss.category2,
       ss.status,
       ss.sop_type,
       ss.create_dt,
       ss.create_by,
       ss.modify_dt,
       ss.modify_by,
       ss.creator_name,
       ss.is_deleted,
       ss.delete_dt
from dl_test.sf_sop ss
where ss.tenant_code = 'DL'
  and ss.is_tpl = 1;

insert into dl_resource.sop_event (event_id, sop_id, name, remark, reach_type, content, is_deleted, rule_type,
                                   rule_content, create_dt, create_by, modify_dt, modify_by, validity_period)
SELECT sse.event_id,
       sse.sop_id,
       sse.name,
       sse.remark,
       sse.reach_type,
       sse.content,
       sse.is_deleted,
       sse.rule_type,
       sse.rule_content,
       sse.create_dt,
       sse.create_by,
       sse.modify_dt,
       sse.modify_by,
       sse.validity_period
from dl_test.sf_sop_event sse
where sse.sop_id in (
    SELECT ss.sop_id from dl_test.sf_sop ss where ss.tenant_code = 'DL' and ss.is_tpl = 1);

INSERT INTO dl_resource.sop_event_cnt (event_id, cnt_type, cnt_id, is_deleted, create_dt, create_by, modify_dt,
                                       modify_by)
SELECT ssec.event_id,
       ssec.cnt_type,
       ssec.cnt_id,
       ssec.is_deleted,
       ssec.create_dt,
       ssec.create_by,
       ssec.modify_dt,
       ssec.modify_by
FROM dl_test.sf_sop_event_cnt ssec
WHERE ssec.event_id IN (
    SELECT sse.event_id
    FROM dl_test.sf_sop_event sse
    WHERE sse.sop_id IN (SELECT ss.sop_id FROM dl_test.sf_sop ss WHERE ss.tenant_code = 'DL' AND ss.is_tpl = 1)
);


--服务包

INSERT INTO dl_resource.pack (pack_id, title, category1, category2, scene_overview, domain, detailed_description,
                              suggest, STATUS, scene, is_deleted, create_dt, create_by, modify_dt, modify_by)
SELECT sp.pack_id,
       sp.title,
       sp.category1,
       sp.category2,
       sp.scene_overview,
       sp.domain,
       sp.detailed_description,
       sp.suggest,
       sp.STATUS,
       sp.scene,
       sp.is_deleted,
       sp.create_dt,
       sp.create_by,
       sp.modify_dt,
       sp.modify_by
FROM dl_test.sf_pack sp
WHERE sp.tenant_code = 'DL'
  AND sp.is_tpl = 1;


INSERT INTO dl_resource.pack_chain_path (pack_id, chain_path_id, NAME, sort, is_deleted, create_dt, create_by,
                                         modify_dt, modify_by)
SELECT spcp.pack_id,
       spcp.chain_path_id,
       spcp.NAME,
       spcp.sort,
       spcp.is_deleted,
       spcp.create_dt,
       spcp.create_by,
       spcp.modify_dt,
       spcp.modify_by
FROM dl_test.sf_pack_chain_path spcp
WHERE spcp.pack_id IN (SELECT sp.pack_id FROM dl_test.sf_pack sp WHERE sp.tenant_code = 'DL' AND sp.is_tpl = 1);


insert
into dl_resource.pack_branch (chain_path_id, branch_id, name, sort, is_deleted, create_dt, create_by, modify_dt,
                              modify_by)
SELECT spb.chain_path_id,
       spb.branch_id,
       spb.name,
       spb.sort,
       spb.is_deleted,
       spb.create_dt,
       spb.create_by,
       spb.modify_dt,
       spb.modify_by
from dl_test.sf_pack_branch spb
where spb.chain_path_id in
      (
          SELECT spcp.chain_path_id
          from dl_test.sf_pack_chain_path spcp
          WHERE spcp.pack_id IN
                (SELECT sp.pack_id FROM dl_test.sf_pack sp WHERE sp.tenant_code = 'DL' AND sp.is_tpl = 1)
      );


insert
into dl_resource.pack_element (element_id, pack_id, chain_path_id, branch_id, type, sort, title, content, attachments,
                               ext_data,
                               is_deleted, create_dt, create_by, modify_dt, modify_by)
SELECT spe.element_id,
       spe.pack_id,
       spe.chain_path_id,
       spe.branch_id,
       spe.type,
       spe.sort,
       spe.title,
       spe.content,
       spe.attachments,
       spe.ext_data,
       spe.is_deleted,
       spe.create_dt,
       spe.create_by,
       spe.modify_dt,
       spe.modify_by
from dl_test.sf_pack_element spe
where spe.pack_id in
      (SELECT sp.pack_id FROM dl_test.sf_pack sp WHERE sp.tenant_code = 'DL' AND sp.is_tpl = 1);


--更新source
update material
set source = 'DL';
update script
set source = 'DL';
update sop
set source = 'DL';
update pack
set source = 'DL';