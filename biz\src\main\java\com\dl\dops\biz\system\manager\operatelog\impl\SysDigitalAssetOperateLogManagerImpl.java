package com.dl.dops.biz.system.manager.operatelog.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.system.dal.operatelog.SysDigitalAssetOperateLogMapper;
import com.dl.dops.biz.system.dal.operatelog.po.SysDigitalAssetOperateLogPO;
import com.dl.dops.biz.system.manager.operatelog.SysDigitalAssetOperateLogManager;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【sys_digital_asset_operate_log(数字资产管理日志记录表)】的数据库操作Service实现
 * @createDate 2023-08-30 15:43:09
 */
@Service
public class SysDigitalAssetOperateLogManagerImpl
        extends ServiceImpl<SysDigitalAssetOperateLogMapper, SysDigitalAssetOperateLogPO>
        implements SysDigitalAssetOperateLogManager {

}




