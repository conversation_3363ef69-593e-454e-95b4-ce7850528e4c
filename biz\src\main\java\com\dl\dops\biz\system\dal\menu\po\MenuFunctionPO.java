package com.dl.dops.biz.system.dal.menu.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

@Data
@TableName("sys_menu_function")
public class MenuFunctionPO extends BasePO {

    private static final long serialVersionUID = 838174572014900961L;
    @TableId("id")
    public Long id;

    @TableField("menu_id")
    private Long menuId;

    @TableField("function_id")
    private Long functionId;

}
