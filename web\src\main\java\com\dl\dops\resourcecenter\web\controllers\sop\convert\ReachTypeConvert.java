package com.dl.dops.resourcecenter.web.controllers.sop.convert;

import com.dl.dops.resourcecenter.sop.dto.RcReachTypeDTO;
import com.dl.dops.resourcecenter.sop.enums.RcReachTypeEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-30 16:21
 */
public class ReachTypeConvert {

    public static List<RcReachTypeDTO> cnvReachTypeEnumList2VOList(List<RcReachTypeEnum> inputList) {
        if (CollectionUtils.isEmpty(inputList)) {
            return Collections.emptyList();
        }
        return inputList.stream().map(ReachTypeConvert::cnvReachTypeEnum2VO).filter(Objects::nonNull)
                .sorted(Comparator.comparing(RcReachTypeDTO::getSort)).collect(Collectors.toList());
    }

    public static RcReachTypeDTO cnvReachTypeEnum2VO(RcReachTypeEnum input) {
        if (Objects.isNull(input)) {
            return null;
        }
        RcReachTypeDTO result = new RcReachTypeDTO();
        result.setType(input.getType());
        result.setName(input.getName());
        result.setDesc(input.getDesc());
        result.setSort(input.getSort());
        result.setConsumeWeComTime(input.consumeWeComTime());
        result.setValidityPeriod(input.getValidityPeriod());
        result.setSupportByContactSop(input.supportByContactSop());
        result.setSupportByGroupSop(input.supportByGroupSop());
        return result;
    }

}
