package com.dl.dops.biz.resourcecenter.manager.script.impl;

import com.dl.framework.common.utils.JsonUtils;
import com.dl.dops.biz.resourcecenter.manager.script.ScriptSearchManager;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class ScriptSearchManagerImpl implements ScriptSearchManager {

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    private final static String SCRIPT_INDEX = "dl_rc_script";

    @Override
    public void deleteEsScript(Long scriptId) {

        UpdateRequest request = new UpdateRequest();
        request.index(SCRIPT_INDEX);
        request.id(String.valueOf(scriptId));
        Map<String,Object> hashMap = new HashMap<>();
        hashMap.put("is_deleted",Boolean.TRUE);
        request.doc(hashMap);

        //设置Es刷新策略
        request.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);

        try {

            restHighLevelClient.update(request, RequestOptions.DEFAULT);

        } catch (IOException e) {
            log.error("Es同步失败",e);
            log.error("request ={}",JsonUtils.toJSON(request));
        }
    }
    @Override
    public void publishOrCancel(Long scriptId, Integer needToBePublistStatus) {

        UpdateRequest request = new UpdateRequest();
        request.index(SCRIPT_INDEX);
        request.id(String.valueOf(scriptId));
        Map<String,Object> hashMap = new HashMap<>();
        hashMap.put("publish_status",needToBePublistStatus);
        request.doc(hashMap);
        //设置Es刷新策略
        request.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        try {
            restHighLevelClient.update(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("Es同步失败",e);
            log.error("request ={}",JsonUtils.toJSON(request));
        }
    }
}
