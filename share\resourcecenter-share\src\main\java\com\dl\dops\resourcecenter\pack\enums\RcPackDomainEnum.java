package com.dl.dops.resourcecenter.pack.enums;


import java.util.Objects;

/**
 * 服务包适用行业枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-08 17:20
 */
public enum RcPackDomainEnum {

    GENERAL(1, "通用"),
    BANK(2, "银行"),
    <PERSON><PERSON><PERSON>(3, "券商"),
    <PERSON>UN<PERSON>(4, "基金"),
    OTHER(5, "其他");

    private Integer domain;

    private String name;

    RcPackDomainEnum(Integer domain, String name) {
        this.domain = domain;
        this.name = name;
    }

    public static RcPackDomainEnum parse(Integer domain) {
        if (Objects.isNull(domain)) {
            return null;
        }
        for (RcPackDomainEnum domainEnum : RcPackDomainEnum.values()) {
            if (domainEnum.getDomain().equals(domain)) {
                return domainEnum;
            }
        }
        return null;
    }

    public static String getNameByDomain(Integer domain) {
        if (Objects.isNull(domain)) {
            return "";
        }
        for (RcPackDomainEnum domainEnum : RcPackDomainEnum.values()) {
            if (domainEnum.getDomain().equals(domain)) {
                return domainEnum.getName();
            }
        }
        return "";
    }

    public String getName() {
        return name;
    }

    public Integer getDomain() {
        return domain;
    }
}
