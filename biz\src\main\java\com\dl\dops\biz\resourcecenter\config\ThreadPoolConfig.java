package com.dl.dops.biz.resourcecenter.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-13 15:14
 */
@Configuration
public class ThreadPoolConfig {

    private static final Integer SPIDER_CORE_THREAD_SIZE = 5;

    @Bean
    public ExecutorService spiderExecutor() {
        ExecutorService spiderExecutor = new ThreadPoolExecutor(SPIDER_CORE_THREAD_SIZE, 10, 10, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(10),
                new ThreadFactoryBuilder().setNameFormat("mp-spider-executor-service-pool-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        return spiderExecutor;
    }
}
