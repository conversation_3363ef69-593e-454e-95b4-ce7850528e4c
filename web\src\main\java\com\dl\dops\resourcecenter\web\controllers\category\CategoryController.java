package com.dl.dops.resourcecenter.web.controllers.category;

import com.dl.dops.biz.resourcecenter.manager.category.CategoryManager;
import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryBO;
import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryDetailBO;
import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryParamBO;
import com.dl.dops.resourcecenter.category.dto.RcCategoryDTO;
import com.dl.dops.resourcecenter.category.dto.RcCategoryDetailDTO;
import com.dl.dops.resourcecenter.category.param.RcCategoryParam;
import com.dl.dops.resourcecenter.category.param.RcDelCategoryParam;
import com.dl.dops.resourcecenter.category.param.RcListCategoryParam;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.resourcecenter.web.controllers.category.convertor.CategoryConvertor;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping({ "/resource/category", "/dops/resource/category" })
@Api("资源中心-分类")
public class CategoryController extends AbstractController {

    @Autowired
    private CategoryManager categoryManager;

    @PostMapping("/list")
    @ApiOperation("获取分类列表")
    public ResultModel<List<RcCategoryDetailDTO>> list(@RequestBody RcListCategoryParam param) {
        Assert.isTrue(Objects.nonNull(param.getCategoryType()),"分类类型不能为空");
        List<CategoryDetailBO> sysCategories = categoryManager.listAll(param.getCategoryType());
        return ResultModel.success(
                sysCategories.stream().map(CategoryConvertor::convertDetailBO2DTO).filter(Objects::nonNull)
                        .collect(Collectors.toList()));
    }

    @PostMapping("/purelist")
    @ApiOperation("纯粹查询分类列表")
    public ResultModel<List<RcCategoryDTO>> pureList(@RequestBody RcListCategoryParam param) {
        List<Long> categoryIds = CollectionUtils.isNotEmpty(param.getCategoryIds())? param.getCategoryIds().stream().map(Long::valueOf).collect(
                Collectors.toList()): Collections.emptyList();
        List<CategoryBO> sysCategories = categoryManager.pureList(categoryIds,param.getCategoryType());
        return ResultModel.success(
                sysCategories.stream().map(CategoryConvertor::convertBO2DTO).filter(Objects::nonNull)
                        .collect(Collectors.toList()));
    }

    @PostMapping("/add")
    @ApiOperation("新增内容分类")
    public ResultModel add(@Validated @RequestBody RcCategoryParam param) {
        CategoryParamBO paramBo = CategoryConvertor.convertParam2DTO(param);
        Assert.notNull(paramBo, "入参不能为空");
        return ResultModel.success(categoryManager.add(paramBo));
    }

    @PostMapping("/edit/save")
    @ApiOperation("更新内容分类")
    public ResultModel editSave(@Validated @RequestBody RcCategoryParam param) {
        CategoryParamBO paramBo = CategoryConvertor.convertParam2DTO(param);
        Assert.notNull(paramBo, "入参不能为空");
        return ResultModel.success(categoryManager.update(paramBo));
    }

    @PostMapping("/del")
    @ApiOperation("删除内容分类")
    public ResultModel delete(@Validated @RequestBody RcDelCategoryParam param) {
        return ResultModel.success(categoryManager.delete(param.getCategoryId()));
    }
}
