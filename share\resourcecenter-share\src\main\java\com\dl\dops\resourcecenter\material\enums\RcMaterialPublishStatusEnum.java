package com.dl.dops.resourcecenter.material.enums;

/**
 * 素材发布状态枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-30 17:47
 */
public enum RcMaterialPublishStatusEnum {

    RELEASED(1, "已发布"),
    CANCEL_RELEASED(2, "取消发布");

    private Integer status;

    private String desc;

    RcMaterialPublishStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
