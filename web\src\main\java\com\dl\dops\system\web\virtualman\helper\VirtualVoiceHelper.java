package com.dl.dops.system.web.virtualman.helper;

import com.alibaba.nacos.common.utils.StringUtils;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceLinkDTO;
import com.dl.dops.system.web.virtualman.vo.DaVirtualVoiceLinkVO;
import com.dl.dops.system.web.virtualvoice.param.DaVirtualVoiceParam;
import com.dl.dops.system.web.virtualvoice.vo.DaVirtualVoiceVO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-30 10:13
 */
public class VirtualVoiceHelper {

    public static DaVirtualVoiceVO cnvDaVirtualVoiceDTO2VO(DaVirtualVoiceDTO input) {
        DaVirtualVoiceVO result = new DaVirtualVoiceVO();
        result.setBizId(String.valueOf(input.getBizId()));
        result.setTenantCode(input.getTenantCode());
        result.setChannel(input.getChannel());
        result.setVoiceKey(input.getVoiceKey());
        result.setVoiceName(input.getVoiceName());
        result.setGender(input.getGender());
        result.setVoiceDesc(input.getVoiceDesc());
        result.setVoiceType(input.getVoiceType());
        result.setVoiceCategory(input.getVoiceCategory());
        result.setSampleLink(input.getSampleLink());
        result.setEffectDt(input.getEffectDt());
        result.setExpiryDt(input.getExpiryDt());
        result.setMaxVoiceLink(input.getMaxVoiceLink());
        result.setVolume(input.getVolume());
        result.setSpeed(input.getSpeed());
        result.setHeadImg(input.getHeadImg());
        result.setIsEnabled(input.getIsEnabled());
        result.setTenantCodeList(input.getTenantCodeList());
        result.setDuration(input.getDuration());
        if (CollectionUtils.isNotEmpty(input.getVoiceLinks())) {
            result.setVoiceLinks(input.getVoiceLinks().stream().map(dto -> {
                DaVirtualVoiceLinkVO vo = new DaVirtualVoiceLinkVO();
                vo.setSpeed(dto.getSpeed());
                vo.setVolume(dto.getVolume());
                vo.setSampleLink(dto.getSampleLink());
                vo.setDuration(dto.getDuration());
                return vo;
            }).collect(Collectors.toList()));
        }

        return result;
    }

    public static DaVirtualVoiceDTO cnvDaVirtualVoiceParam2DTO(DaVirtualVoiceParam input) {
        DaVirtualVoiceDTO result = new DaVirtualVoiceDTO();
        result.setTenantCodeList(input.getTenantCodeList());
        if (StringUtils.isNotBlank(input.getBizId())) {
            result.setBizId(Long.valueOf(input.getBizId()));
        }
        result.setChannel(input.getChannel());
        result.setVoiceKey(input.getVoiceKey());
        result.setVoiceName(input.getVoiceName());
        result.setGender(input.getGender());
        result.setVoiceDesc(input.getVoiceDesc());
        result.setVoiceType(input.getVoiceType());
        result.setVoiceCategory(input.getVoiceCategory());
        result.setSampleLink(input.getSampleLink());
        result.setEffectDt(input.getEffectDt());
        result.setExpiryDt(input.getExpiryDt());
        result.setMaxVoiceLink(input.getMaxVoiceLink());
        result.setVolume(input.getVolume());
        result.setSpeed(input.getSpeed());
        result.setHeadImg(input.getHeadImg());
        result.setDuration(input.getDuration());
        if (CollectionUtils.isNotEmpty(input.getVoiceLinks())) {
            result.setVoiceLinks(input.getVoiceLinks().stream().map(vo -> {
                DaVirtualVoiceLinkDTO dto = new DaVirtualVoiceLinkDTO();
                dto.setSpeed(vo.getSpeed());
                dto.setVolume(vo.getVolume());
                dto.setSampleLink(vo.getSampleLink());
                dto.setDuration(vo.getDuration());
                return dto;
            }).collect(Collectors.toList()));
        }

        return result;
    }
}
