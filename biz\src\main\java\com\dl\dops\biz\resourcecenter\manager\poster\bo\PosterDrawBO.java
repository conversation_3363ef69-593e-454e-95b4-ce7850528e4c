package com.dl.dops.biz.resourcecenter.manager.poster.bo;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * 海报制作过程数据参数
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Builder
public class PosterDrawBO {

    String bizId;

    String name;

    String categoryId;

    String optDetail;

    String imgUrl;

    /**
     * 是否企业海报：0 否（默认）；1 是
     */
    private Integer isEnterprise;
}
