package com.dl.dops.biz.common.forest.magicvideo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: DynamicNodeParam
 * @author: zhousx
 * @date: 2023/4/25 17:16
 */
@Data
public class DynamicNodeVO {
    @ApiModelProperty("节点id")
    private String nodeId;

    @ApiModelProperty("节点类型")
    private String type;

    @ApiModelProperty("节点时长")
    private Long duration;

    @ApiModelProperty("节点封面")
    private String coverUrl;

    @ApiModelProperty("是否启用，0-否 1-是")
    private Integer isEnabled;

    @ApiModelProperty("tts配置")
    private List<TtsConfigVO> ttsList;

    @ApiModelProperty("数字人配置")
    private List<DigitalManConfigVO> dmList;

    @Data
    public static class TtsConfigVO {
        private String ttsId;

        private Long start;

        private String content;

        private Integer enableSubtitle;

        private Integer maxLength;
    }

    @Data
    public static class DigitalManConfigVO {
        private String dmId;

        private Long start;

        private String content;

        private Integer enableSubtitle;

        private Integer maxLength;
    }
}
