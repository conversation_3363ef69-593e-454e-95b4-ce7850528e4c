package com.dl.dops.system.web.role.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleSearchParam extends AbstractPageParam {

    @Size(max = 20)
    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("用户id,指定ID后其他条件无效")
    private Long userId;

}

