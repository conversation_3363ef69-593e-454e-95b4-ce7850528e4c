package com.dl.dops.biz.resourcecenter.manager.pack.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.resourcecenter.dal.pack.PackChainPathMapper;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackChainPathBO;
import com.dl.dops.biz.resourcecenter.manager.pack.helper.PackHelper;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackBranchPO;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackChainPathPO;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackElementPO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.resourcecenter.manager.pack.PackBranchManager;
import com.dl.dops.biz.resourcecenter.manager.pack.PackChainPathManager;
import com.dl.dops.biz.resourcecenter.manager.pack.PackElementManager;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackChainPathSaveBO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 10:07
 */
@Component
public class PackChainPathManagerImpl extends ServiceImpl<PackChainPathMapper, PackChainPathPO>
        implements PackChainPathManager {

    @Resource
    private HostTimeIdg hostTimeIdg;

    @Resource
    private PackBranchManager packBranchManager;

    @Resource
    private PackElementManager packElementManager;

    @Resource
    private OperatorUtil operatorUtil;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public List<PackChainPathBO> batchSave(Long packId, List<PackChainPathSaveBO> saveBOList) {
        Assert.notNull(packId, "服务包id不能为空");
        Assert.isTrue(CollectionUtils.isNotEmpty(saveBOList), "服务包链路不能为空");

        //查询已有的链路
        List<PackChainPathPO> existChainPathList = baseMapper.selectList(
                Wrappers.lambdaQuery(PackChainPathPO.class).eq(PackChainPathPO::getPackId, packId)
                        .eq(PackChainPathPO::getIsDeleted, Const.ZERO));
        //若无已有链路，则表示本次全是新增
        if (CollectionUtils.isEmpty(existChainPathList)) {
            //批量新增
            List<PackChainPathPO> insertPOList = this.buildAddList(packId, saveBOList);
            this.saveBatch(insertPOList);
            return insertPOList.stream().map(PackHelper::cnvPackChainPathPO2DTO).collect(Collectors.toList());
        }

        //需要更新的链路,即有链路id的
        List<PackChainPathSaveBO> needToUpdateList = saveBOList.stream()
                .filter(s -> Objects.nonNull(s.getChainPathId())).collect(Collectors.toList());
        //需要新增的链路，即无链路id的
        List<PackChainPathSaveBO> needToAddList = saveBOList.stream().filter(s -> Objects.isNull(s.getChainPathId()))
                .collect(Collectors.toList());

        //需要逻辑删除的链路id列表
        List<Long> needToDeleteChainPathIdList = calNeedToDeleteIdList(saveBOList, existChainPathList);

        //用来存放新增、修改之后的链路
        List<PackChainPathPO> finalPackChainPOList = new ArrayList<>();
        //批量新增
        if (CollectionUtils.isNotEmpty(needToAddList)) {
            List<PackChainPathPO> insertPOList = this.buildAddList(packId, needToAddList);
            this.saveBatch(insertPOList);
            finalPackChainPOList.addAll(insertPOList);
        }
        //批量修改
        if (CollectionUtils.isNotEmpty(needToUpdateList)) {
            List<PackChainPathPO> updatePOList = this
                    .buildUptList(packId, needToUpdateList, operatorUtil.getOperator());
            baseMapper.batchUpdate(updatePOList);
            finalPackChainPOList.addAll(updatePOList);
        }
        //批量逻辑删除
        if (CollectionUtils.isNotEmpty(needToDeleteChainPathIdList)) {
            //删除链路
            baseMapper.batchLogicDeleteByChainPathIds(needToDeleteChainPathIdList, operatorUtil.getOperator());
            //删除分支
            packBranchManager.update(Wrappers.lambdaUpdate(PackBranchPO.class)
                    .in(PackBranchPO::getChainPathId, needToDeleteChainPathIdList)
                    .set(PackBranchPO::getIsDeleted, Const.ONE));
            //删除元素
            packElementManager.update(Wrappers.lambdaUpdate(PackElementPO.class).eq(PackElementPO::getPackId, packId)
                    .in(PackElementPO::getChainPathId, needToDeleteChainPathIdList)
                    .set(PackElementPO::getIsDeleted, Const.ONE));
        }
        return finalPackChainPOList.stream().map(PackHelper::cnvPackChainPathPO2DTO)
                .sorted(Comparator.comparing(PackChainPathBO::getSort)).collect(Collectors.toList());
    }

    @Override
    public PackChainPathBO getByChainPathId(Long chainPathId) {
        Assert.notNull(chainPathId, "链路id不能为空");
        PackChainPathPO packChainPathPO = baseMapper.selectOne(
                Wrappers.lambdaQuery(PackChainPathPO.class).eq(PackChainPathPO::getChainPathId, chainPathId)
                        .eq(PackChainPathPO::getIsDeleted, Const.ZERO));
        return PackHelper.cnvPackChainPathPO2DTO(packChainPathPO);
    }

    @Override
    public List<PackChainPathBO> listByPackId(Long packId) {
        Assert.notNull(packId, "服务包id不能为空");
        List<PackChainPathPO> packChainPathPOList = baseMapper.selectList(
                Wrappers.lambdaQuery(PackChainPathPO.class).eq(PackChainPathPO::getPackId, packId)
                        .eq(PackChainPathPO::getIsDeleted, Const.ZERO));
        if (CollectionUtils.isEmpty(packChainPathPOList)) {
            return Collections.emptyList();
        }
        return packChainPathPOList.stream().map(PackHelper::cnvPackChainPathPO2DTO).collect(Collectors.toList());
    }

    @Override
    public List<PackChainPathBO> listByPackIds(List<Long> packIds) {
        Assert.isTrue(CollectionUtils.isNotEmpty(packIds), "服务包id列表不能为空");

        List<PackChainPathPO> packChainPathPOList = baseMapper.selectList(
                Wrappers.lambdaQuery(PackChainPathPO.class).in(PackChainPathPO::getPackId, packIds)
                        .eq(PackChainPathPO::getIsDeleted, Const.ZERO));
        if (CollectionUtils.isEmpty(packChainPathPOList)) {
            return Collections.emptyList();
        }

        return packChainPathPOList.stream().map(PackHelper::cnvPackChainPathPO2DTO).collect(Collectors.toList());
    }

    /**
     * 计算需要逻辑删除的链路id列表
     *
     * @param list
     * @param exisitPOList
     * @return
     */
    private static List<Long> calNeedToDeleteIdList(List<PackChainPathSaveBO> list,
            List<PackChainPathPO> exisitPOList) {
        Set<Long> saveIdSet = list.stream().map(PackChainPathSaveBO::getChainPathId).collect(Collectors.toSet());
        List<Long> needToDeleteIdList = exisitPOList.stream().map(po -> {
            if (!saveIdSet.contains(po.getChainPathId())) {
                return po.getChainPathId();
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return needToDeleteIdList;
    }

    private List<PackChainPathPO> buildAddList(Long packId, List<PackChainPathSaveBO> needToAddList) {
        return needToAddList.stream().map(saveBO -> {
            PackChainPathPO insertPO = new PackChainPathPO();
            insertPO.setChainPathId(hostTimeIdg.generateId().longValue());
            insertPO.setPackId(packId);
            insertPO.setName(saveBO.getName());
            insertPO.setSort(saveBO.getSort());
            insertPO.setIsDeleted(Const.ZERO);
            return insertPO;
        }).collect(Collectors.toList());
    }

    private List<PackChainPathPO> buildUptList(Long packId, List<PackChainPathSaveBO> needToUpdateList, Long userId) {
        return needToUpdateList.stream().map(saveBO -> {
            PackChainPathPO updatePO = new PackChainPathPO();
            updatePO.setPackId(packId);
            updatePO.setChainPathId(saveBO.getChainPathId());
            updatePO.setName(saveBO.getName());
            updatePO.setSort(saveBO.getSort());
            updatePO.setModifyBy(operatorUtil.getOperator());
            return updatePO;
        }).collect(Collectors.toList());
    }
}
