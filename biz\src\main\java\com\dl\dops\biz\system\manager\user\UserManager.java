package com.dl.dops.biz.system.manager.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.system.dal.function.po.FunctionPO;
import com.dl.dops.biz.system.dal.user.po.UserPO;
import com.dl.dops.biz.system.manager.user.bo.ResetUserPasswordBO;
import com.dl.dops.biz.system.manager.user.bo.UpdatePwdParamBO;
import com.dl.dops.biz.system.manager.user.bo.UpdateUserSelfBO;
import com.dl.dops.biz.system.manager.user.bo.UserSaveBO;
import com.dl.dops.biz.system.manager.user.bo.UserSearchParamBO;
import com.dl.dops.biz.system.manager.user.dto.BasicUserDTO;
import com.dl.dops.biz.system.manager.user.dto.PermissionsDTO;
import com.dl.dops.biz.system.manager.user.dto.UserDTO;

import java.util.Collection;
import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 09:25
 */
public interface UserManager extends IService<UserPO>, CommonService {

    String TOKEN_KEY_PREFIX = "dl.dops.admUser.token.";

    /**
     * 修改密码
     *
     * @param bo
     */
    boolean updatePassword(UpdatePwdParamBO bo);

    UserDTO addUser(UserSaveBO saveBO);

    BasicUserDTO login(UserPO po);

    String createJwtToken(BasicUserDTO dto);

    /**
     * 解析token
     *
     * @param token
     * @return
     */
    BasicUserDTO parseJwtToken(String token);

    UserDTO findUserDetail(Long userId);

    /**
     * 刷新用户详情的缓存
     *
     * @param userId
     * @return
     */
    UserDTO refreshUserDetailCache(Long userId);

    /**
     * 登出
     *
     * @param token
     */
    void logoutJwtToken(String token);

    String formatToken(String token);

    /**
     * 从redis中获取会话token
     *
     * @param userId
     * @return
     */
    String getSessionToken(Long userId);

    /**
     * 刷新缓存过期时间
     *
     * @param userId
     */
    void refreshToken(Long userId);

    IPage<UserDTO> findUsers(UserSearchParamBO bo);

    /**
     * 是否拥有权限
     *
     * @param roleIds
     * @param functionCode 权限代码
     * @return
     */
    boolean hasPermission(Collection<Long> roleIds, String functionCode);

    PermissionsDTO hasPermission(Collection<Long> roleIds, Collection<String> functionCodes);

    /**
     * 更新用户信息
     *
     * @param po
     */
    void updateUserDetail(UserPO po);

    Set<FunctionPO> getUserFunctions(Long userId);

    /**
     * 用户修改个人信息(仅供用户修改自己信息)
     *
     * @param bo
     * @return
     */
    boolean updateUserSelfDetail(UpdateUserSelfBO bo);

    /**
     * 重置用户密码
     *
     * @param bo
     * @return
     */
    boolean resetUserPassword(ResetUserPasswordBO bo);
}
