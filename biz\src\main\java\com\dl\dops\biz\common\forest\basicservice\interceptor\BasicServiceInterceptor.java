package com.dl.dops.biz.common.forest.basicservice.interceptor;

import com.dl.dops.biz.common.config.DlDomainConfig;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.util.ApplicationUtil;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BasicServiceInterceptor implements Interceptor {

    @Override
    public boolean beforeExecute(ForestRequest request) {
        DlDomainConfig bean = ApplicationUtil.getBean(DlDomainConfig.class);
        OperatorUtil operatorUtil = ApplicationUtil.getBean(OperatorUtil.class);
        request.setUrl(bean.getBasicServiceBaseUrl() + request.getMethod().getMetaRequest().getUrl());
        request.addHeader(Const.SYSTEM_CODE, Const.MAGIC_SYSTEM_CODE);
        request.addHeader(Const.BASIC_SERVICE_INVOKE_TENANTCODE, Const.DEFAULT_TENANT_CODE);
        request.addHeader(Const.BASIC_SERVICE_INVOKE_OPERATOR, operatorUtil.getOperator());
        return Boolean.TRUE;
    }

}
