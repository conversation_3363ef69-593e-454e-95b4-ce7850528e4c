package com.dl.dops.biz.common.service.menu;

import com.dl.dops.biz.common.forest.basicservice.dto.BasicServiceMenuObjectDTO;
import com.dl.dops.biz.common.forest.basicservice.param.BasicServiceSysMenuParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.BasicServiceTenantMenuSaveParamDTO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-15 15:40
 */
public interface BasicServiceMenuService {

    /**
     * 查询可分配给租户的系统菜单列表
     *
     * @param param
     * @return
     */
    BasicServiceMenuObjectDTO tenantMenuList(BasicServiceSysMenuParamDTO param);

    /**
     * 保存指定租户的菜单和功能项
     *
     * @param param
     * @return
     */
    Boolean saveTenantMenu(BasicServiceTenantMenuSaveParamDTO param);

}
