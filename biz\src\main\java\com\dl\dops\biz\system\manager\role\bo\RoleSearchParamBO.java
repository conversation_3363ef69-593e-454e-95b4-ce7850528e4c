package com.dl.dops.biz.system.manager.role.bo;

import com.dl.dops.biz.common.SearchBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleSearchParamBO extends SearchBO {

    private static final long serialVersionUID = -3393000607885243069L;
    @Size(max = 20)
    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("用户id,指定ID后其他条件无效")
    private Long userId;

}

