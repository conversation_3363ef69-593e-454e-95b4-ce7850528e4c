<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">

    <!-- text -->
    <property name="text.type" value="text" dbms="mysql"/>
    <property name="text.type" value="text" dbms="postgresql"/>
    <!-- NUMBER -->
    <property name="number.type" value="decimal" dbms="mysql,postgresql"/>

    <!-- CLOB -->
    <property name="clob.type" value="clob" dbms="postgresql"/>
    <property name="clob.type" value="longtext" dbms="mysql"/>
    <!-- BLOB -->
    <property name="blob.type" value="longblob" dbms="mysql"/>
    <property name="blob.type" value="bytea" dbms="postgresql"/>

    <!-- DATE -->
    <property name="date.type" value="datetime" dbms="mysql"/>
    <property name="date.type" value="timestamp" dbms="postgresql"/>

    <!-- STRING -->
    <property name="string.type" value="varchar" dbms="mysql,postgresql"/>

    <!-- sysdate -->
    <property name="sysdate.type" value="sysdate()" dbms="mysql"/>
    <property name="sysdate.type" value="clock_timestamp()" dbms="postgresql"/>

    <!-- bigInt -->
    <property name="bigInt.type" value="bigint" dbms="mysql"/>
    <property name="bigInt.type" value="decimal" dbms="postgresql"/>

    <!-- integer -->
    <property name="integer.type" value="int" dbms="mysql"/>
    <property name="integer.type" value="int4" dbms="postgresql"/>

    <!-- tinyint -->
    <property name="tinyint.type" value="tinyint" dbms="mysql"/>
    <property name="tinyint.type" value="smallint" dbms="postgresql"/>
    <!-- binary -->
    <property name="binary.type" value="blob" dbms="mysql"/>
    <property name="binary.type" value="bytea" dbms="postgresql"/>

</databaseChangeLog>
