package com.dl.dops.resourcecenter.pack.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 15:08
 */
@Data
@ApiModel("服务包链路信息")
public class RcPackChainPathDTO implements Serializable {

    private static final long serialVersionUID = 172664263590769268L;
    @ApiModelProperty("链路id")
    private String chainPathId;

    @ApiModelProperty("链路名称")
    private String name;

    @ApiModelProperty("排序")
    private Integer sort;
}
