package com.dl.dops.resourcecenter.category.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RcListCategoryParam implements Serializable {

    @ApiModelProperty("分类类型")
    Integer categoryType;

    @ApiModelProperty("分类id列表")
    List<String> categoryIds;
}
