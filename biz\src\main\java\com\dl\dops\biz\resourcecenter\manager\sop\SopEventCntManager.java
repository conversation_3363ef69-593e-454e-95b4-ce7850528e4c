package com.dl.dops.biz.resourcecenter.manager.sop;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.resourcecenter.dal.sop.po.SopEventCntPO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventCntBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventCntSaveBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventSaveBO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-07 10:32
 */
public interface SopEventCntManager extends IService<SopEventCntPO>, CommonService {

    void batchSave(List<SopEventSaveBO> saveBOList);

    void save(Long eventId, List<SopEventCntSaveBO> saveBOList);

    List<SopEventCntBO> listByEventId(Long eventId);

    void logicDelete(Long eventId);

    List<SopEventCntBO> listByEventIdList(List<Long> eventIdList);

    /**
     * key-eventId
     * value-内容列表
     *
     * @param eventIdList
     * @return
     */
    Map<Long, List<SopEventCntBO>> getEventCntMap(List<Long> eventIdList);

}
