package com.dl.dops.biz.common.forest.magicvideo.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @describe: TemplatePageQueryParam
 * @author: zhousx
 * @date: 2023/2/8 11:15
 */
@Data
public class TemplatePageQueryParam extends AbstractPageParam {
    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty(value = "模板id")
    private String templateId;

    @ApiModelProperty("模板状态 0-启用，1-禁用")
    private Integer status;

    @ApiModelProperty("是否系统模板 0-否，1-是")
    private Integer isSys;

    @ApiModelProperty("1-竖版 2-横版")
    private Integer resolutionType;

    @ApiModelProperty("排序类型 0-创建时间倒序 1-名称升序，时间倒序 2 修改时间倒序")
    private Integer sortType;

    @ApiModelProperty("转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置")
    private Integer shareConfState;

    @ApiModelProperty("是否包含理财经理 1包含 0 不包含")
    private Integer isManager;

    @ApiModelProperty("租户编码")
    private String tenantCode;
}
