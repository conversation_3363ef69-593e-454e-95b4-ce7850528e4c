package com.dl.dops.resourcecenter.sop.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-06-10 15:36
 */
@Data
@ApiModel("sop事件批量保存参数")
public class RcSopEventBatchSaveParam implements Serializable {

    private static final long serialVersionUID = -5757736168260218266L;
    @ApiModelProperty("sopId")
    @NotBlank
    private String sopId;

    @ApiModelProperty("事件列表")
    @NotEmpty
    private List<RcSopEventSaveParam> eventList;

}
