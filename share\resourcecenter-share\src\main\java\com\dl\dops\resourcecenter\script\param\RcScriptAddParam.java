package com.dl.dops.resourcecenter.script.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @describe: CntScriptParam
 * @author: zhousx
 * @date: 2022/6/15 9:52
 */
@Data
public class RcScriptAddParam implements Serializable {
    private static final long serialVersionUID = -7085501479514446034L;
    @NotBlank
    private String question;

    @NotBlank
    private String content;

    private String category1;

    private String category2;

    private Integer type = 0;

    private String remark;

    private List<String> materialIds;

    @ApiModelProperty("发布状态，1-已发布，2-取消发布")
    private Integer publishStatus = 2;

}
