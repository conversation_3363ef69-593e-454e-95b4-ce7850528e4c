package com.dl.dops.biz.resourcecenter.manager.pack.impl;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.resourcecenter.dal.pack.PackElementMapper;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackElementBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackElementSaveBO;
import com.dl.dops.biz.resourcecenter.manager.pack.helper.PackHelper;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackElementPO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.resourcecenter.manager.pack.PackElementManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 13:37
 */
@Component
public class PackElementManagerImpl extends ServiceImpl<PackElementMapper, PackElementPO>
        implements PackElementManager {

    @Resource
    private HostTimeIdg hostTimeIdg;

    @Override
    public List<PackElementBO> doBatchInsert(Long packId, Long chainPathId, Long branchId,
            List<PackElementSaveBO> elementSaveBOList) {
        Assert.notNull(packId, "服务包id不能为空");

        List<PackElementPO> insertElementPOList = elementSaveBOList.stream().map(bo -> {
            PackElementPO po = new PackElementPO();
            po.setElementId(hostTimeIdg.generateId().longValue());
            po.setAttachments(bo.getAttachments());
            po.setPackId(packId);
            po.setChainPathId(chainPathId);
            po.setBranchId(branchId);
            po.setContent(bo.getContent());
            po.setExtData(bo.getExtData());
            po.setSort(bo.getSort());
            po.setTitle(bo.getTitle());
            po.setType(bo.getType());
            po.setRemark(bo.getRemark());
            return po;
        }).collect(Collectors.toList());

        this.saveBatch(insertElementPOList);

        return insertElementPOList.stream().map(PackHelper::cnnPackElementPO2DTO).collect(Collectors.toList());
    }

    @Override
    public List<PackElementBO> doBatchUpdate(List<PackElementSaveBO> elementSaveBOList) {
        List<PackElementPO> updateElementPOList = elementSaveBOList.stream().map(bo -> {
            PackElementPO po = new PackElementPO();
            po.setElementId(bo.getElementId());
            po.setAttachments(bo.getAttachments());
            po.setContent(bo.getContent());
            po.setExtData(bo.getExtData());
            po.setSort(bo.getSort());
            po.setTitle(bo.getTitle());
            po.setType(bo.getType());
            po.setRemark(bo.getRemark());
            return po;
        }).collect(Collectors.toList());

        baseMapper.batchUpdate(updateElementPOList);

        return updateElementPOList.stream().map(PackHelper::cnnPackElementPO2DTO).collect(Collectors.toList());
    }

    @Override
    public List<PackElementBO> listByPackId(Long packId, Long chainPathId) {
        Assert.notNull(packId, "服务包id不能为空");

        List<PackElementPO> existElementPOList = baseMapper.selectList(
                Wrappers.lambdaQuery(PackElementPO.class).eq(PackElementPO::getPackId, packId)
                        .eq(Objects.nonNull(chainPathId), PackElementPO::getChainPathId, chainPathId)
                        .eq(PackElementPO::getIsDeleted, Const.ZERO));
        if (CollectionUtils.isEmpty(existElementPOList)) {
            return Collections.emptyList();
        }
        return existElementPOList.stream().map(PackHelper::cnnPackElementPO2DTO).collect(Collectors.toList());
    }

    @Override
    public List<PackElementBO> listByPackIds(List<Long> packIds) {
        Assert.isTrue(CollectionUtils.isNotEmpty(packIds), "服务包id列表不能为空");

        List<PackElementPO> existElementPOList = baseMapper.selectList(
                Wrappers.lambdaQuery(PackElementPO.class).in(PackElementPO::getPackId, packIds)
                        .eq(PackElementPO::getIsDeleted, Const.ZERO));
        if (CollectionUtils.isEmpty(existElementPOList)) {
            return Collections.emptyList();
        }
        return existElementPOList.stream().map(PackHelper::cnnPackElementPO2DTO).collect(Collectors.toList());
    }
}
