package com.dl.dops.biz.common.forest.basicservice;

import com.dl.dops.biz.common.forest.basicservice.dto.TenantInfoDTO;
import com.dl.dops.biz.common.forest.basicservice.dto.TenantListDTO;
import com.dl.dops.biz.common.forest.basicservice.interceptor.BasicServiceInterceptor;
import com.dl.dops.biz.common.forest.basicservice.param.AddTenantParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.DelTenantParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.TenantInfoQueryParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.TenantParam;
import com.dl.dops.biz.common.forest.basicservice.param.UpdateTenantParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.UpdateTenantStatusParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.UpdateTenantTrialParamDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-15 20:38
 */
@BaseRequest(interceptor = BasicServiceInterceptor.class)
public interface TenantClient {

    @Post(url = "/internal/tenant/info")
    ResultModel<TenantInfoDTO> info(@JSONBody TenantInfoQueryParamDTO param);

    @Post(url = "/internal/tenant/listall")
    ResultModel<List<TenantInfoDTO>> listAll();

    @Post("/internal/tenant/page")
    ResultPageModel<TenantListDTO> pageTenant(@JSONBody TenantParam p);

    @Post("/internal/tenant/add")
    ResultModel<String> addTenant(@JSONBody AddTenantParamDTO param);

    @Post("/internal/tenant/update")
    ResultModel updateTenant(@JSONBody UpdateTenantParamDTO param);

    @Post("/internal/tenant/update/status")
    ResultModel updateStatus(@JSONBody UpdateTenantStatusParamDTO param);

    @Post("/internal/tenant/del")
    ResultModel delTenant(@JSONBody DelTenantParamDTO param);

    @Post("/internal/tenant/update/trial")
    ResultModel updateTrial(@JSONBody UpdateTenantTrialParamDTO param);

}
