package com.dl.dops.biz.resourcecenter.es.script.po;

import cn.easyes.annotation.TableField;
import cn.easyes.annotation.TableId;
import cn.easyes.annotation.TableName;
import cn.easyes.common.enums.FieldType;
import cn.easyes.common.enums.IdType;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Date;

/**
 * @describe: DlRcScriptIdex
 * @author: zhousx
 * @date: 2022/6/15 17:40
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@TableName(value = "dl_rc_script")
public class EsIndexRcScript {
    @TableId(value = "script_id", type = IdType.CUSTOMIZE)
    Long scriptId;
    @TableField(value = "question",
            fieldType = FieldType.KEYWORD_TEXT,
            analyzer = "ik_smart",
            searchAnalyzer = "ik_smart")
    String question;
    @TableField(value = "content",
            fieldType = FieldType.KEYWORD_TEXT,
            analyzer = "ik_smart",
            searchAnalyzer = "ik_smart")
    String content;
    @TableField(value = "category1", fieldType = FieldType.LONG)
    Long category1;
    @TableField(value = "category2", fieldType = FieldType.LONG)
    Long category2;
    @TableField(value = "create_by", fieldType = FieldType.LONG)
    Long createBy;
    @TableField(value = "modify_by", fieldType = FieldType.LONG)
    Long modifyBy;
    @TableField(value = "is_deleted", fieldType = FieldType.BOOLEAN)
    Boolean isDeleted;
    @TableField(value = "modify_dt", fieldType = FieldType.DATE)
    Date modifyDt;
    @TableField(value = "create_dt", fieldType = FieldType.DATE)
    Date createDt;
    @TableField(value = "publish_status", fieldType = FieldType.INTEGER)
    Integer publishStatus;
    @TableField(value = "creator_name", fieldType = FieldType.KEYWORD)
    String creatorName;
    @TableField(value = "modify_name", fieldType = FieldType.KEYWORD)
    String modifyName;
    @TableField(value = "remark", fieldType = FieldType.KEYWORD)
    String remark;
}
