package com.dl.dops.biz.system.dal.operatelog.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

/**
 * 数字资产管理日志记录表
 *
 * @TableName sys_digital_asset_operate_log
 */
@TableName(value = "sys_digital_asset_operate_log")
@Data
public class SysDigitalAssetOperateLogPO extends BasePO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 内部业务唯一标识，如 数字人的唯一标识、声音的唯一标识等
     */
    @TableField(value = "biz_id")
    private String bizId;

    /**
     * 操作类型: 1 数字人 ；2 声音 ；3 花字
     */
    @TableField(value = "operate_type")
    private Integer operateType;

    /**
     * 操作类型描述
     */
    @TableField(value = "operate_type_desc")
    private String operateTypeDesc;

    /**
     * 操作内容，json结构串
     */
    @TableField(value = "operate_cnt")
    private String operateCnt;

}