package com.dl.dops.biz.common.forest.magicvideo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-27 11:15
 */
@Data
public class InternalChartletCategoryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("分类编码")
    private Integer code;

    @ApiModelProperty("分类名称")
    private String name;

    @ApiModelProperty("分类排序")
    private Integer sort;

}
