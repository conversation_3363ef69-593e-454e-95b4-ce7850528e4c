package com.dl.dops.resourcecenter.web.controllers.organize;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.dops.biz.resourcecenter.dal.organize.po.OrganizePO;
import com.dl.dops.biz.resourcecenter.manager.organize.OrganizeManager;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.resourcecenter.web.controllers.organize.convert.OrganizeConvert;
import com.dl.dops.resourcecenter.web.controllers.organize.vo.OrganizeVO;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.controller.param.AbstractPageParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OrganizeProcess extends AbstractController {

    @Resource
    private OrganizeManager organizeManager;

    public ResultPageModel<OrganizeVO> page(AbstractPageParam param) {
        Page<OrganizePO> page = organizeManager.page(new Page<>(param.getPageIndex(), param.getPageSize()));
        List<OrganizePO> organizePOList = page.getRecords().stream()
                .sorted(Comparator.comparing(OrganizePO::getSort).reversed()).collect(Collectors.toList());
        page.setRecords(organizePOList);
        return pageQueryModel(page, OrganizeConvert::convertBO2VO);
    }
}
