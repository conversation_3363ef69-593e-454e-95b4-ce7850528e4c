package com.dl.dops.resourcecenter.pack.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 20:41
 */
@Data
@ApiModel("服务包链路详情")
public class RcRcPackChainPathDetailDTO extends RcPackChainPathDTO implements Serializable {

    private static final long serialVersionUID = -6069042641611748678L;
    @ApiModelProperty("服务包分支列表")
    private List<RcPackBranchDetailDTO> packBranchList;

}
