package com.dl.dops.resourcecenter.sop.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-07 11:18
 */
@Data
@ApiModel("Sop事件内容保存参数")
public class RcSopEventCntSaveParam implements Serializable {

    private static final long serialVersionUID = 4526515498162627274L;
    @ApiModelProperty("内容类型 1素材 2海报")
    private Integer contentType;

    @ApiModelProperty("内容id")
    private String contentId;

}
