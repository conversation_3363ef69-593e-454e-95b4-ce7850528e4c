package com.dl.dops.resourcecenter.pack.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 15:08
 */
@Data
@ApiModel("服务包保存响应")
public class RcPackSaveRespDTO implements Serializable {

    private static final long serialVersionUID = -4715842303130391404L;
    @ApiModelProperty("服务包id")
    private String packId;

    @ApiModelProperty("链路列表")
    private List<RcPackChainPathDTO> chainPathList;
}
