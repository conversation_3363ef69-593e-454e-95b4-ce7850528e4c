package com.dl.dops.biz.resourcecenter.manager.pack.bo;

import com.dl.dops.biz.common.BaseBO;
import com.dl.dops.resourcecenter.pack.enums.RcPackSceneEnum;
import com.dl.dops.resourcecenter.pack.enums.RcPackStatusEnum;
import lombok.Data;

import java.util.Date;

@Data
public class PackBO extends BaseBO {
    private static final long serialVersionUID = 1860296981048741207L;
    /**
     * 服务包id
     */
    private Long packId;

    /**
     * 标题
     */
    private String title;

    /**
     * 所属一级分类id
     */
    private Long category1;

    /**
     * 所属二级分类id
     */
    private Long category2;

    /**
     * 场景概述
     */
    private String sceneOverview;

    /**
     * 适用行业
     */
    private Integer domain;

    /**
     * 详细描述
     */
    private String detailedDescription;

    /**
     * 运营投放建议
     */
    private String suggest;

    /**
     * 状态
     *
     * @see RcPackStatusEnum
     */
    private Integer status;

    /**
     * 场景 1-助你营，2-助你拍
     *
     * @see RcPackSceneEnum
     */
    private Integer scene;

    /**
     * 创建时间
     */
    private Date createDt;

    /**
     * 修改时间
     */
    private Date modifyDt;

    public Long createBy;

    public Long modifyBy;

    private String creatorName;

    private String modifyName;
}
