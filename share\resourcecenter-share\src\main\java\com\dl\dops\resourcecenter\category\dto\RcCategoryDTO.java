package com.dl.dops.resourcecenter.category.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RcCategoryDTO {

    @ApiModelProperty("内容分类业务ID")
    String id;

    @ApiModelProperty("内容分类名称")
    String name;

    @ApiModelProperty("内容业务分类")
    Integer categoryType;

    @ApiModelProperty("内容分类级别： 0 一级（默认）； 1 二级")
    Integer categoryLevel;

    @ApiModelProperty("内容分类父级ID")
    String parentId;

    @ApiModelProperty("分类级别路径，以|区分")
    String link;

}
