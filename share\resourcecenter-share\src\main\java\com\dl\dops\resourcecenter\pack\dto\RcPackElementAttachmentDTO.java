package com.dl.dops.resourcecenter.pack.dto;

import com.dl.dops.resourcecenter.material.dto.RcMaterialDTO;
import com.dl.dops.resourcecenter.poster.dto.RcPosterDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 20:45
 */
@Data
@ApiModel("服务包元素附件")
public class RcPackElementAttachmentDTO implements Serializable {

    private static final long serialVersionUID = 1171174344534805195L;
    @ApiModelProperty("素材id")
    private String materialId;

    @ApiModelProperty("海报id")
    private String posterId;

    @ApiModelProperty("素材")
    private RcMaterialDTO material;

    @ApiModelProperty("海报")
    private RcPosterDTO poster;

}
