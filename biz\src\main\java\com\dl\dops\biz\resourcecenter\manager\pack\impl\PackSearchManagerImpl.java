package com.dl.dops.biz.resourcecenter.manager.pack.impl;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.dl.dops.biz.resourcecenter.es.pack.EsIndexRcPackMapper;
import com.dl.dops.biz.resourcecenter.es.pack.po.EsIndexRcPack;
import com.dl.dops.biz.resourcecenter.manager.pack.PackSearchManager;
import com.dl.framework.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class PackSearchManagerImpl implements PackSearchManager {

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private EsIndexRcPackMapper esIndexRcPackMapper;

    private final static String PACK_INDEX = "dl_rc_pack";

    @Override
    public void publishOrCancel(String packId, Integer needToBeStatus) {

        LambdaEsQueryWrapper<EsIndexRcPack> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.eq(EsIndexRcPack::getPackId,packId);
        EsIndexRcPack esIndexRcPack = esIndexRcPackMapper.selectOne(queryWrapper);
        //当刚复制、或者新建服务包时，packId并没有保存在es上，这里直接返回
        if (Objects.isNull(esIndexRcPack)){
            return;
        }
        UpdateRequest request = new UpdateRequest();
        request.index(PACK_INDEX);
        request.id(esIndexRcPack.getId());
        Map<String,Object> hashMap = new HashMap<>();
        hashMap.put("status",needToBeStatus);
        request.doc(hashMap);
        //设置Es刷新策略
        request.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        try {
            restHighLevelClient.update(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("Es同步失败",e);
            log.error("request ={}", JsonUtils.toJSON(request));
        }
    }
}

