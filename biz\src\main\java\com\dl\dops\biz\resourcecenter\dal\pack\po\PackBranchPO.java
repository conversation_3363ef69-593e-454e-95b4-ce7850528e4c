package com.dl.dops.biz.resourcecenter.dal.pack.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 09:38
 */
@Data
@TableName("pack_branch")
public class PackBranchPO extends BasePO {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @TableField("chain_path_id")
    private Long chainPathId;

    @TableField("branch_id")
    private Long branchId;

    @TableField("name")
    private String name;

    @TableField("sort")
    private Integer sort;

    @TableField("is_deleted")
    private Integer isDeleted;
}
