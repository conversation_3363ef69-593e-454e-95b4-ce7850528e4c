package com.dl.dops.biz.common.forest.basicservice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-15 14:14
 */
@Data
@ApiModel("系统菜单信息")
public class BasicServiceMenuDTO {

    @ApiModelProperty("菜单主键")
    private String menuId;

    @ApiModelProperty("菜单名称")
    private String name;

    @ApiModelProperty("菜单图标")
    private String icon;

    @ApiModelProperty("菜单url")
    private String url;

    @ApiModelProperty("菜单顺序")
    private Integer sort;

    @ApiModelProperty("是否禁用【0-开启1-禁用】")
    private Integer disable = 1;

    @ApiModelProperty("菜单等级【1-一级菜单2-二级菜单···】")
    private Integer level;

    @ApiModelProperty("是否已分配权限，0否1是")
    private Integer owner = 0;

    @ApiModelProperty("上级菜单id")
    private String parentId;

    @ApiModelProperty("子菜单")
    private List<BasicServiceMenuDTO> children = new ArrayList<>();

    @ApiModelProperty("可配置的权限")
    private List<BasicServiceFunctionDTO> functions = new ArrayList<>();

}
