package com.dl.dops.biz.resourcecenter.dal.tag.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

@Data
@TableName("tag_rela")
public class TagRelaPO extends BasePO {

    @TableId
    private Long id;

    @TableField("biz_id")
    private Long bizId;

    @TableField("tag_id")
    private Long tagId;

    @TableField("type")
    private Integer type;

    @TableField("is_deleted")
    private Integer isDelete;
}
