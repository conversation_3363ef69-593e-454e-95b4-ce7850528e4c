package com.dl.dops.system.web.virtualman.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class VmAuthParam {

    @NotNull(message = "数字人厂商必填")
    @ApiModelProperty(value = "数字人来源渠道", required = true)
    Integer channel;

    @NotEmpty(message = "被授权租户编码必填")
    @ApiModelProperty(value = "被授权租户编码", required = true)
    List<String> authTenantCode;

    @NotBlank(message = "数字人外部编码必填")
    @ApiModelProperty(value = "数字人外部编码", required = true)
    String vmCode;

    @NotNull(message = "数字人唯一标识必填")
    @ApiModelProperty(value = "数字人唯一标识bizId", required = true)
    Long vmBizId;

}
