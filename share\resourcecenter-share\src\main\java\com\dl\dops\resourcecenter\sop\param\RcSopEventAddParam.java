package com.dl.dops.resourcecenter.sop.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-07 09:21
 */
@Data
@ApiModel("添加sop事件参数")
public class RcSopEventAddParam implements Serializable {

    private static final long serialVersionUID = -7815354378892791647L;
    @ApiModelProperty("sopId")
    @NotBlank(message = "sopId不能为空")
    private String sopId;

    /**
     * 1群发 2任务下发 3纯提醒 4企微朋友圈群发 5修改旅程阶段 6一对一私聊
     */
    @ApiModelProperty("触达形式")
    @NotNull(message = "触达形式不能为空")
    private Integer reachWay;

    @ApiModelProperty("事项名称")
    @NotBlank(message = "任务标题不能为空")
    @Length(min = 1, max = 20)
    private String name;

    @ApiModelProperty("时间规则类型")
    @NotNull(message = "时间规则类型不能为空")
    private Integer ruleType;

    @ApiModelProperty("规则内容，定时推送时为天-HH:mm:ss  周期推送为首次天-天-HH:mm:ss")
    private String ruleContent;

    @ApiModelProperty("发送内容")
    @Length(max = 500, message = "发送内容长度最大为500")
    private String content;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("有效期")
    @NotNull(message = "有效期不能为空")
    private Integer validityPeriod;

    @ApiModelProperty("添加内容列表")
    private List<RcSopEventCntAddParam> cntParamList;
}
