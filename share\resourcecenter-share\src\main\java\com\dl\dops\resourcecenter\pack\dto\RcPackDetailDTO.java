package com.dl.dops.resourcecenter.pack.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-11 15:34
 */
@Data
@ApiModel("资源中心-服务包详情")
public class RcPackDetailDTO extends RcPackDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("链路详情列表")
    private List<RcRcPackChainPathDetailDTO> packChainPathDetailList;

}
