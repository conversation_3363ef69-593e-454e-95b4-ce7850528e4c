package com.dl.dops.biz.resourcecenter.manager.rs.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-10-21 16:39
 */
@Data
public class RsPackElementDTO implements Serializable {
    private static final long serialVersionUID = 7184601505161059262L;
    /**
     * 服务包id
     */
    private Long packId;

    /**
     * 元素id
     */
    private Long elementId;

    /**
     * 链路id
     */
    private Long chainPathId;

    /**
     * 分支id
     */
    private Long branchId;

    /**
     * 元素类型
     *
     * @see：PackElementTypeEnum
     */
    private Integer type;

    /**
     * 排序
     */
    private int sort;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 附件
     */
    private String attachments;

    /**
     * 扩展数据
     */
    private String extData;

}
