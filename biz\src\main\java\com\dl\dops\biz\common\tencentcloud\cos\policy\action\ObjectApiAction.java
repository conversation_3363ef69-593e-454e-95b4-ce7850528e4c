package com.dl.dops.biz.common.tencentcloud.cos.policy.action;

public enum ObjectApiAction {
    //简单上传操作
    PUT("name/cos:PutObject"),
    //表单上传对象
    POST("name/cos:PostObject"),
    DELETE("name/cos:DeleteObject"),

    //分块上传：初始化分块操作
    INIT_MULTI_UPLOAD("name/cos:InitiateMultipartUpload"),
    //分块上传：List 进行中的分块上传
    MULTI_UPLOADS("name/cos:ListMultipartUploads"),
    //分块上传：List 已上传分块操作
    LIST_PARTS("name/cos:ListParts"),
    //分块上传：上传分块块操作
    UPLOAD_PART("name/cos:UploadPart"),
    //分块上传：完成所有分块上传操作
    COMPLETE_MULTI_UPLOAD("name/cos:CompleteMultipartUpload"),
    //取消分块上传操作
    ABORT_MULTI_UPLOAD("name/cos:AbortMultipartUpload"),

    //查询，下载
    //查询元数据
    HEAD_OBJECT("name/cos:HeadObject"),

    OPTIONS_OBJECT("name/cos:OptionsObject"),
    /**
     * 下载
     */
    GET_OBJECT("name/cos:GetObject");
    private String value;

    ObjectApiAction(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
