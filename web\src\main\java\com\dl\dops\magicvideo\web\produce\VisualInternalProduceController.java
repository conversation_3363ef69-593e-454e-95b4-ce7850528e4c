package com.dl.dops.magicvideo.web.produce;

import com.dl.dops.biz.common.forest.magicvideo.dto.DailyProduceStatisticsDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.ProduceBatchDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.ProduceJobDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.VisualProduceJobInternalPageParamDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.VisualProduceJobPageDTO;
import com.dl.dops.biz.common.forest.magicvideo.param.ProduceJobPageQueryParam;
import com.dl.dops.biz.magicvideo.manager.MagicVideoManager;
import com.dl.dops.magicvideo.deliveryplan.param.ProduceBatchPageQueryBO;
import com.dl.dops.magicvideo.web.produce.param.ProduceBatchPageQueryParam;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @describe: VisualProduceController
 * @author: zhousx
 * @date: 2023/6/19 10:38
 */
@RestController
@RequestMapping("/dops/magicvideo/produce")
@Api("生产作业模块")
public class VisualInternalProduceController {
    @Autowired
    private MagicVideoManager magicVideoManager;

    @PostMapping("/batchlist")
    @ApiOperation("批次列表")
    public ResultPageModel<ProduceBatchDTO> batchList(@RequestBody ProduceBatchPageQueryParam param) {
        ProduceBatchPageQueryBO bo = new ProduceBatchPageQueryBO();
        bo.setStatusList(param.getStatusList());
        if(StringUtils.isNumeric(param.getPlanId())) {
            bo.setPlanId(Long.valueOf(param.getPlanId()));
        }
        bo.setTenantCodeList(param.getTenantCodeList());
        bo.setEndTime(param.getEndTime());
        bo.setStartTime(param.getStartTime());
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        return magicVideoManager.batchList(bo);
    }

    @PostMapping("/joblistbybatchId")
    @ApiOperation("批次下作业列表")
    public ResultPageModel<ProduceJobDTO> jobListByBatchId(@RequestBody ProduceJobPageQueryParam param) {
        return magicVideoManager.jobListByBatchId(param);
    }

    @PostMapping("/cancelbatch/{batchId}")
    @ApiOperation("取消批次")
    public ResultModel<Void> cancelBatch(@PathVariable Long batchId) {
        return magicVideoManager.cancelBatch(batchId);
    }

    @PostMapping("/jobdetail/{jobId}")
    @ApiOperation("作业详情")
    public ResultModel<ProduceJobDTO> jobDetail(@PathVariable Long jobId) {
        return magicVideoManager.jobDetail(jobId);
    }

    @PostMapping("/dailystatistics")
    @ApiOperation("当日生产统计")
    public ResultModel<DailyProduceStatisticsDTO> dailyStatistics() {
        return magicVideoManager.dailyStatistics();
    }

    @PostMapping("/deleteworks/{jobId}")
    @ApiOperation("删除作品")
    public ResultModel<Void> deleteWorks(@PathVariable Long jobId) {
        return magicVideoManager.deleteWorks(jobId);
    }

    @PostMapping("/pagejob")
    @ApiOperation("分页查询作品")
    public ResultPageModel<VisualProduceJobPageDTO> pageJob(@RequestBody VisualProduceJobInternalPageParamDTO param) {
        return magicVideoManager.pageJob(param);
    }

}
