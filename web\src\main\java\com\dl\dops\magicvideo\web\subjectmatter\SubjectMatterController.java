package com.dl.dops.magicvideo.web.subjectmatter;

import com.alibaba.excel.EasyExcel;
import com.dl.dops.biz.common.forest.magicvideo.dto.AssetProdInfoVO;
import com.dl.dops.biz.common.forest.magicvideo.dto.SubjectMatterDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.SubjectMatterDetailDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.SubjectMatterEditDTO;
import com.dl.dops.biz.common.forest.magicvideo.param.*;
import com.dl.dops.biz.magicvideo.manager.MagicVideoDictManager;
import com.dl.dops.biz.magicvideo.manager.MagicVideoSubjectMatterManager;
import com.dl.dops.magicvideo.web.subjectmatter.vo.StockVO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-01 15:10
 */
@RestController
@Api("题材库管理")
@RequestMapping("/dops/magicvideo/subjectmatter")
public class SubjectMatterController {

    @Autowired
    private MagicVideoSubjectMatterManager magicVideoSubjectMatterManager;

    @Resource
    private MagicVideoDictManager magicVideoDictManager;

    @ApiOperation("保存图表题材")
    @PostMapping("/save")
    public ResultModel<Long> save(@RequestBody @Validated SubjectMatterSaveParam param) {
        return ResultModel.success(magicVideoSubjectMatterManager.save(param));
    }

    @ApiOperation("图表题材库详情")
    @GetMapping("/detail")
    public ResultModel<SubjectMatterDetailDTO> detail(@RequestParam Long bizId) {
        return ResultModel.success(magicVideoSubjectMatterManager.detail(bizId));
    }

    @ApiOperation("分页查询题材")
    @PostMapping("/page")
    public ResultPageModel<SubjectMatterDTO> page(@RequestBody SubjectMatterPageParam pageParam) {
        return magicVideoSubjectMatterManager.page(pageParam);
    }

    @ApiOperation("删除图表题材")
    @GetMapping("/delete")
    public ResultModel<Void> delete(@RequestParam Long bizId) {
        magicVideoSubjectMatterManager.delete(bizId);
        return ResultModel.success(null);
    }

    @ApiOperation("添加题材")
    @PostMapping("/savesubject")
    public ResultModel<Long> saveSubject(@RequestBody @Validated SubjectAddDopsParam param) {
        return ResultModel.success(magicVideoSubjectMatterManager.add(param));
    }
    @ApiOperation("删除题材")
    @GetMapping("/deletesubject")
    public ResultModel<Void> deleteSubject(@RequestParam Long bizId) {
        magicVideoSubjectMatterManager.deleteSubject(bizId);
        return ResultModel.success(null);
    }

    @ApiOperation("编辑题材库")
    @GetMapping("/editsubject")
    public ResultModel<SubjectMatterEditDTO> editSubject(@RequestParam Long bizId) {
        return ResultModel.success(magicVideoSubjectMatterManager.edit(bizId));
    }

    @ApiOperation("分页查询产品")
    @PostMapping("/pageassetprodinfo")
    public ResultPageModel<AssetProdInfoVO> pageAssetProdInfo(@RequestBody AssetProdInfoPageParam param) {
        return magicVideoDictManager.pageAssetProdInfo(param);
    }

    @PostMapping("/readexcelstocks")
    @ApiOperation("读取excel表格中的股票")
    public ResultModel<List<StockVO>> excelBatchProducePreChek(@RequestPart(value = "file") MultipartFile file)
            throws IOException {

        List<StockVO> stockList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), new SubjectMatterExcelListener(stockList, magicVideoDictManager))
                .headRowNumber(2).sheet().doRead();

        return ResultModel.success(stockList);
    }

    @ApiOperation("查询子题材列表")
    @GetMapping("/listsons")
    public ResultModel<List<SubjectMatterDTO>> listSons(@RequestParam Long parentId) {
        return magicVideoSubjectMatterManager.listSons(parentId);
    }

}
