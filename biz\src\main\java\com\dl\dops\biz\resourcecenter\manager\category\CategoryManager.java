package com.dl.dops.biz.resourcecenter.manager.category;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.resourcecenter.dal.category.po.CategoryPO;
import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryBO;
import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryDetailBO;
import com.dl.dops.biz.resourcecenter.manager.category.bo.CategoryParamBO;
import com.dl.dops.resourcecenter.category.enums.RcCategoryTypeEnum;

import java.util.List;

/**
 * @ClassName CategoryService
 * @Description 分类service
 * <AUTHOR>
 * @Date 2022/4/7 9:21
 * @Version 1.0
 **/
public interface CategoryManager extends IService<CategoryPO>, CommonService {

    /**
     * 按categoryType批量获取所有的分类
     *
     * @param categoryType
     * @return
     */
    List<CategoryDetailBO> listAll(Integer categoryType);

    /**
     * 纯粹查询分类列表（不组装上下级）
     *
     * @param categoryIds
     * @return
     */
    List<CategoryBO> pureList(List<Long> categoryIds,Integer categoryType);
    /**
     * 新增分类
     *
     * @param paramBO
     * @return
     */
    boolean add(CategoryParamBO paramBO);

    /**
     * 更新分类
     *
     * @param paramBO
     * @return
     */
    boolean update(CategoryParamBO paramBO);

    /**
     * 删除分类
     *
     * @param categoryId
     * @return
     */
    boolean delete(String categoryId);

    /**
     * 根据categoryId查询目录信息
     *
     * @return
     */
    CategoryBO getByCategoryId(RcCategoryTypeEnum type, Long categoryId);

}
