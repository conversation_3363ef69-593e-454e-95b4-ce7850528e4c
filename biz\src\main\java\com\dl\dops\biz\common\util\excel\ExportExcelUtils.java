package com.dl.dops.biz.common.util.excel;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class ExportExcelUtils {

    public static void exportExcel(HttpServletResponse response, String fileName, ExcelData data) throws Exception {
        // 告诉浏览器用什么软件可以打开此文件
        response.setHeader("content-Type", "application/vnd.ms-excel");
        // 下载文件的默认名称
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
        exportExcel(data, response.getOutputStream());
    }

    public static void exportExcel(ExcelData data, OutputStream out) throws Exception {
        XSSFWorkbook wb = new XSSFWorkbook();
        try {
            String sheetName = data.getName();
            if (null == sheetName) {
                sheetName = "Sheet1";
            }
            XSSFSheet sheet = wb.createSheet(sheetName);
            writeExcel(wb, sheet, data);
            wb.write(out);
        } finally {
            out.close();
            wb.close();
        }
    }

    private static void writeExcel(XSSFWorkbook wb, Sheet sheet, ExcelData data)
            throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        int rowIndex = 0;
        rowIndex = writeTitlesToExcel(wb, sheet, data.getFields());
        writeRowsToExcel(wb, sheet, data, rowIndex);
    }

    private static int writeTitlesToExcel(XSSFWorkbook wb, Sheet sheet, List<ExcelData.Field> fields) {
        int rowIndex = 0;
        int colIndex = 0;
        Font titleFont = wb.createFont();
        titleFont.setFontName("simsun");
        titleFont.setBold(true);
        titleFont.setColor(IndexedColors.BLACK.index);
        XSSFCellStyle titleStyle = wb.createCellStyle();
        titleStyle.setFont(titleFont);
        Row titleRow = sheet.createRow(rowIndex);
        colIndex = 0;
        for (ExcelData.Field field : fields) {
            Cell cell = titleRow.createCell(colIndex);
            cell.setCellValue(field.getTitle());
            cell.setCellStyle(titleStyle);
            colIndex++;
        }
        rowIndex++;
        return rowIndex;
    }

    private static int writeRowsToExcel(XSSFWorkbook wb, Sheet sheet, ExcelData excelData, int rowIndex)
            throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        int colIndex = 0;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Font dataFont = wb.createFont();
        dataFont.setFontName("simsun");
        dataFont.setColor(IndexedColors.BLACK.index);
        XSSFCellStyle dataStyle = wb.createCellStyle();
        dataStyle.setFont(dataFont);
        if (CollectionUtils.isEmpty(excelData.getRows())) {
            return rowIndex;
        }
        for (Object rowData : excelData.getRows()) {
            Row dataRow = sheet.createRow(rowIndex);
            colIndex = 0;
            for (ExcelData.Field field : excelData.getFields()) {
                Cell cell = dataRow.createCell(colIndex);
                Object cellData = invokeGet(rowData, field.getFieldName());
                if (cellData != null) {
                    if (field.getCodeMapping() != null) {
                        cell.setCellValue(String.valueOf(field.getCodeMapping().get(cellData.toString())));
                    } else if (field.isDateType()) {
                        cell.setCellValue(dateFormat.format(cellData));
                    } else {
                        cell.setCellValue(cellData.toString());
                    }
                } else {
                    cell.setCellValue("");
                }
                cell.setCellStyle(dataStyle);
                colIndex++;
            }
            rowIndex++;
        }
        return rowIndex;
    }

    /**
     * 执行get方法
     *
     * @param o         执行对象
     * @param fieldName 属性
     */
    public static Object invokeGet(Object o, String fieldName)
            throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        Method method = getGetMethod(o.getClass(), fieldName);
        return method.invoke(o, new Object[0]);
    }

    /**
     * 构建ExcelData
     *
     * @param excelData
     * @param dataMap   key——FieldName value——Title
     * @return
     */
    public static ExcelData buildExcelData(ExcelData excelData, Map<String, String> dataMap) {
        List<ExcelData.Field> fields = Lists.newArrayList();
        Set<String> keySet = dataMap.keySet();
        for (String fieldName : keySet) {
            ExcelData.Field field = new ExcelData.Field();
            field.setFieldName(fieldName);
            field.setTitle(dataMap.get(fieldName));
            fields.add(field);
        }
        excelData.setFields(fields);
        return excelData;
    }

    public static Method getGetMethod(Class objectClass, String fieldName) throws NoSuchMethodException {
        StringBuilder sb = new StringBuilder();
        sb.append("get");
        sb.append(fieldName.substring(0, 1).toUpperCase());
        sb.append(fieldName.substring(1));
        return objectClass.getMethod(sb.toString());
    }
}