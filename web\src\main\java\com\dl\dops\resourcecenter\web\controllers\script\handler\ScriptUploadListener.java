package com.dl.dops.resourcecenter.web.controllers.script.handler;

import cn.easyes.core.biz.PageInfo;
import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.easyes.core.toolkit.EsWrappers;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.dl.dops.biz.common.util.ApplicationUtil;
import com.dl.dops.biz.common.util.RedisUtil;
import com.dl.dops.biz.resourcecenter.dal.category.po.CategoryPO;
import com.dl.dops.biz.resourcecenter.es.script.EsIndexRcScriptMapper;
import com.dl.dops.biz.resourcecenter.es.script.po.EsIndexRcScript;
import com.dl.dops.biz.resourcecenter.manager.category.CategoryManager;
import com.dl.dops.biz.resourcecenter.manager.script.ScriptManager;
import com.dl.dops.biz.resourcecenter.manager.script.bo.CntScriptBatchBO;
import com.dl.dops.resourcecenter.script.dto.RcScriptUploadResultDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.utils.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @describe: ScriptUploadListener
 * @author: zhousx
 * @date: 2022/6/20 20:08
 */
@Slf4j
public class ScriptUploadListener implements ReadListener<CntScriptBatchBO> {
    private static final List<String> DEFAULT_CATEGORY_NAME = Arrays.asList("未分类", "无分类");

    private static final Integer SEARCH_PAGE_SIZE = 50;

    private ScriptManager scriptManager;

    private CategoryManager categoryManager;

    private RcScriptUploadResultDTO result;

    private HostTimeIdg hostTimeIdg;

    private RedisUtil redisUtil;

    private EsIndexRcScriptMapper esIndexRcScriptMapper;

    private List<CntScriptBatchBO> acceptList = new ArrayList<>();

    private List<ScriptUploadFailResult> rejectList = new ArrayList<>();

    private Map<String, CategoryPO> categoryMap = new HashMap<>();

    public ScriptUploadListener(RcScriptUploadResultDTO result) {
        this.result = result;
        this.scriptManager = ApplicationUtil.getBean(ScriptManager.class);
        this.categoryManager = ApplicationUtil.getBean(CategoryManager.class);
        this.hostTimeIdg = ApplicationUtil.getBean(HostTimeIdg.class);
        this.redisUtil = ApplicationUtil.getBean(RedisUtil.class);
        this.esIndexRcScriptMapper = ApplicationUtil.getBean(EsIndexRcScriptMapper.class);
        List<CategoryPO> categories = new ArrayList<>();
        categories.addAll(categoryManager.lambdaQuery().eq(CategoryPO::getIsDeleted, NumberUtils.INTEGER_ZERO).list());
        this.categoryMap.putAll(categories.stream()
                .collect(Collectors.toMap(CategoryPO::getName, Function.identity(), (v1, v2) -> v1)));
    }

    @Override
    public void invoke(CntScriptBatchBO param, AnalysisContext analysisContext) {
        if (preCheck(param)) {
            acceptList.add(param);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        List<CntScriptBatchBO> saveList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(acceptList)) {
            List<String> duplicateQuestions = new ArrayList<>();
            for (int i = 0; i < acceptList.size(); i += SEARCH_PAGE_SIZE) {
                List<CntScriptBatchBO> subList = acceptList
                        .subList(i, Math.min(acceptList.size(), i + SEARCH_PAGE_SIZE));
                LambdaEsQueryWrapper<EsIndexRcScript> wrapper = EsWrappers.lambdaQuery(EsIndexRcScript.class);
                wrapper.eq(EsIndexRcScript::getIsDeleted, false).in("question.keyword",
                        subList.stream().map(CntScriptBatchBO::getQuestion).collect(Collectors.toList()));
                PageInfo<EsIndexRcScript> result = esIndexRcScriptMapper.pageQuery(wrapper, 0, SEARCH_PAGE_SIZE);
                if (CollectionUtils.isNotEmpty(result.getList())) {
                    duplicateQuestions.addAll(result.getList().stream().map(EsIndexRcScript::getQuestion).distinct()
                            .collect(Collectors.toList()));
                }
            }

            for (CntScriptBatchBO script : acceptList) {
                if (duplicateQuestions.contains(script.getQuestion())) {
                    ScriptUploadFailResult result = new ScriptUploadFailResult();
                    result.setContent(script.getContent());
                    result.setQuestion(script.getQuestion());
                    result.setReason("问题重复，系统已存在该问题的话术");
                    result.setCategory1Name(script.getCategory1Name());
                    result.setCategory2Name(script.getCategory2Name());
                    rejectList.add(result);
                } else if (saveList.size() == 1000) {
                    ScriptUploadFailResult result = new ScriptUploadFailResult();
                    result.setContent(script.getContent());
                    result.setQuestion(script.getQuestion());
                    result.setReason("超过数量限制，单词最多导入1000条");
                    result.setCategory1Name(script.getCategory1Name());
                    result.setCategory2Name(script.getCategory2Name());
                    rejectList.add(result);
                } else {
                    saveList.add(script);
                }
            }

            if (CollectionUtils.isNotEmpty(saveList)) {
                scriptManager.batchSave(saveList);
            }
        }

        result.setBatchId(hostTimeIdg.generateId().toString());
        result.setFailTotal(rejectList.size());
        result.setSuccessTotal(saveList.size());
        if (CollectionUtils.isNotEmpty(rejectList)) {
            log.info("batch id={}, reject list={}", result.getBatchId(), JsonUtils.toJSON(rejectList));
            redisUtil.set("script_batch_" + result.getBatchId(), JsonUtils.toJSON(rejectList), 60 * 60);
        }
    }

    private Boolean preCheck(CntScriptBatchBO param) {
        String reason = "";
        CategoryPO category1 = categoryMap.get(param.getCategory1Name());
        CategoryPO category2 = categoryMap.get(param.getCategory2Name());

        if (StringUtils.isBlank(param.getQuestion())) {
            reason = "问题不可为空";
        } else if (StringUtils.isBlank(param.getContent())) {
            reason = "话术内容不可为空";
        } else if (param.getQuestion().length() > 15) {
            reason = "问题最多支持15个汉字";
        } else if (!hasCategory(param.getCategory1Name())) {
            if (hasCategory(param.getCategory2Name())) {
                reason = "未指定一级分类";
            }
        } else {
            if (Objects.isNull(category1)) {
                reason = "一级分类不存在";
            } else {
                if (hasCategory(param.getCategory2Name()) && Objects.isNull(category2)) {
                    reason = "二级分类不存在";
                } else if (Objects.nonNull(category2) && !category1.getBizId().equals(category2.getParentId())) {
                    reason = "一级分类不包含二级分类";
                }
            }
        }

        if (StringUtils.isNotBlank(reason)) {
            ScriptUploadFailResult result = new ScriptUploadFailResult();
            result.setContent(param.getContent());
            result.setQuestion(param.getQuestion());
            result.setReason(reason);
            result.setCategory1Name(param.getCategory1Name());
            result.setCategory2Name(param.getCategory2Name());
            rejectList.add(result);
            return Boolean.FALSE;
        }
        if (Objects.nonNull(category1)) {
            param.setCategory1Id(category1.getBizId());
        }
        if (Objects.nonNull(category2)) {
            param.setCategory2Id(category2.getBizId());
        }

        return Boolean.TRUE;
    }

    private Boolean hasCategory(String categoryName) {
        return StringUtils.isNotBlank(categoryName) && !DEFAULT_CATEGORY_NAME.contains(categoryName);
    }

    @Data
    public static class ScriptUploadFailResult {
        private String question;

        private String content;

        private String category1Name;

        private String category2Name;

        private String reason;
    }
}
