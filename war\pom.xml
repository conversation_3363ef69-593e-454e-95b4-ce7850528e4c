﻿<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
	http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dl.dops.web</groupId>
        <artifactId>dl-dops-web</artifactId>
        <version>1.0.2-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>dl-dops-web-war</artifactId>
    <name>dl-dops-web-war</name>

    <dependencies>
        <dependency>
            <groupId>com.dl.dops.web</groupId>
            <artifactId>dl-dops-web-web</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dl.dops.web</groupId>
            <artifactId>dl-dops-web-job</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 单元测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
	</dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
<!--                    <executions>-->
<!--                        <execution>-->
<!--                            <id>default</id>-->
<!--                            <goals>-->
<!--                                <goal>build</goal>-->
<!--                            </goals>-->
<!--                        </execution>-->
<!--                    </executions>-->
                <configuration>
                    <repository>${entry.docker.url}/${project.groupId}/${project.artifactId}</repository>
                    <tag>${project.version}</tag>
                    <buildArgs>
                        <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                    </buildArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>