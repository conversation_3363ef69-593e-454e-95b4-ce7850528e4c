package com.dl.dops.biz.common.util;

import cn.hutool.json.JSONUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class CommonSecretUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonSecretUtil.class);

    /**
     * 对签名进行解密
     *
     * @param strSign
     * @return
     */
    public static Map<String, Object> unSecurity(String strSign, String secretKey) {
        if (StringUtils.isNotBlank(strSign) && StringUtils.isNotBlank(secretKey)) {
            String baseString = decrypt(strSign, secretKey);
            return getParams(baseString);
        }
        return new HashMap<>();
    }

    public static void main(String[] args) throws Exception {
        String encryptStr = "d3ql8fUXJhqY9b%2F0tK2RTO97X43ZNmV1V1daX0WjI4NG%2BtF1KrZJ1iXQEYmTgm3QmpzGxEMTeDL1hYkSPH%2F1ry8A6nrM0vLrDE00Yf6GhPI%2FIqN2CM5cvFpMxVUHUIWQUmCAILDohO8%3D";
        //        //将base64字符串进行DES加密
        String key = "7da6e15e1e674bf987ea6983de5e289f";
        //        //将DES加密后的字符串URLEncode
        String decodeStr = URLDecoder.decode(encryptStr, "UTF-8");

        Map<String, Object> stringStringMap = unSecurity(decodeStr, key);

        System.out.println(stringStringMap);
    }

    public static Map<String, Object> getParams(String baseString) {
        Map<String, Object> map = jsonParser(baseString);
        if (map.isEmpty()) {
            map = urlParser(baseString);
        }
        return map;
    }

    private static Map<String, Object> jsonParser(String json) {
        Map<String, Object> tempMap = new HashMap<>();
        if (StringUtils.isBlank(json)) {
            return tempMap;
        }
        try {
            Map<Object, Object> m = JSONUtil.toBean(json, Map.class);
            if (!m.isEmpty()) {
                for (Map.Entry<Object, Object> entry : m.entrySet()) {
                    tempMap.put(entry.getKey().toString(), entry.getValue().toString());
                }
            }
        } catch (Exception e) {
            LOGGER.warn("jsonParser error:{}", json, e);
            return tempMap;
        }

        return tempMap;
    }

    private static Map<String, Object> urlParser(String baseString) {
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isBlank(baseString)) {
            return map;
        }
        try {
            for (String param : baseString.split("&")) {
                String[] keyValue = param.split("=");
                if (keyValue.length == 1) {
                    map.put(keyValue[0], "");
                } else {
                    map.put(keyValue[0], keyValue[1]);
                }
            }
        } catch (Exception e) {
            LOGGER.warn("urlParser error:{}", baseString, e);
            return map;
        }
        return map;

    }

    /**
     * DES 解密
     *
     * @param encryptData 加密串
     * @param secret      加密/解密 secret
     * @return
     */
    private static String decrypt(String encryptData, String secret) {
        try {
            // --通过base64,将字符串转成byte数组
            byte[] bytesrc = Base64.decodeBase64(encryptData.getBytes());
            // --解密的key
            DESedeKeySpec dks = new DESedeKeySpec(secret.getBytes(StandardCharsets.UTF_8));
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DESede");
            SecretKey securekey = keyFactory.generateSecret(dks);

            // --Chipher对象解密
            Cipher cipher = Cipher.getInstance("DESede/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, securekey);
            byte[] retByte = cipher.doFinal(bytesrc);

            return new String(retByte);
        } catch (Exception e) {
            LOGGER.warn("解密异常:", e);
            return "";
        }

    }

    /**
     * DES 加密
     *
     * @param originData 原始数据,未加密
     * @param secret     加密秘钥
     * @return
     */
    public static String encrypt(String originData, String secret) {
        try {
            DESedeKeySpec dks = new DESedeKeySpec(secret.getBytes(StandardCharsets.UTF_8));
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DESede");
            SecretKey securekey = keyFactory.generateSecret(dks);

            Cipher cipher = Cipher.getInstance("DESede/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, securekey);
            byte[] b = cipher.doFinal(originData.getBytes());
            return new String(Base64.encodeBase64(b), StandardCharsets.UTF_8).replaceAll("\r", "").replaceAll("\n", "");

        } catch (Exception e) {
            LOGGER.warn("加密异常:", e);
            return "";
        }

    }

}
