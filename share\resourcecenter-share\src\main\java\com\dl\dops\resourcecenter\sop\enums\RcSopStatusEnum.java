package com.dl.dops.resourcecenter.sop.enums;

import java.util.Objects;

public enum RcSopStatusEnum {
    DRAFT(0, "草稿"),
    RELEASED(8, "已发布"),
    CANCEL_RELEASED(9, "取消发布");

    private Integer code;

    private String desc;

    RcSopStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RcSopStatusEnum parse(Integer status) {
        if (Objects.isNull(status)) {
            return null;
        }
        for (RcSopStatusEnum statusEnum : RcSopStatusEnum.values()) {
            if (statusEnum.getCode().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
