package com.dl.dops.system.web.util;

import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.system.manager.role.RoleManager;
import com.dl.dops.biz.system.manager.user.UserManager;
import com.dl.dops.biz.system.manager.user.dto.BasicUserDTO;
import com.dl.dops.biz.system.manager.user.dto.UserDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Set;

@Component
public class AccountCompent {

    @Autowired
    private RoleManager roleManager;

    @Autowired
    private UserManager userManager;

    /**
     * @description 获取当前子账户Id
     * <AUTHOR>
     * @createTime 2020/12/28 15:39
     */
    public BasicUserDTO getUser() {
        return userManager.parseJwtToken(getToken());
    }

    public Set<Long> getRoleIds() {
        return roleManager.findUserRoleByUserId(getUser().getUserId());
    }

    private String getToken() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            String token = request.getHeader(Const.TOKEN_HEADER_NAME);
            return token.substring(token.indexOf(" ") + 1);
        }
        return null;
    }

    public UserDTO getCurrentDetail() {
        return userManager.findUserDetail(getUser().getUserId());
    }

}