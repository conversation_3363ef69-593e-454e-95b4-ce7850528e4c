package com.dl.dops.biz.common.forest.magicvideo.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-31 15:15
 */
@Data
public class SubjectMatterDTO {

    /**
     * 业务id
     */
    private String bizId;

    /**
     * 题材名称
     */
    private String name;

    /**
     * 标题名称
     */
    private String title;

    /**
     * 图片链接地址
     */
    private String imgUrl;

    /**
     * xmind文件地址
     */
    private String xmindUrl;

    /**
     * 简介
     */
    private String intro;

    /**
     * prompt
     */
    private String prompt;

    /**
     * excel链接地址
     */
    private String excelUrl;

    /**
     * 题材级别，1-一级题材，2-二级题材，3-三级题材，4-四级题材
     */
    private Integer level;

    /**
     * 父级题材bizId
     */
    private String parentId;

    /**
     * 是否有子节点
     */
    private Integer isHaveChild;

    /**
     * json文件地址
     */
    private String jsonUrl;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 修改人名称
     */
    private String modifyName;

    private String createBy;

    private String modifyBy;

    /**
     * 修改时间
     */
    private Date modifyDt;
}
