package com.dl.dops.biz.resourcecenter.dal.category.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("category")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CategoryPO extends BasePO {

    private static final long serialVersionUID = 6398182063125294500L;
    Long id;

    Long bizId;

    Integer categoryType;

    String name;

    Integer categoryLevel;

    Long parentId;

    String link;

    Integer isDeleted;

}