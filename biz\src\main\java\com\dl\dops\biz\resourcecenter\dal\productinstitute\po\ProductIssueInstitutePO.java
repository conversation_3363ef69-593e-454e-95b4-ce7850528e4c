package com.dl.dops.biz.resourcecenter.dal.productinstitute.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import java.io.Serializable;
import lombok.Data;

/**
 * 产品发行机构表
 * @TableName product_issue_institute
 */
@TableName(value ="product_issue_institute")
@Data
public class ProductIssueInstitutePO extends BasePO implements Serializable {
    /**
     * 表ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 产品机构id
     */
    private Long instituteId;

    /**
     * 外部代码，用于映射业务，关联金融数据，获取相关资讯
     */
    private String extCode;

    /**
     * 机构全名
     */
    private String fullName;

    /**
     * 机构简称
     */
    private String abbrName;

    /**
     * 机构logo小图标
     */
    private String logoIconUrl;

    /**
     * 机构logo高清图
     */
    private String logoHighResUrl;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProductIssueInstitutePO other = (ProductIssueInstitutePO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getInstituteId() == null ? other.getInstituteId() == null : this.getInstituteId().equals(other.getInstituteId()))
            && (this.getExtCode() == null ? other.getExtCode() == null : this.getExtCode().equals(other.getExtCode()))
            && (this.getFullName() == null ? other.getFullName() == null : this.getFullName().equals(other.getFullName()))
            && (this.getAbbrName() == null ? other.getAbbrName() == null : this.getAbbrName().equals(other.getAbbrName()))
            && (this.getLogoIconUrl() == null ? other.getLogoIconUrl() == null : this.getLogoIconUrl().equals(other.getLogoIconUrl()))
            && (this.getLogoHighResUrl() == null ? other.getLogoHighResUrl() == null : this.getLogoHighResUrl().equals(other.getLogoHighResUrl()))
            && (this.getCreateDt() == null ? other.getCreateDt() == null : this.getCreateDt().equals(other.getCreateDt()))
            && (this.getModifyDt() == null ? other.getModifyDt() == null : this.getModifyDt().equals(other.getModifyDt()))
            && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
            && (this.getModifyBy() == null ? other.getModifyBy() == null : this.getModifyBy().equals(other.getModifyBy()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getInstituteId() == null) ? 0 : getInstituteId().hashCode());
        result = prime * result + ((getExtCode() == null) ? 0 : getExtCode().hashCode());
        result = prime * result + ((getFullName() == null) ? 0 : getFullName().hashCode());
        result = prime * result + ((getAbbrName() == null) ? 0 : getAbbrName().hashCode());
        result = prime * result + ((getLogoIconUrl() == null) ? 0 : getLogoIconUrl().hashCode());
        result = prime * result + ((getLogoHighResUrl() == null) ? 0 : getLogoHighResUrl().hashCode());
        result = prime * result + ((getCreateDt() == null) ? 0 : getCreateDt().hashCode());
        result = prime * result + ((getModifyDt() == null) ? 0 : getModifyDt().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getModifyBy() == null) ? 0 : getModifyBy().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", instituteId=").append(instituteId);
        sb.append(", extCode=").append(extCode);
        sb.append(", fullName=").append(fullName);
        sb.append(", abbrName=").append(abbrName);
        sb.append(", logoIconUrl=").append(logoIconUrl);
        sb.append(", logoHighResUrl=").append(logoHighResUrl);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}