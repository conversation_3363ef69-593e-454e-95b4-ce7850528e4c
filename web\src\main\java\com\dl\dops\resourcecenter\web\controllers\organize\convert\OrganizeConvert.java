package com.dl.dops.resourcecenter.web.controllers.organize.convert;

import com.dl.dops.biz.resourcecenter.dal.organize.po.OrganizePO;
import com.dl.dops.resourcecenter.web.controllers.organize.vo.OrganizeVO;

import java.util.Objects;

public class OrganizeConvert {

    public static OrganizeVO convertBO2VO(OrganizePO cate) {
        if (Objects.isNull(cate)) {
            return null;
        }
        OrganizeVO organizeVO = new OrganizeVO();
        organizeVO.setOrganizeId(cate.getOrganizeId());
        organizeVO.setOrganizeName(cate.getOrganizeName());
        organizeVO.setLogoIconUrl(cate.getLogoIconUrl());
        organizeVO.setLogoHighResUrl(cate.getLogoHighResUrl());
        organizeVO.setSort(cate.getSort());
        return organizeVO;
    }

}
