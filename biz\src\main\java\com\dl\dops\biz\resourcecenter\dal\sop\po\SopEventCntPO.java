package com.dl.dops.biz.resourcecenter.dal.sop.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

@Data
@TableName("sop_event_cnt")
public class SopEventCntPO extends BasePO {
    private static final long serialVersionUID = -174348377082664291L;
    @TableId
    public Long id;

    @TableField("event_id")
    private Long eventId;

    @TableField("cnt_id")
    private Long cntId;

    @TableField("cnt_type")
    private Integer cntType;

    @TableField("is_deleted")
    private Integer isDeleted;
}
