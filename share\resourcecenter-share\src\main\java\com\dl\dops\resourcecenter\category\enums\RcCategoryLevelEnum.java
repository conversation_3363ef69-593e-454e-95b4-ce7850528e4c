package com.dl.dops.resourcecenter.category.enums;

/**
 * @ClassName CategoryLevelEnum
 * @Description 分类级别枚举类
 * <AUTHOR>
 * @Date 2022/4/7 11:17
 * @Version 1.0
 **/
public enum RcCategoryLevelEnum {

    LEVEL_ONE(0, "一级分类"),

    LEVEL_TWO(1, "二级分类");

    private Integer code;
    private String desc;

    RcCategoryLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
