package com.dl.dops.resourcecenter.tag;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-08 10:17
 */
public enum TagDefaultEnum {

    ALL("-1", "全部"),

    UNTAG("0", "无标签");

    private String code;
    private String desc;

    TagDefaultEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
