package com.dl.dops.resourcecenter.pack.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel("服务包分页")
public class RcPackPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("服务包id")
    private String packId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态描述")
    private String statusDesc;

    @ApiModelProperty("场景概述")
    private String sceneOverview;

    @ApiModelProperty("所属行业")
    private Integer domain;

    @ApiModelProperty("所属行业名")
    private String domainName;

    /**
     * 场景 1-助你营，2-助你拍
     *
     * @see
     */
    @ApiModelProperty("场景 1-助你营，2-助你拍")
    private Integer scene;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date modifyTime;

}
