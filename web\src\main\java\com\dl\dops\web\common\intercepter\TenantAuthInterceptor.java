package com.dl.dops.web.common.intercepter;

import com.dl.dops.biz.common.annotation.NotLogin;
import com.dl.dops.biz.common.properties.WebIgnoreProperties;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.common.util.TenantAuthUtil;
import com.dl.dops.biz.resourcecenter.manager.tenantauth.TenantAuthManager;
import com.dl.dops.resourcecenter.common.RcConst;
import com.dl.dops.resourcecenter.tenantauth.dto.RcTenantAuthTokenDTO;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import io.jsonwebtoken.lang.Collections;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-22 10:28
 */
@Component
public class TenantAuthInterceptor implements HandlerInterceptor, Ordered {

    @Autowired
    private WebIgnoreProperties properties;
    @Autowired
    private TenantAuthUtil tenantAuthUtil;
    @Autowired
    private OperatorUtil operatorUtil;
    @Autowired
    private TenantAuthManager tenantAuthManager;

    private boolean isIgnoreUrl(HttpServletRequest request) {
        String url = request.getRequestURI();
        List<String> urlPatterns = properties.getUrls();
        if (Collections.isEmpty(urlPatterns)) {
            return false;
        }
        for (String urlPattern : urlPatterns) {
            if (TenantAuthUtil.getPathMatcher().match(urlPattern, url)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        //判断是否为B端，非B端请求不走此判断
        if (!TenantAuthUtil.isFromTenantAuth(request) && !TenantAuthUtil.isFromB(request)) {
            return true;
        }

        //判断是否忽略url
        if (isIgnoreUrl(request)) {
            return true;
        } else if (handler instanceof ResourceHttpRequestHandler) {
            //静态资源
            return false;
        }

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        //2、判断 NotLogin，有则跳过认证
        if (method.isAnnotationPresent(NotLogin.class)) {
            NotLogin loginToken = method.getAnnotation(NotLogin.class);
            if (loginToken.required()) {
                return true;
            }
        }

        // 从 http 请求头中取出 token
        String token = request.getHeader(RcConst.TENANT_AUTH_TOKEN_HEADER_NAME);
        String tenantCode = request.getHeader(RcConst.TENANT_CODE_NAME);
        //如果token或者租户号为空的情况下，禁止访问
        if (StringUtils.isBlank(token) || StringUtils.isBlank(tenantCode)) {
            //非法token
            throw BusinessServiceException.getInstance("无权访问资源中心");
        }

        //解密并校验token
        RcTenantAuthTokenDTO rcTenantAuthTokenDTO = tenantAuthManager.encrptAndValidToken(token, tenantCode);
        tenantAuthUtil.init(rcTenantAuthTokenDTO.getTenantCode());
        //因为B端资源中心页面尚未改造，还是走的老页面老逻辑。而dops后台只做了通用素材的页面，服务包、sop的都还没做，也没有资源投入了（因为没有直接经济效益吧）
        //所以这里兼容了下，允许B端DL租户操作资源中心。
        operatorUtil.init(rcTenantAuthTokenDTO.getUserId(),rcTenantAuthTokenDTO.getUserName());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) {
        tenantAuthUtil.remove();
        operatorUtil.remove();
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
