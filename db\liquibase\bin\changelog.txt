Liquibase Core Changelog
===========================================

Changes in version 4.11.0 (2022.05.19)

## Enhancements
* Added new liquibase.monitorPerformance setting for CLI by @nvoxland in https://github.com/liquibase/liquibase/pull/2599
* Updated Installer JDK to 17 by @nvoxland in https://github.com/liquibase/liquibase/pull/2602
* Create internal/lib dir by @nvoxland in https://github.com/liquibase/liquibase/pull/2850

## JDBC Driver and Third-Party Library Updates
* Upgraded picocli from 4.6.1 to 4.6.3 by @dependabot in https://github.com/liquibase/liquibase/pull/2518
* Upgraded jaxb libraries to the newest 2.3.x versions by @nvoxland in https://github.com/liquibase/liquibase/pull/2793
* Upgraded snakeyaml from 1.27 to 1.30 by @dependabot in https://github.com/liquibase/liquibase/pull/2498

## Fixes
* Do not throw a confusing error if a datatype specified as a changelog parameter is not expanded by @nvoxland in https://github.com/liquibase/liquibase/pull/2772
* Fixed SQLite not correctly snapshotting tables with underscores in the name by @nvoxland in https://github.com/liquibase/liquibase/pull/2778
* Fixed local XSD lookup by @nvoxland in https://github.com/liquibase/liquibase/pull/2830
* Test System Framework: add support for "liquibase.sdk.testSystem.skip" property by @StevenMassaro in https://github.com/liquibase/liquibase/pull/2838
* stop database containers when spock execution ends by @StevenMassaro in https://github.com/liquibase/liquibase/pull/2843
* Refactor start-h2 logic by @nvoxland in https://github.com/liquibase/liquibase/pull/2597
* Support default values in the format `(0)::real` by @nvoxland in https://github.com/liquibase/liquibase/pull/2836
* Fix handling of defaultSchemaName on Postgresql by @nvoxland in https://github.com/liquibase/liquibase/pull/2812
* Introduce generateStatementsFromRows for extensions by @fbiville in https://github.com/liquibase/liquibase/pull/2686
* The type of attributes "ordered" and "cycle" should be boolean by @DartVerder in https://github.com/liquibase/liquibase/pull/2725
* improve Command Test filtering logic by @StevenMassaro in https://github.com/liquibase/liquibase/pull/2853
* Remove custom log message filtering by @nvoxland in https://github.com/liquibase/liquibase/pull/2777
* Spring Boot: better support relativeToChangelogfile when ResourceLoaders return FilteredReactiveWebContextResources by @erik-meuwese-topicus in https://github.com/liquibase/liquibase/pull/2758
* Fix ForeignKeyComparator when foreign key table is not known by @joserebelo in https://github.com/liquibase/liquibase/pull/2565

**Full Changelog**: https://github.com/liquibase/liquibase/compare/v4.10.0...v4.11.0


Changes in version 4.10.0 (2022.05.04)

## Breaking Change

Upgraded mssql driver to 10.2.0 by @nvoxland in https://github.com/liquibase/liquibase/pull/2790

End User Impact:  The driver changed the encryption default from "false" to "true" between 8.x and 10.x. If you have a self-signed certificate in your database, you must do one of the following: add `encrypt=false`; add `trustServerCertificate=true`; or add the server certificate to the java trusted certificate list. For production systems, Liquibase recommends against using self-signed certificates without adding the server certificate to the Java keystore. For more information on installing the trusted certificate, see https://docs.microsoft.com/en-us/sql/database-engine/configure-windows/manage-certificates?view=sql-server-ver15

## Security Updates
Upgraded postgresql from 42.3.2 to 42.3.4  to address [CVE-2022-26520](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2022-26520) by @dependabot in https://github.com/liquibase/liquibase/pull/2769
* Vulnerability introduced in org.postgresql:postgresql@42.3.2
* Fixed in org.postgresql:postgresql@42.3.3

## JDBC Driver and Third-Party Library Updates
* Upgraded oracle driver to ******** by @nvoxland in https://github.com/liquibase/liquibase/pull/2791
* Upgraded jcc from ******** to ******** by @dependabot in https://github.com/liquibase/liquibase/pull/2421
* Upgraded mssql driver to 10.2.0 by @nvoxland in https://github.com/liquibase/liquibase/pull/2790
* Upgraded  h2 from 2.1.210 to 2.1.212 by @dependabot in https://github.com/liquibase/liquibase/pull/2740
* Upgraded sqlite-jdbc from 3.34.0 to ******** by @dependabot in https://github.com/liquibase/liquibase/pull/2412
* Upgraded jaybird from 4.0.3.java8 to 4.0.6.java8 by @dependabot in https://github.com/liquibase/liquibase/pull/2802
* Upgraded hsqldb from 2.4.0 to 2.5.2 by @nvoxland in https://github.com/liquibase/liquibase/pull/2797
* Updated information in "licenses" directory by @nvoxland in https://github.com/liquibase/liquibase/pull/2795
* Upgraded mariadb driver to 3.0.4 by @nvoxland in https://github.com/liquibase/liquibase/pull/2789


## Enhancements
* Allow CompositeResourceAccessor to have a non-fixed size list of ResourceAccessor so that the list can be added to/removed from by @wwillard7800 in https://github.com/liquibase/liquibase/pull/2650
* Correctly handle null class package names in the logging system by @nvoxland in https://github.com/liquibase/liquibase/pull/2763
* Handle URLs correctly in error conditions DAT-9882 by @wwillard7800 in https://github.com/liquibase/liquibase/pull/2709
* Validate formatted SQL changelog for invalid patterns DAT-7721 by @wwillard7800 in https://github.com/liquibase/liquibase/pull/2761
* Quality checks for Maven - `checks run` and `checks show` goals


## Fixes
* Fix EnterpriseDB database recognition by @nvoxland in https://github.com/liquibase/liquibase/pull/2767
* Fix start failure with Nullpointed exception when Environment contains empty string Key (fix Issue 2631) by @feltonma91 in https://github.com/liquibase/liquibase/pull/2636
* Fix createTable on Mysql not preserving primary key information when remarks also defined on the column by @nvoxland in https://github.com/liquibase/liquibase/pull/2752
* Updated information in "licenses" directory by @nvoxland in https://github.com/liquibase/liquibase/pull/2795


**Full Changelog**: https://github.com/liquibase/liquibase/compare/v4.9.1...v4.10.0


Changes in version 4.9.1 (2022.03.25)

## Liquibase 4.9.1 patch release
Please report any issues to [https://github.com/liquibase/liquibase/issues](https://github.com/liquibase/liquibase/issues).

## Notable Changes

[[PR#2666](https://github.com/liquibase/liquibase/pull/2666)] [[nvoxland](https://github.com/nvoxland)] Liquibase 4.9.1 release is a patch release that fixes the `MANIFEST.MF` location in the `liquibase.jar` file for tools that require the `MANIFEST.MF` file to be the first entry in the jar archive.
If the `MANIFEST.MF` file is not the first entry, `java.util.jar.JarInputStream#getManifest()` reading the `MANIFEST.MF` file will not return the manifest information appropriately. The PR fixes issue [#2648](https://github.com/liquibase/liquibase/issues/2648).

[[Issue#2672](https://github.com/liquibase/liquibase/issues/2672)] Addresses the remaining OSGi-related packaging problems to fully restore Liquibase support in OSGi environments.

## Fixes
[[PR#2651](https://github.com/liquibase/liquibase/pull/2651)] Fixed the issue with a malicious software alert for the Liquibase macOS Installer preventing from proceeding with the installation. [DAT-9524]

[[PR#2643](https://github.com/liquibase/liquibase/pull/2643)] Fixed an issue in snapshot on PostgreSQL where non-integer datatypes that used a default value with a sequence would not be handled correctly [DAT-8670]

[[PR#2691](https://github.com/liquibase/liquibase/pull/2691)] Fixed invalid JSON in the example-changelog.json file [DAT-9893]

Changes in version 4.9.0 (2022.03.17)

# Notable Changes

Liquibase  4.9.0 introduces the following functionality:
- [PRO] Quality checks output in a JSON format
- [PR#2558] [kavya-shastri] The extension of the usage of the Liquibase global configuration for the `secureParsing` parameter to prevent the entity resolver from falling back to looking on the network for missing XSDs. If you reference an XSD other than one of Liquibase, Liquibase will no longer automatically download the XSD from the internet and use it. The default value for `secureParsing`  is `true`, but you can override it in any of the Liquibase configuration locations. For more information, see the Example Changelogs: XML Format documentation.
- [PR#2529] [Krzysztof Sierszeń] [LB-1781] The `uniqueConstraintExists` precondition to check for the existence of unique constraints before running the update. You can check the constraint by constraint name or columns:
<preConditions>
    <uniqueConstraintExists constraintName="uq_constraint_name" tableName="test_table"/>
</preConditions>
<preConditions>
    <uniqueConstraintExists tableName="test_table" columnNames="first_name, last_name"/>
</preConditions>
- [PR#2339] [[nvoxland] [Toukite] The support for running the Liquibase Servlet listener in application servers built on the Jakarta Servlet specification and not the Java Servlet specification. To use the new listener, specify the following tag:
<listener>
    <listener-class>liquibase.integration.servlet.LiquibaseJakartaServletListener</listener-class>
</listener>
The PR fixes issue #2298.
## Enhancements
- [PR#2434] [Balázs Dési] Added the Maven `validate` goal to `maven-liquibase-plugin` to provide access to the existing Liquibase `validate` command and be available not only in CLI, but also in Maven
- Added the ability to use `sqlcmd` on an instance with multiple databases
For more information, see the Using the SQLCMD integration and runWith attribute with Liquibase Pro and MSSQL Server documentation.
- Added support for Liquibase, Spring Boot, and SQL Plus run with Kerberos
- [PR#2561] [Wesley Willard] Added the `--schemas` argument to the `snapshot` command to be able to compare a multi-schema database to its snapshot
- [PR#2606] [Wesley Willard] Added a best practice message for the `generate-changelog` command so that you can check if the `splitStatements` attribute works for your environment when generating formatted SQL changelogs [DAT-9327]
- Added the `StripComments` attribute for the `SQLUserDefinedPatternCheck` quality check to choose the code that the `SQLUserDefinedPatternCheck` searches, including the option for the commented code. The `StripComments` attribute strips comments from SQL before searching for the string. The default value is `N` (no). [DAT-9098]
- [PR#2273] [Tsvi Zandany] Added the autocomplete option for quality checks commands running on macOS
## Fixes
- [PR#2542] [nvoxland] Fixed the `--should-snapshot-data` CLI parameter to be preserved when the `--data-output-directory` property is not specified in the command
- [PR#2330] [Inane Mohammed] Fixed the issue with checking the `referenceSnapshot` parameter instead of `referenceDatabase` for the potential null value
- [PR#2521] [Steven Massaro] Added a label, context, and comment to the `example-changelog.sql` file that comes with the Liquibase installation package
- [PR#2556] [nvoxland] Fixed the CockroachDB processes so that Liquibase checks the URL first rather than proceeding with SQL calls
- [PR#2569] [Clarence Dimitri CHARLES] Made `FormattedSqlChangeLogParser` to be reusable for extensions
- [PR#2377] [devorgpl] Fixed the issue with updating statements when the `onlyUpdate` attribute is `false` in the `loadUpdateData` Change Type for HSQL
- [PR#1908] [Emiliano Capoccia] Fixed the issue with retrieving the CockroachDB version. As part of the fix, Liquibase generates version-specific autoincrement SQL statements for CockroachDB identity columns:
   - `SMALLSERIAL`, `SERIAL`, and `BIGSERIAL` for versions earlier than 21.2
   - `GENERATED BY DEFAULT AS IDENTITY` for version 21.2 or later
- [PR#2135] [PR#2133] [Saucistophe] [mkobylarz] Fixed the issue with the `endDelimiter` attribute  not being respected during the Liquibase update. The fix for `endDelimiter` does not break the handling of trailing comments in SQL statements. [LB-2186]
- [PR#2573] [nvoxland] Fixed the quoting for the MANIFEST.MF file for correct versioning
- [PR#2572] [Steven Massaro] Fixed the issue with generating changelogs for PostgreSQL columns that use `SET DEFAULT`
- [PR#2589] [nvoxland] [Marcel Blonk] Fixed the MD5 checksums generated by older versions of Liquibase to be correctly updated in the DATABASECHANGELOG table when a more recent version of Liquibase executes against the database. The PR fixes issue #2580.
- [PR#2592] [nvoxland] Added support for the double precision type in MySQL. The PR fixes issue #2337.
- [PR#1894] [KushnirykOleh] Fixed the time data type to be precise for PostgreSQL. The PR fixes issue #1774.
- [PR#2387] [Jenson3210] Fixed the handling of date and time in the `loadData` Change Type
- [PR#2566] [nvoxland] Updated OpenCSV from 5.5.2 to 5.6


Changes in version 4.8.0 (2022.02.23)

Notable Changes

Liquibase 4.8.0 introduces a built-in SQLCMD integration that allows you to specify the runwith paramter sqlcmd custom executor to process
complex SQL for MSSQL Server. Liquibase provides the liquibase.sqlcmd.conf file to pass arguments to your executor when running Liquibase Pro.

For new and existing Liquibase Hub users, Liquibase 4.8.0 introduces the init hub command, used in Hub’s Getting Started on-boarding.
Users can get defaults and changelog files setup, working, and registered to Hub with just this one command.

Enhancements
- Implemented the SimpleObjectConstructor interface for DB2 on z/OS [DAT-8580]
- Implemented the init hub command to complete Liquibase Hub onboarding
- Included the CLI instructions on how to use the properties file with a nonstandard name when running the init project subcommand [DAT-9041]
- Added to init start-h2 a clearer message when the H2 database driver is specified, but there is no connection detected. [DAT-8992]
- Added validation errors for the enableCheckConstraint, disableCheckConstraint, dropPackage, dropPackageBody Change Types [DAT-9017]
- [PR#2367] [Mike Olivas] Added example rollback scripts to the example-changelog.sql file [LB-2220]
- [PR#1648] [Daniel Gray] Improved the exception error message for the customChange node with no class attribute [LB-1144]
- [PR#2222] [msimko81] Added the offline mode support for the rollback-sql <tag> operation [LB-2198]

Fixes

- Fixed the renameSequence Change Type generating an uppercase SQL instead of lowercase and causing failures with case-sensitive databases [LB-1763
- Fixed the generate-changelog and diff commands to show and generate a proper SQL with autoincrement for PostgreSQL [DAT-8779]
- [PR#1320] [Anatoly S] Fixed the issue with the generated SQL incorrectly displaying the numero sign (No) [LB-562]
- [PR#2139] [wziebicki] Improved the UniqueConstraintSnapshotGenerator class to add a table name to the Oracle database query so that the cache key can contain it, and the unique constraint can be read from cache [LB-2206]
- [PR#2335] [erzsebet] Removed duplicated DATABASECHANGELOGLOCK SQL statements when running the update-sql command against a new database [LB-2208]
- [PR#1894] [KushnirykOleh] Made the time data type precise for PostgreSQL [LB-1798]•[PR#2190] [Richard Bradley] [Hannu Hartikainen] [VlasyukA] [Nathan Voxland] Fixed the issue with locking database if no changelogs are needed to run and no updates are pending. The services can proceed in parallel. [LB-2203]
- New Test System management by @nvoxland in https://github.com/liquibase/liquibase/pull/2312
- Remove duplicate databasechangeloglock SQL when running update-sql against a new database by @nvoxland in https://github.com/liquibase/liquibase/pull/2335
- UniqueConstraintSnapshotGenerator - Add table name to OracleDB query by @wziebicki in https://github.com/liquibase/liquibase/pull/2139
- CORE-3326 Numero sign is a symbol in Russian by @tolix in https://github.com/liquibase/liquibase/pull/1320
- Allow a custom executor to be specified through a property on existing Executor implementations DAT-7531 by @wwillard7800 in https://github.com/liquibase/liquibase/pull/2374
- Make ClassLoaderResourceAccessor implement Closable by @Delir4um in https://github.com/liquibase/liquibase/pull/2308
- Liquibase Responsible Disclosure Policy by @kristyldatical in https://github.com/liquibase/liquibase/pull/2435
- use lowercase sp_rename function in MSSQL (LB-1763) by @StevenMassaro in https://github.com/liquibase/liquibase/pull/2366
- Added example rollback scripts by @molivasdat in https://github.com/liquibase/liquibase/pull/2367
- Bump junit from 4.12 to 4.13.1 in /liquibase-extension-testing by @dependabot in https://github.com/liquibase/liquibase/pull/1827
- Bump maven-surefire-plugin from 2.22.1 to 2.22.2 by @dependabot in https://github.com/liquibase/liquibase/pull/2427
- Bump testcontainers-bom from 1.16.2 to 1.16.3 by @dependabot in https://github.com/liquibase/liquibase/pull/2467
- Bump mockito-core from 3.3.3 to 3.12.4 by @dependabot in https://github.com/liquibase/liquibase/pull/2470
- Bump maven-jar-plugin from 3.1.1 to 3.2.2 by @dependabot in https://github.com/liquibase/liquibase/pull/2458
- Bump mockito-inline from 3.8.0 to 3.12.4 by @dependabot in https://github.com/liquibase/liquibase/pull/2465
- Bump surefire-junit4 from 2.22.1 to 2.22.2 by @dependabot in https://github.com/liquibase/liquibase/pull/2466
- Bump objenesis from 2.1 to 2.6 by @dependabot in https://github.com/liquibase/liquibase/pull/2468
- Bump slf4j-jdk14 from 1.7.33 to 1.7.35 by @dependabot in https://github.com/liquibase/liquibase/pull/2475
- Bump assertj-core from 3.13.2 to 3.22.0 by @dependabot in https://github.com/liquibase/liquibase/pull/2474
- Bump maven-resources-plugin from 3.0.2 to 3.2.0 by @dependabot in https://github.com/liquibase/liquibase/pull/2473
- Bump maven-verifier from 1.6 to 1.7.2 by @dependabot in https://github.com/liquibase/liquibase/pull/2472
- Added new Quality Checks command and subcommands (Mac specifict) by @szandany in https://github.com/liquibase/liquibase/pull/2273
- Don't repackage all org.apache.- classes. by @nvoxland in https://github.com/liquibase/liquibase/pull/2429
- Log exceptions in CDILiquibase before rethrowing them by @nvoxland in https://github.com/liquibase/liquibase/pull/2397
- Bump maven-assembly-plugin from 3.1.1 to 3.3.0 by @dependabot in https://github.com/liquibase/liquibase/pull/2499
- Bump flatten-maven-plugin from 1.1.0 to 1.2.7 by @dependabot in https://github.com/liquibase/liquibase/pull/2495
- Bump gmavenplus-plugin from 1.6.2 to 1.13.1 by @dependabot in https://github.com/liquibase/liquibase/pull/2491
- Bump sonar-maven-plugin from 3.7.0.1746 to 3.9.1.2184 by @dependabot in https://github.com/liquibase/liquibase/pull/2490
- Bump robinraju/release-downloader from 1.2 to 1.3 by @dependabot in https://github.com/liquibase/liquibase/pull/2405
- Bump maven-plugin-plugin from 3.6.0 to 3.6.4 by @dependabot in https://github.com/liquibase/liquibase/pull/2497
- Bump maven-bundle-plugin from 5.1.2 to 5.1.4 by @dependabot in https://github.com/liquibase/liquibase/pull/2494
- [Snyk] Security upgrade com.h2database:h2 from 2.0.206 to 2.1.210 by @snyk-bot in https://github.com/liquibase/liquibase/pull/2392
- Enable FEATURE_SECURE_PROCESSING XML setting by default by @nvoxland in https://github.com/liquibase/liquibase/pull/2384
- Correctly set Bundle-Version in META-INF on release by @nvoxland in https://github.com/liquibase/liquibase/pull/2505
- Bump ant from 1.9.15 to 1.10.9 in /liquibase-core by @dependabot in https://github.com/liquibase/liquibase/pull/1808
- Bump postgresql from 42.2.12 to 42.3.2 by @dependabot in https://github.com/liquibase/liquibase/pull/2459
- Use path instead of absolutePath for resources by @liefke in https://github.com/liquibase/liquibase/pull/2122
- Bump junit from 4.12 to 4.13.2 by @dependabot in https://github.com/liquibase/liquibase/pull/2411
- Bump slf4j-jdk14 from 1.7.35 to 1.7.36 by @dependabot in https://github.com/liquibase/liquibase/pull/2506
- Added additional unicode letter points by @nvoxland in https://github.com/liquibase/liquibase/pull/2329
- Escape schema/tablenames in metadata.getTables() by @nvoxland in https://github.com/liquibase/liquibase/pull/2398
- Add offline mode support for rollbackSQL <tag> by @msimko81 in https://github.com/liquibase/liquibase/pull/2222
- Avoid NullPointerException when customChange has no "class" attribute by @danielthegray in https://github.com/liquibase/liquibase/pull/1648
- Support adding computed=true columns with no type set by @nvoxland in https://github.com/liquibase/liquibase/pull/2340
- Bump maven-javadoc-plugin from 3.3.1 to 3.3.2 by @dependabot in https://github.com/liquibase/liquibase/pull/2525
- DAT-9218  Added limited permission user for SQLPLUS integration tests by @wwillard7800 in https://github.com/liquibase/liquibase/pull/2480
- instruct surefire to read files with UTF-8 encoding by @StevenMassaro in https://github.com/liquibase/liquibase/pull/2541
- Bump mysql-connector-java from 6.0.6 to 8.0.28 by @dependabot in https://github.com/liquibase/liquibase/pull/2419
- Reworked license not found message by @suryaaki2 in https://github.com/liquibase/liquibase/pull/2371
- Fix for Github issue dealing with duplicate property keys by @wwillard7800 in https://github.com/liquibase/liquibase/pull/2351
- Use "GENERATED BY DEFAULT AS IDENTITY" for h2 2.0+ by @nvoxland in https://github.com/liquibase/liquibase/pull/2372
- MSSQL: Throw validation error if "ordered" is specified in createSequence/alterSequence by @nvoxland in https://github.com/liquibase/liquibase/pull/2549
- init hub command by @StevenMassaro in https://github.com/liquibase/liquibase/pull/2326 [DAT-8615]

Changes in version 4.7.1 (2022.01.21)

- Fix bug which makes Liquibase misidentify your database dialect if the string 'edb' is in your connection url. (#2364)
- Output changeset execution to UI like rollback does (#1932)
- Support MaxDB for sequences (#932)
- Include columnDataType in generated setColumnRemarks changesets (#2188)
- Use database time for changeloglock table (#2217)
- Mysql: support additional information "double" datatypes (#2293)
- Correctly handle old "liquibase tagExists myTag" style CLI structure (#2269)
- Correctly handle cacheSize in mariadb (#2270)
- Mark liquibase-maven-plugin logging configuration as deprecated (#2261)
- Added validation errors for SQLite and DB2z databases (#2359)

Changes in version 4.7.0 (2022.01.07)

Liquibase 4.7.0 introduces the init command with the project subcommand, which helps to build the necessary
configuration files for using Liquibase or, if you already use Liquibase, create new project files with minimal input.[DAT-8640]

Additionally, we have updated the Liquibase Getting Started tutorials and sample changelog files in the installation package.
From now on, you can use sample SQL, XML, JSON, and YAML changelog files with the corresponding changesets.

Enhancements
- [PRO] Improved the output of the rollback-one-update command
- [PR#2244] [szandany] Enabled the use of the jdbc:edb URL for Liquibase to recognize EnterpriseDB
- [PR#2225] [nvoxland] [catholic-indulgence-vaper] Enabled the use of lowercase sys.synonyms objects for the generate-changelog and diff-changelog commands in Liquibase and SQL Server. The PR fixes PR#2044.
- Added DB2 on z/OS database type and Java classes for Liquibase
- Upgrade h2 to newer version 2.0.206

Fixes
- Fixed the generateChangeLog wrong dependency order: the addPrimaryKey Change Type appeared before its base table in PostgreSQL [DAT-8256]
- Fixed the quality checks settings file to be created using both relative and absolute paths
- Fixed quality checks reporting only the first failure for a changeset to report all failures in outputs
- PR#2066] [smainz] Fixed the loadData Change Type to recognize CSV files when loading data with the path that contains a dot character ('.')
- [PR#2071] [nhumblot] [philhinco] Fixed the addNotNullConstraint Change Type causing the "Data too long" error message. The PR fixes PR#1054.
- [PR#2073] [smainz] Fixed the relativeToChangelogFile attribute for loadData, createProcedure, and createView Change Types
- [PR#2228] Handle the fact that FK names are not always unique
- [PR#2081] fix OSGI support (manifest, class loading)
- [PR#928] addColumn should support not-null constraint

Changes in version 4.6.2(2021.11.30)

Liquibase 4.6.2 introduces the multiple objects name quality check that allows you to define and check the naming pattern for several objects rather than one object.
For example, you do not want to have a dash in the table, column, and sequence names. With the multiple objects name check, you can specify a pattern to check for dashes
and choose tables, columns, and sequences as the objects to check. In version 4.6.2, the check is available for:
- Table
- Column
- Sequence
You can use two patterns for multiple object check:
- Multiple objects’ name in search:
    - Table name matches a pattern
    - Column name matches a pattern
    - Sequence name matches a pattern
- Multiple objects’ name is not in search:
    - Table name does not match a specific pattern
    - Column name does not match a specific pattern
    - Sequence name does not match a specific pattern

Enhancements
- [PRO] Added the ability to configure the DDL_LOCK_TIMEOUT parameter for Oracle without SQL Plus and define the connection timeout.
- Improved the output of the SqlUserDefinedPatternCheck quality check copy
- Changed the default value of the strict parameter to false. You can set the strict parameter as follows:
- liquibase.strict=false|true in your liquibase.properties file
- Improved the handling of nested changelog preconditions. The PR fixes PR#1624.
- Enabled the update operations to continue if the DATABASECHANGELOGLOCK table already exists, which helps to serialize all processes following schema creation.
- Added support for MySQL function indexes. The PR fixes PR#1626.
- Added support for the NOT NULL constraint with an initial default value in the addColumn Change Type
- Added a flag to control the logging of SQL statements and SQL execution output
- Added support for the remarks attribute in a primary key column tag for MariaDB
- Implemented the handling of the GENERATED ALWAYS AS clause while creating a table for PostgreSQL
- Added the ability for Liquibase to load an additional directory of liquibase_libs in the current working directory to the classpath. With the liquibase_libs directory located in the /lib directory, projects can have separate extensions, drivers, and versions specific to a project

Fixes
- [PRO] Fixed the snapshot command failing against CockroachDB with the Liquibase Pro license key applied
- Fixed the naming of the checks settings file shown as null if the file was corrupted
- Fixed the issue with Liquibase escaping the backslash in values stored in objects. The PR fixes PR#1598.
- Fixed the generate-changelog command to generate the NUMERIC[] data type for PostgreSQL correctly
- Fixed the parsing logic for YAMLChangelogParser and changeLogId in a Map. The PR fixes PR#1988.
- Fixed the --verbose flag used in the unexpected-changesets command. The PR fixes PR#1987.
- Fixed the exit code for the validate command running in automation to be 1 when there is an error. The PR fixes PR#1006.
- Fixed the dependency order for a changelog file with the addPrimaryKey Change Type generated by the generate-changelog command for PostgreSQL
- [PRO] Fixed the following environment variables LIQUIBASE_COMMAND_CHECKS_CONFIGURE_CHECKS_SETTINGS_FILE, LIQUIBASE_COMMAND_CHECKS_RUN_CHECKS_SETTINGS_FILE, and LIQUIBASE_COMMAND_CHECKS_SHOW_CHECKS_SETTINGS_FILE to be detected when running the quality checks commands [DAT-8546]
- Fixed quality checks reporting only the first failure in a changeset to report all failures found
- Fixed the time data type to be precise for PostgreSQL. The PR fixes PR#1774.
- Fixed the SQL generated by the alterSequence Change Type to be compatible with the Oracle, PostgreSQL, MariaDB, and MSSQL databases
- Fixed the Liquibase Cassandra extension failing when username and password are enabled
- Fixed the LOCKGRANTED column to include time zone information in the DATABASECHANGELOGLOCK table
- Fixed the classpath to include files with the relativeToChangelog=true parameter for Spring Boot

Changes in version 4.6.1(2021.11.04)

- Fixed the liquibase-pro-4.6.xsd

Changes in version 4.6.0(2021.11.04)

Windows and Mac/Linux Liquibase installers and the .tar.gz and .zip installation files include the most common JDBC drivers in the installation package:
 - DB2
 - Firebird
 - H2
 - HSQLDB
 - PostgreSQL
 - MariaDB
 - MSSQL
 - Oracle
 - SQLite

**Note:** Since Liquibase 4.6.0 provides driver jar files, the size of the installation package is bigger than the size of the installation package in earlier versions.
 - Quality checks have configurable severity and exit codes
 - The quality checks bulk-set command
 - New Quality checks
	- Static check: Warn when a table truncation is detected
	- Dynamic check: Warn when granting specific privileges
	- Static check: Warn when the `GRANT WITH ADMIN OPTION` statement is detected
	- Static check: Warn when the `GRANT WITH GRANT OPTION` statement is detected
 - The `liquibase.checks-settings.conf` file is unencoded in version 4.6.0 and later versions for easier inspection that allows you to resolve a merge conflict in source control. Liquibase backs up encoded files in the working directory, and you can still use previous versions by specifying the `--checks-settings-file` parameter in the CLI, as an environment variable, or as a property in the liquibase.properties file.
 - Changed the XSD default `errorOnMissingOrEmpty` attribute to `true` for the `includeAll` attribute in the XML _changelog_ file (#1960)
 - Added support for deferrable primary keys in the `createTable` Change Type. (#1597)
 - Added support for the `constraintName` attribute in the `dropNotNullConstraint` attribute. Updated the `addNotNullConstraint` rollback logic to pass the constraint name to the generated `dropNotNullConstraint` change (#1492)
 - Improved code and added tests in the `liquibase.util` package
 - Changed the default encoding to UTF-8. Added a new global `liquibase.fileEncoding` setting, which you can set as `--file-encoding=ASCII` in the CLI, `liquibase.fileEncoding: ASCII` in the liquibase.properties file, and `LIQUIBASE_FILE_ENCODING=ASCII` as an environment variable (#1445)

 - [PRO] Added a message to warn Liquibase Pro users when they have noncompliant Liquibase environment variables if the `strict` attribute is set to `true`
 - [PRO] Fixed the issue with Liquibase 4.4.0 and later versions to prompt if the Liquibase Pro license key is expired or invalid.
 - [PRO] Fixed Liquibase Pro SQL Plus _changesets_ failures with the Kerberos setup
 - [PRO] Fixed the `snapshot` command using for CockroachDB with Liquibase Pro license key
 - Fixed an issue with running quality checks on Windows when using a configuration file created on Linux/macOS
 - Fixed the `ChangesetCommentCheck` check to handle a lack of comments in the `tagDatabase` Change Type
 - Fixed the default severity for the existing `liquibase.checks-settings.conf` files
 - Fixed the `checks customize` and `checks copy` commands to handle directories with spaces
 - Fixed the `schemas` and `includeSchema` attributes for the `generateChangeLog` command in Liquibase 4.4 and later versions
 - Fixed the quality checks customization issue when using Git Bash
 - Fixed the message for the quality checks severity customization
 - Fixed the quality checks halting execution when the checks settings file contains entries for rules which do not exist in the system. Liquibase skips unknown checks and continues the execution
 - Fixed the quality checks customization issue when using Git Bash
 - Fixed the issue with Liquibase inserting a wrong default datetime value into MySQL. The PR fixes (#1395)
 - Fixed the `insert` Change Type with the `valueSequenceNext` and `valueSequenceCurrent` attributes to use the schema specified on a statement level of a _changeset_ (#1304)
 - Fixed the issue with Liquibase not handling case-sensitive primary key names in the `dropPrimaryKey` attribute for PostgreSQL (#1933)
 - Fixed the `sqlFile` Change Type to handle the `encoding` attribute(#1760)
 - Fixed the issue with Liquibase throwing a null pointer exception in the CLI if the `LicenseService` interface cannot be loaded because of the missing `jaxb` dependencies
 - Fixed the `UniqueConstraintSnapshotGenerator` class switching to a bulk query after four unique constraints (#1996)
 - Fixed the issue with Liquibase formatted SQL _changesets_ that are skipped if there is a space in the `--changeset` line (#1919)
 - Fixed the `dropAll` command failing in the PostgreSQL database when the Liquibase-Cassandra extension is in the `liquibase/lib` directory (#47)
 - Fixed Liquibase not reading the `liquibaseSchemaName` and `liquibaseCatalogName` configuration settings (#2000)
 - Fixed the issue with Liquibase incorrectly inserting a long CLOB value in the database. Added integration tests.

Changes in version 4.5.0(2021.09.21)

There is a new family of commands to use to run quality checks:
- liquibase checks show to list the available quality checks
- liquibase checks run to run the available quality checks
along with enable, disable, customize, copy, reset, and delete options, and liquibase checks --help to learn more.
The following is a list of changes related to Liquibase Quality Checks:
- Implement checks enable/disable subcommands 
- Implement checks run subcommand for dynamic rules 
- Implement checks customize subcommand 
- Implement checks copy subcommand 
- Implement checks reset subcommand 
- Implement checks show subcommand for dynamic rules
- Implement checks delete subcommand
- STATIC CHECK :: Changeset Must Have A Label
- STATIC CHECK :: Changeset Must Have A Context
- STATIC CHECK :: Changeset Must Have A Comment
- STATIC CHECK :: Warn When "DROP TABLE" Detected
- STATIC CHECK :: Warn When "DROP COLUMN" Detected
- STATIC CHECK :: Warn When Data Type Modification is Detected
- STATIC CHECK :: Warn when 'GRANT' Statement Detected
- STATIC CHECK :: Warn when 'REVOKE' Statement Detected
- DYNAMIC CHECK :: Warn when Tables have more than n columns
- Fail when creating a Scope with a null-parent #1354
- Improve synchronization of LockServiceFactory reset #1991
- Fix createSequence for h2 when dataType is specified #1992
- GenerateChangeLog on an Oracle db ignores diffIncludeTablespace when creating MissingTableChange #1812
- Fix OSGiResourceAccessor failing due to javax.activation #1534
- Ensure that the root scope is initialized correctly regardless of whether a scope manager has been configured yet or not #1768
- Manage parent directory relative to changelog in IncludeAll #1968
- Fix UniqueConstraint snapshot query on db2z #1771
- Fixes Postgresql bit column support #1468 
- fixed usage of computed values in prepared insert/update changes #1590 
- Get Set deleteCascade for serializer mapping #1837 
- Add support for changeset ids and authors with spaces in formattedsql changelogs. #1845 
- More efficient XML resource loading #1860
- Too many open files error on application server: LiquibaseEntityResolver opens too many file descriptors and never closes them #1815 
- Classpath scan time lapse for every included changelog file #1913 
- Postgresql: Preserve existing search_path when setting the defaultSchemaName #1864
- Cannot call extension function from migration #1823 
- Fixed postgresql addPrimaryKey cluster handling #1880
- PostgresDatabase should ignore addPrimaryKey clustered false #1779 
- CLI no longer reading defaultsFile from classpath #1893
- [PRO] Pro Enhancement Request: set 'splitStatements' to 'false' for Stored Logic changesets when generating a Formatted SQL changelog 
- [PRO] Allow users to indicate names and location of their spool files when using runWith:sqlplus 
- [PRO] Cannot update MariaDB expand based storage after first liquibase update 
- [PRO] Liquibase PRO generateChangeLog error on Postgresql functions that have long signatures containing any '()' characters 

Changes in version 4.4.3 (2021.08.05)

- Do not worry about FileSystemAlreadyExistsException errors #1885
- The sqlFile and relativeToChangelogFile attributes fail when using the logicalFilePath attribute #1277
- The wrong path received if relativeToChangelogFile=true and databaseChangeLog has logicalFilePath != PhisicalFilePath #1353
- Added a separate query for DB2 on AS400, which fixed the dropAll command on DB2 for iSeries #1412
- Fixed the logicalFilePath attribute not being serialized #1471
- Fixed the SqlParser bug related to parsing UTF-8 valid symbols #1513
- Fixed the invalid update SQL generated for HSQL database in InsertOrUpdateGeneratorHsql.java #1563
- Added a validation error when the customChange Change Type has no name attribute #1592
- Fixed the issue with Liquibase failing to create a DATABASECHANGELOGLOCK record and apply changesets into Firebird 3. isNumericBoolean now checks the Firebird3Database instance type and returns Boolean for Firebird 3. #1875
- Fixed the issue with escaping question marks in PostgreSQL: #1877
- SQL changesets are transforming the PostgreSQL ? JSON operator into ??, causing function definitions using this operator to become invalid #1764
- An error when processing a large Postgres SQL changeset #1507
- Fixed the issue with ClassCastException in a standard lock service: java.time.LocalDateTime cannot be cast to java.util.Date #1878
- Added the MySQL reserved word CUBE to the list of Liquibase reserved columns for the schema migration to work. CUBE is a reserved word as of MySQL 8.0.1 #1515
- Added the ability to handle empty lines at the beginning of a formatted SQL #1713
- Fixed loading the CLOB data into PostgreSQL. Instead of loading the CLOB file name, PostgreSQL loads the content of the CLOB file. #1791
- Changed the MD5SUM column value from RawSqlStatement to UpdateStatement #1602
- Fixed synchronization in ChangeLogHistoryServiceFactory.java #1361
- Enabled the dropAll report to work with a registered changelog with no extra arguments and send the information to Liquibase Hub
- Renamed the tablespaceName parameter to liquibaseTablespaceName
- Changed from RawSqlStatement to UpdateStatement #1602
- Fix synchronization in ChangeLogHistoryServiceFactory.java #1361
- [PRO] Fixed the changeset execution failure related to the DATICAL_SPERRORLOG table when using the runWith=sqlplus attribute and executing operations as a user with only SELECT, UPDATE, and INSERT permissions [DAT-7410]

Changes in version 4.4.2 (2021.07.22)

- Changed NullPointerException to ChangeLogParseException for the output when there is a typo in a changeSet node (#1593)
- Added the support method to DatabaseConnection (#1784)
- Fixed the issue with handling serial and bigserial if liquibase.convertDataTypes=false (#1883)
- Improved the ConfigurationValueProvider performance (#1952)
- Added the ability to allow an empty string for the defaultValue attribute (#1963)
- Reintroduced support for the - arguments in the CLI (#1943)
- Restored the updateTestingRollback command (#1939) 
- Added the Maven goal for unexpectedChangeSets (#1972)
- Extended the dropAll command with optional hubConnectionId and hubProjectId attributes. For more information, check our [dropAll command]documentation.
- Fixed nested properties and the substitution of variables in YML changelogs.
- Implemented the ability to use connectionjdbcurl when there is no project or connection ID with the dropAll command.
- Improved the Liquibase Pro License key messaging.
- Added the ability to handle a declined auto-registration when running the dropAll command.
- Fixed auto-registration issues.
- [PRO] Added the ability to handle special characters in procedures when using the generateChangeLog command.
- [PRO] Fixed the issue with formatted SQL changesets and runwith:sqlplus being stopped unless Liquibase includes a quit statement. 
- [PRO] Fixed the SQL Plus spool log issue.

Changes in version 4.4.1 (2021.07.09)
 - GH PR#1869 ⁃ HubUpdater releases lock even though registration is skipped
 - GH PR#1861 ⁃ Only snapshot tables/columns in loadData
 - GH PR#1843 ⁃ [LB-704] fixed generating sequences for Postgres versions prior to 10
 - GH Issue#1829 ⁃ Fixes MySQL Snapshot/Generated ChangeLog output for table key constraints when multiple tables
 - Postgres: Rollback of rename target fails due to malformed SQL.
 - Liquibase Hub registerChangeLog messaging fixes
 - Liquibase Hub, Maven, and liquibase.properties file fixes
 - Using --liquibaseProLicenseKey is on the left side of the command
 - Update Liquibase installer adoptopenjdk from 11.0.10 to 11.0.11  
 - [PRO] SQL Plus handles backslashes in changeset  attributes' values
 - [PRO] SQL Plus passing arguments during runtime fixes
 - [PRO] SQL Plus handles Oracle proxy user setups
 - [PRO] SQL Plus improve temp file handling
 - [PRO] Fixes snapshot on MySQL Stored Logic Objects with Integer Types

Changes in version 4.4.0 (2021.06.08)
- Refactor Configuration API
-- [PRO] including native use of Environment variables
- Refactor Command API
- Rewrote CLI to use new library
-- including new command options and help content
- Fixes an issue with Maven logs and username password (#1859)
- Fixes an issue with resolving properties files (#1723)
- Fixes an issue with diffChangelog dependency order (#1722)
- Fixes an issue with generating changlogs with DB2 on z/OS (#1506)
- Fixes an issue with long table names in Postgres (#1483)
- Fixes an issue with update flags for Postegres(#1213)
- Fixes an issue with loadData and loadUpdateData(#964)

Changes in version 4.3.5 (2021.04.29)
- Better error handling in runWith=sqlplus when user doesn't have create/drop table permissions

Changes in version 4.3.4 (2021.04.15)
- Reverts changes from #1722 because they broke other diffChangeLog/generateChangeLog use cases.

Changes in version 4.3.3 (2021.04.09)
- Fixes an issue with changelogsync and hub
- Fixes an issue with diff and NullPointerException (#1631)
- Fixes an issue with NullPointerException and DateTimeType (#1366)
- Fixes an issue with maven resolving/using properties file (#1723)
- Fixes an issue with diffChangeLog and addColumn -> addPrimaryKey ordering (#1722)

Changes in version 4.3.2 (2021.03.25)

- Added new changelogSyncToTag command #1133
- Support for working with deactivated changelogs in hub
- Added ability to create hub projects with parameters instead of only via prompts
- Only enable console prompting when explicitly enabled by the CLI #1711 #1689
- Handle includeAll better in spring boot #1595 and #1436
- Changed JRE bundled with installers to use the latest LTS version
- Better handle dropAll on postgresql partitioned tables #1650
- Fixed sample liquibase.sqlplus.conf file #1448 and #1416
- Fixed loadData's usePreparedStatement attribute #1098
- Send hub logs at the same log level as configured in the CLI

Changes in version 4.3.1 (2021.02.12)

- Fixed handling of `classpath*:` in includeAll #1595
- ClassCastException on moving to mysql-connector-java 8.0.23 #1639
- Passwords sent to Hub when using jTDS driver
- ChangeExecListener cannot be used to to HubChangeExecListener #1651
- RelativePathError when using includeAll tag and Spring Boot #1657
- Liquibase 4.3.0 throws NullPointerExceptions without snakeyaml in the classpath #1682

Changes in version 4.3.0 (2021.02.06)

- MariaDB is now fully certified for Liquibase Pro users
- Added ability to register for Hub during update operations
- Receive Liquibase Hub report links in the CLI after running commands
- Fixed NPE when an output writer is null #1625
- Fixed filename + Id + author to be treated as unique in ValidatingVisitor.findChangeSet method #1622
- Fixed EntityResolver to check the classpath even if the standard ResourceAccessor does not include it #1621
- Fixed the CVE-2017-18640 issue on snakeyaml-1.2.4 #1608
- Fixed HistoryCommandTest to run on systems having a non-US default locale #1078
- Added quotes around ${JAVA_PATH} in Liquibase shell script to avoid spaces in path issues #1062
- Fixed the issue with automatic dropping of default values on MS SQL #1003
- Allow --changeset to have a space after -- in formatted sql files
- Modified SetColumnRemarks support for MySQL #942
- Implemented boolean data type support for DB2 LUW #966
- Added deprecated LogFactory.getInstance() to smooth the upgrade to 4.x #1642 and #1641
- Included the existing dropPrimaryKey.dropIndex attribute in XSD #1629
- Implemented auto-registration and operation report capabilities for Liquibase Hub

Changes in version 4.2.2 (2020.12.09)

- Handle null statement objects coming from JdbcConnection objects #1576

Changes in version 4.2.1 (2020.12.03)

- Fixed invalid casting to JdbcConnection in CockroachDatabase #1536
- Fixed missing unique constraints in snapshot #1477
- Fixed unexpectedChangeSets command throwing a null pointer exception
- If error occurs during generating SQL, Liquibase stops and does not send POST to Hub
- Use ConcurrentHashMap in ExecutorService #1402
- DropDefaultValue on postgresql now drops the default value vs. setting the default value to null #931
- Updated banner to reference to hub.liquibase.com
- [PRO] GenerateChangeLog on postgresql stored logic now hashes parameters in filenames instead of listing them all out
- [PRO] Fix in postgresql check constraints snapshot
- [HUB] Command registerChangeLog now supports YAML and JSON changelog files

Changes in version 4.2.0 (2020.11.12)

- [PRO] Support for snapshotting Mysql stored logic
- GH PR#373 - Added option "updateable" to the column tag.
- GH Issue#1523 - Fix xsd, remove surplus space after validateForeignKey (4.1.xsd / 4.2.xsd
- GH Issue#1510 - MySql 8.0 requires create and drop of temporary tables to run generateChangeLog
- GH PR#1496 - Move banner.txt to new location
- GH PR#1495 - Fix issue with loadUpdateData GH-1489
- GH Issue#1489 - loadUpdateData broken for clob/text fields in 3.10.3
- GH Issue#1476 - Consider alternative location for banner.txt
- GH Issue#1438 - XML Export adds ',xxx' instead of ','
- GH Issue#1389 - Precondition onFail="WARN" doesn't work properly
- GH PR#1427 - Update Ant Tasks to use DatabaseFactory rather than create connection itself

Changes in version 4.1.1 (2020.10.09)

- GH #1276 Rewrote SpringResourceAccessor
- GH PR#1285 Remove deprecated code in Ant tasks
- GH PR#1286 Fix usage of AntTaskLogger
- GH #1097 diffChangeLog overwrites first changeset in formatted sql changelog

Changes in version 3.10.3 (2020.10.09)

- GH PR#912  - CORE-3379 escaping postgres questionmark operators to prevent unwanted parameter syntax
- GH PR#936  - SpringLiquibase createResourceOpener() now returns a ResourceAccessor, which allows it to be overridden with any kind of ResourceAccessor
- GH PR#983  - Fixes for loadData to support Postgres enums with java.sql.Types.OTHER
- GH PR#1010 - Potential fix for generated columns on postgres
- GH PR#1258 - Reduced the verbosity of logging by moving SQL logging from info to debug logging
- GH PR#1239 - Fix xsd, remove surplus space after validateForeignKey
- GH PR#1296 - Put the banner in an external file for easier editing.
- GH PR#1327 - Fix varchar(max) extrainfo in 3.10 branch
- GH PR#1406 - Fixed integration tests for 983
- GH PR#781  - feat: Maven Liquibase Plugin generateChangeLog mysql sql comment supported
- GH PR#945  - [CORE-3523] Consistently map Liquibase integer types to Oracle integer types.  MERGED THEN UNMERGED
- GH PR#1140 - fix root paths for spring boot fat jar
- GH PR#1332 - Update FileSystemResourceAccessor.java
- GH PR#1372 - Fix for PR 1001 in 3.10.x branch
- Fixed priority for JDBC Connections
- GH PR#1140 - Added json and sql to the list
- GH PR#1418 - Revert "[CORE-3523] Consistently map Liquibase integer types to Oracle integer types"  UNMERGE 945
- GH PR#1422 - Updates for #1081 in 3.10.x
- GH PR#1431 - Fix merge issue in OracleDatabase
- GH PR#1443 - Handle MSSQL computed columns in generate changelog LB-366
- GH PR#1447 - Do not use batched statements for Postgres LoadDataChange LB-744
- Fixed schema version number for 3.10

Changes in version 4.1.0 (2020.9.28)

- Added support for hub.liquibase.com to CLI and Maven integrations
- New registerChangeLog command
- New syncHub command
- Added hubApiKey argument
- Added hubConnectionId argument
- CLI commands and arguments are now case insensitive
- Includes changes from 3.10.2

Changes in version 3.10.2 (2020.7.27)

 - Dependency cycle error during generateChangeLog with --diffTypes=tables,columns,data
 - liquibase maven:: unable to run tests on java > 8
 - Add Banner in log out put that directs users to online resources.
 - GH PR#980 - fixed invalid carriage return coming from oracle ojdbc8 12.2.0.1
 - GH PR#976 - Changes databasechangelog sql to use legacy mode
 - [CORE-3525] Add support for java.sql.Types.TIMESTAMP_WITH_TIMEZONE.
 - GH PR#929 - Fix #CORE-3493 Inconsistent resolution of ChangeLogParameters
 - GH PR#744 - Informix data type generation with Default Value cause
 - GH PR#1176 - Add MariaDB sequence support to the 3.10.x branch
 - GH PR#1177 - CORE-3643: fixed the 'shouldValidateX' methods in the Constraints
 - GH PR#1201 - H2 unknown reserved word


Changes in version 4.0.0 (2020.07.14)
- No changes since 4.0.0 beta2

Changes in version 4.0.0 Beta2 (2020.07.1)
- Re-introduced deprecated versions of commonly used 3.x methods that had been removed for 4.0
- Fix to handle --logLevel=debug/fine CLI argument
- Do not include root filesystem in default CLI search paths
Changes in version 3.10.1 (2020.7.2)

 - GH PR#913 - [CORE-3471] Fixed NPE in LiquibaseUtil when MANIFEST.MF doesn't contain Bundle-Version or Build-Time
 - GH PR#917 - [Core 3463] Fix for validateX attributes of constraintsConfig
 - GH PR#919 - Fixed issued with bundling in shaded jars
 - GH PR#941 - [CORE-3520] Add support for ALWAYS generation type with PostgreSQL auto-increment column.
 - GH PR#946 - [CORE-3524] Prevent auto-increment column silently failing on Oracle.
 - GH PR#949 - [CORE-3526] TABLE is a reserved keyword in H2database since 1.4.197
 - GH PR#963 - [CORE-3556] No default values for changes' properties
 - GH PR#965 - [CORE-3558] Unique constraint name is ignored when using the addColumn change
 - GH PR#970 - Fix Java 1.9+ versions require jaxb as separate dependency
 - GH PR#971 - Add Class-Path entry to the manifest file to simplify command line execution
 - GH PR#975 - NULL date value in prepared statement requires type in MSSQL
 - GH PR#986 - XMLSerializer: Correct Map value serialization
 - GH PR#993 - Improve ChangeFactory initialization time
 - GH Issue#1035 - Change how Liquibase produces debug logs
 - GH Issue#1051 - Update liquibase-core.jar to exclude ServiceLoader files
 - GH PR#1053 - Update liquibase_autocomplete.shape
 - GH PR#1059 - Allow multiple column unique constraints on table creation.
 - GH Issue#1069 - Patch Liquibase Runner plugin for Jenkins
 - GH Issue#1070 - Enable <command>--help without databse connection
 - GH PR#1082 - Correct include bash script
 - GH PR#1096 - Fix sequence statement generation for PostgreSQL <= 9.4
 - GH Issue#1104 - Fix maven-liquibase offline diff to prevent errors
 - GH Issue#1113 - Fix schema name using "--defaultSchemaName" on a non-default schema
 - GH PR#1120 - LiquibaseServletListener.contextInitialized() improved with Embedded Derby
 - GH Issue#1220 - Fix how JAVA_HOME env variable is handled

Changes in version 3.10.0 (2020.6.12)
- [Pro] Built-in runWith="sqlplus"
- New "runWith" changeSet extension point
- Added liquibase_autocomplete_mac.bash completion script

Changes in version 3.9.0 (2020.5.13)
- [Pro] New ""--format=json" argument for diff command

Changes in version 4.0.0 Beta1 (2020.4.20)
- MAJOR CHANGE: Replaced old custom classpath scanner with java.util.ServiceLoader for finding extensions
- MAJOR CHANGE: Refactored logging system to clean up API and use java.util.Logging
- Fixes to ConstraintsConfig https://github.com/liquibase/liquibase/pull/954
- ignoreLines command in formatted sql log https://github.com/liquibase/liquibase/pull/877
- postgresql >= 10: use identity columns for autoincrement https://github.com/liquibase/liquibase/pull/874
- Using proxy user for Oracle Connections https://github.com/liquibase/liquibase/pull/894
- Capture table remarks on mysql https://github.com/liquibase/liquibase/pull/895
- Add PERIOD to reserved words for MariaDB https://github.com/liquibase/liquibase/pull/896
- Don't require columnDataType on H2 for addNotNullConstraint https://github.com/liquibase/liquibase/pull/851
- Do not remove trailing /'s of comments like /* dsds */ https://github.com/liquibase/liquibase/pull/853
- Require type xsd:string to where element of update element https://github.com/liquibase/liquibase/pull/875/files
- runOnChange change set runs every time even if there wasn't changed https://github.com/liquibase/liquibase/pull/891
- Indexes with DESC sorting are not created for PostgreSQL https://github.com/liquibase/liquibase/pull/880
- Added Ingress support https://github.com/liquibase/liquibase/pull/683
- Support boolean in firebird 3.0 https://github.com/liquibase/liquibase/pull/757
- Change Log Parameters from Environment Variable https://github.com/liquibase/liquibase/pull/758
- Adds clearCheckSums property in spring integration https://github.com/liquibase/liquibase/pull/792
- Oracle datatype translation changes https://github.com/liquibase/liquibase/pull/797
- Implement Sequence Support for MariaDB https://github.com/liquibase/liquibase/pull/852
- Cleanup checksum classes https://github.com/liquibase/liquibase/pull/869
- Fix Turkish uppercase column keys bug https://github.com/liquibase/liquibase/pull/857
- Generate diff with onDelete and onUpdate https://github.com/liquibase/liquibase/pull/858
- PrimaryKeySnapshotGenerator throws NPE for SQLite https://github.com/liquibase/liquibase/pull/856
- Handle java.sql.Time when setting prepared statement parameter values in CSV filese https://github.com/liquibase/liquibase/pull/859
- DB2 LUW supports boolean data type column from version ******** https://github.com/liquibase/liquibase/pull/865
- Offline history support not correctly closing files https://github.com/liquibase/liquibase/pull/863
- Use computeIfAbsent to avoid Liquibase concurrency issues https://github.com/liquibase/liquibase/pull/866
- Firebird does not support RESTRICT option on Foreign Keys https://github.com/liquibase/liquibase/pull/761
- Liquibase was ignoring DatabaseFunction value and processing only SequenceNextValueFunction when creating tables and generating column default value. https://github.com/liquibase/liquibase/pull/785
- Restore usage of `DatabaseList.definitionMatches` to match the `dbms` tag against the database type https://github.com/liquibase/liquibase/pull/811
- DEPLOYMENT_ID column header not included in offline updateSql CSV https://github.com/liquibase/liquibase/pull/813
- Can't set some parameters for snapshot & generate changelog https://github.com/liquibase/liquibase/pull/822
- UUID type handling improvements https://github.com/liquibase/liquibase/pull/838
- Correctly close yaml file in generateChagneLog https://github.com/liquibase/liquibase/pull/844
- Adding restricted words for MSSQL Server Database https://github.com/liquibase/liquibase/pull/825
- Getting a java.io.FileNotFoundException using Liquibase with Spring reactive web https://github.com/liquibase/liquibase/pull/814
- FileSystemResourceAccessor fails with non-hierarchical URLS on classpath https://github.com/liquibase/liquibase/pull/830
- fix oracle date literal without millis https://github.com/liquibase/liquibase/pull/828
- fix nullPointerException when migrating RedShift https://github.com/liquibase/liquibase/pull/823
- Prints the SQL error code on Exception https://github.com/liquibase/liquibase/pull/833
- Fixed SetColumnRemarks for mysql https://github.com/liquibase/liquibase/pull/835
- Allow "NOW" and "Today" literal capabilities in CSV filese https://github.com/liquibase/liquibase/pull/808
- Add ability to disable running of Liquibase via CDILiquibaseConfig https://github.com/liquibase/liquibase/pull/804

Changes in version 3.8.9 (2020.4.06)
- Fixed an issue with how Liquibase handles SMALLINT columns in PostgreSQL when generating a changelog.
- Improved `generateChangeLog` command to assume a `diffTypes` that includes "data" when the `dataOutputDirectory` parameter is used. Most users looking to export data as part of `generateChangeLog` can now simply use the `dataOuputDirectory` parameter without needing to specify `diffTypes`.
- Enabled the use of the `--schemas` parameter to work on either side of the `snapshot` command.
- Enabled `generatechangelog` in PostgreSQL to correctly order `create`, `function`, and `trigger` statements.
- Improved the use of dblink objects when using `diff` command.

Changes in version 3.8.8 (2020.3.19)
- `rollbackOneChangeSetSQL` now works correctly with the Liquibase Maven plugin.
- Liquibase Pro no longer generates procedural code with incorrect SQL syntax when running `generateChangeLog` against a MySQL database.

Changes in version 3.8.7 (2020.2.21)

- New liquibase history command
- [Pro] New liquibase rollbackOneUpdate command
- PR#927 CompositeClassLoader does not implement getResource(String)
- `liquibase --help` was improved to include missing commands and correct information

Changes in version 3.8.6 (2020.2.6)

- New graphical installer for Windows and Mac
- New examples directory with sample xml and sql changelogs and test h2 database
- [Pro] New RollbackOneChangeSet and RollbackOneChangeSetSQL commands
- Updated default Mysql driver to com.mysql.cj.jdbc.Driver
- Removed non-jdbc compatible "SET DEFINE OFF" from generated SQL on Oracle
- Fixed --excludeObjects/--includeObjects arguments in diff

Changes in version 3.8.5 (2020.1.13)

- Fixes issue in which Liquibase did not add `DESC` to an `id` column
- CLI once again accepts sqlFile, delimiter, rollbackScript, and outputSchemaAs arguments
- Fixes Stored Procedure whitespace impacting update calls
- Fixes generateChangelog failure when there are missing NOT NULL constraints.
- Fixes updateSQL, which was not including schemaName for package bodies.
- Fixes bug in which Liquibase was not always capturing `create package body` while generating changeLog
- Liquibase CLI now gives a more useful error message when an invalid character is passed to a command.
- Fixes an MSSQL issue, which threw an exception when doing diff/diffChangeLog between offline connections (such as when snapshotting json files)
- Fixes an MSSQL issue, which incorrectly generated `datetime` data type column when using`diff` & `diffChangeLog`
- Liquibase Maven plugin fails while executing/using "diff" and "generateChangeLog" with PostgreSQL)

Changes in version 3.8.4 (2019.12.23)

- Fixed a 'Class not Found' exception error when using Java +9.
- Fixed a 'Cannot Use Default Schema Name' error when users diff against an SQL Server snapshot file.
- Fixed an 'Unexpected type: java.util.Date' error when using a defaultValueDate attribute in a YAML changelog.

Changes in version 3.8.3 (2019.12.19)

- Liquibase-Maven plugin now works with Java 9+.
- Expired license messages should be far less verbose and excitable.

Changes in version 3.8.2 (2019.11.26)

- Pro stored logic snapshot logic now supports postgresql and db2
- Using generateChangeLog/diffChangeLog to generate formatted sql changelogs now works with Liquibase Pro's stored logic support
- CDI-related classes moved back out of the main liquibase jar into an optional jar
- Fixed issue with indexes backing foreign keys not always being caputured in diffChangeLog/generateChangeLog
- Fixed issue with diffChangeLog/generateChangeLog generating primary keys when column order doesn't match the table's column order

Changes in version 3.8.1 (2019.11.5)

- [PRO] Oracle and SQL Server users can now reverse engineer database changes for stored logic such as triggers, functions & procedures using generateChangeLog, snapshot, diff, and diffChangeLog commands.
- diff command writes to an external file using --outputFile flag
- updateSQL command no longer outputs to liquibase.log
- Cleaned up unexpected entries in diffchangelog.xml for SQL Server

Changes in version 3.8.0 (2019.8.16)

- [CORE-3462] - Add new procedural database code change types for Liquibase Pro

Changes in version 3.7.0 (2019.7.16)

- [CORE-3303] - Allow disabling shouldRun configuration via CDI integration
- [CORE-3388] - DB2 LUW supports boolean data type column from version ********
- [CORE-3400] - add "manifoldclass:" to list of skipped URL protocols in DefaultPackageScanClassResolver
- [CORE-2377] - Using replaceIfExists with <createView> generates DROP statements
- [CORE-2971] - Comments at end of statements are incorrectly parsed
- [CORE-3124] - Indexes with DESC sorting are not created for PostgreSQL
- [CORE-3159] - class cast exception when running command line
- [CORE-3174] - Plugin does not create parent directory
- [CORE-3211] - Firebird does not support RESTRICT option on Foreign Keys
- [CORE-3251] - SQL Server Changesets Not Escaping Keywords
- [CORE-3365] - PrimaryKeySnapshotGenerator throws NPE for SQLite
- [CORE-3375] - addNotNullConstraint on h2 still requires (unused) columnDataType
- [CORE-3386] - includeAll can't resolve relativeToChangelogFile path within a jar file
- [CORE-3437] - MariaDB 10.3.4+ problem with add column PERIOD
- [CORE-2235] - Support priority-based selection of Precondition implementation
- [CORE-3205] - Change Log Parameters from Environment Variable

Changes in version 3.6.3 (2019.1.29)

- [CORE-3100] - diff with uppercase reference database name
- [CORE-3141] - ForeignKeySnapshotGenerator broken with recent SQL Server driver
- [CORE-3192] - Error while including resources with includeAll directive
- [CORE-3221] - Parsing valueDate Attribute of column Tag leads to precision loss and generating SQL for Oracle DB is broken
- [CORE-3222] - Bin zip doesn't work due to missing slf4j-api JAR
- [CORE-3231] - NPE in LogService.pushContext for MDCs that don't allow null values
- [CORE-3242] - MS SQL Server 2008 support issue
- [CORE-3256] - Verbose flag not getting interperated during 'status' command.
- [CORE-3293] - Non global change log parameters are wrongly resolved in inner files
- [CORE-3296] - CommandLineUtils::getBanner fails if manifest entries are missing
- [CORE-3335] - Not possible to register/unregister SqlGenerators during execution of changeSet
- [CORE-3348] - MSSQLDatabase class has a bug when connecting to SQL Server 2008 databases

Changes in version 3.6.2 (2018.06.30)

- [CORE-3129] - PostgreSQL dropPrimaryKey with objectQuotingStrategy="QUOTE_ALL_OBJECTS"
- [CORE-3206] - Liquibase 3.6.x is binary api-incompatible with 3.5.x
- [CORE-3213] - Changelog with includeAll will not find child changelogs in multi-modules Spring Boot's executable JAR
- [CORE-3229] - Oracle 11g doesn't support TIMESTAMP WITHOUT TIME ZONE data type

Changes in version 3.6.1 (2018.04.11)

- [CORE-3200] - Wrong SQL generator is selected
- [CORE-3201] - Command line missing required dependencies in 3.6.0 tarball
- [CORE-3198] - Configuration option to prefer internal XSD usage

Changes in version 3.6.0 (2018.04.5)

- [CORE-1609] - Command Prompt: Can't connect to database with a special character in pwd
- [CORE-1852] - checksums depend on environment
- [CORE-1888] - Sybase error for TINYINT, INT, BIGINT: Can't specify a length, scale or storage property on type 'int/tinyint/bigint'.
- [CORE-2008] - H2 Supports minValue and maxValue in Sequences since Version 1.3.175, but Liquibase does not
- [CORE-2033] - NPE during Diff when case sensitive table missing
- [CORE-2135] - liquibase corrupting UTF-8 changesets
- [CORE-2162] - MSSQL: Multiple inserts in sqlFile do not fail as expected
- [CORE-2191] - Update on SQL Azure database fails due to unavailable sys.extended_properties
- [CORE-2527] - Sybase create table fails because column name is too long
- [CORE-2631] - dbdoc does not specify content type
- [CORE-2739] - --delimiter parameter in liquibase --help
- [CORE-2747] - CreateView / Oracle: Keyword REPLACE in view definition prohibits "or replace" in DDL statement
- [CORE-2772] - primaryKeyExists check fails on Turkish locale for some chars
- [CORE-2773] - DB2 AS/400 - generateChangeLog throwing Exception
- [CORE-2796] - Handle TimeStamps with nano second precision correctly
- [CORE-2797] - Determine the DB2 data server type correctly
- [CORE-2820] - Unsupported ReorganizeTable for DB2 z/OS causes changelog validation to fail
- [CORE-2821] - AddForeignKey statement generates incorrect SQL for DB2 z/OS
- [CORE-2826] - indexExists precondition fails on AS400 + JDBCDatabaseSnapshot/Snapshot generator code is not coded generically
- [CORE-2843] - Sql wrong lexical analysis for string literals - escaped single quotes are misparsed
- [CORE-2875] - UTF-8 character not understood
- [CORE-2894] - Oracle snapshot not detecting custom datatypes in different schemas
- [CORE-2909] - column remarks for mysql should be escaped
- [CORE-2911] - Oracle: generateChangeLog on RAW types not including the size param
- [CORE-2928] - Invalid snapshot of "duplicate" foreign keys
- [CORE-2929] - Views with definitions that start with a comment are not captured correctly in generateChangeLog
- [CORE-2940] - Do not print warning when DBA_RECYCLEBIN is not available
- [CORE-2944] - outputDefaultSchema and outputDefaultCatalog command line parameters not respected
- [CORE-2953] - update with valueSequenceNext and schema produces wrong SQL on oracle
- [CORE-2965] - Custom Properties XML Changelog
- [CORE-2992] - liquibase.util.grammar.TokenMgrError: Lexical error at line 1, column 71. Encountered: "\u00b4" (180), after : ""
- [CORE-2993] - createSequence with order denied on DB2
- [CORE-3002] - SQLAnywhere: Revert Unique Index failed
- [CORE-3006] - Oracle CSV-Import: "String index out of range: -1"
- [CORE-3009] - SQLAnywhere: Drop Default Value failed
- [CORE-3020] - No warning when included file doesn't exist (missing extension)
- [CORE-3033] - typo in postgresql reserverd word
- [CORE-3040] - onlyUpdate="true" flag generates empty statements for MySQL DB
- [CORE-3046] - Fix faulty snakeyaml class-path entry after upgrade to 1.17
- [CORE-3051] - SQLAnywhere: Drop Table does not support CASCADE
- [CORE-3054] - SQLAnywhere: java-coredump on changeSet-SQL
- [CORE-3055] - SQLAnywhere: supports Sequences
- [CORE-3063] - Integration tests failing on master
- [CORE-3069] - Checksum: line endings not standardized on windows if multiple lines
- [CORE-3072] - Add usePreparedStatements="true|false" flag to loadData
- [CORE-3076] - SUM is not reserved word for HsqlDB
- [CORE-3099] - Non English environment; invalid tablename and column name can be generated because of toUpperCase toLowerCase method which is dependent to locale in java
- [CORE-3101] - dropPrimaryKey TABLE_SCHEMA = 'null'
- [CORE-3106] - SQLAnywhere: DROP INDEX should use tablename
- [CORE-3115] - Prefix space in column type causing the Unknown LiquibaseDataType with the latest release
- [CORE-3117] - TIMESTAMP WITH TIME ZONE datatype is changed to TIMESTAMP in H2
- [CORE-3119] - Maven failing to use driverPropertiesFile from Liquibase Properties File
- [CORE-3135] - Column t1.tgconstrname does not exist
- [CORE-3138] - SQLAnywhere: AddAutoIncrement-Statement is wrong
- [CORE-3140] - MSSQL2005 doesn't support built-in function original_db_name()
- [CORE-3155] - CSV line content behind inline comment character doesn't contribute to checksum
- [CORE-3162] - Diff problem with MSSQL case sensitive database
- [CORE-3171] - LoadUpdateData doesn't work on SAP SQLAnywhere
- [CORE-3180] - A DBMS-specific change set referencing a DBMS-specific rollback can't be parsed on a different DBMS
- [CORE-2735] - Add possibility to test rollback with SpringLiquibase
- [CORE-1225] - Add support for tablespace assigned to liquibase metadata tables
- [CORE-2628] - defaultSchema parameter doesn't do Connection.setCatalog() for SpringLiquibase
- [CORE-2842] - MSSQL: Support creating clustered unique constraints
- [CORE-2891] - Liquibase "Command" objects can be extended and overridden
- [CORE-2919] - Make all variants of Liquibase.listUnrunChangeSets public
- [CORE-2952] - Use the clustered index if duplicate indexes are defined
- [CORE-2955] - MSSQL: Capture explicit null default values on snapshot and generate/diffChangeLog
- [CORE-2970] - MSSQL: Support default value constraint names
- [CORE-2977] - Generated primary key constraint name doesn't match Postgres default
- [CORE-2985] - MSSQL Snapshot performance improvements
- [CORE-3000] - Oracle JDBC batch for load_data
- [CORE-3005] - Consideration of DB2/400 system views
- [CORE-3017] - Add path attribute to createView
- [CORE-3018] - Oracle: support remarks on createView
- [CORE-3045] - Support indexes on views
- [CORE-3079] - Make includeObjects and excludeObjects affect which objects are snapshotted
- [CORE-3094] - HSQLDB UUID support
- [CORE-2920] - Using "//" as an endDelimiter stopped working 3.5.0

Changes in version 3.5.5 (2018.02.5)

-  [CORE-2851] - includeAll tag with a relative path duplicates the database changes with an absolute and with a relative changelog

Changes in version 3.5.4 (2018.02.5)

- [CORE-2863] - Issue with Spring boot 1.4.0 - 1.4.3
- [CORE-2898] - includeAll broken in 3.5.1
- [CORE-2948] - Changelog with includeAll will not find child changelogs in Spring Boot's executable JAR
- [CORE-2978] - AddAutoIncrement on Postgres does not work when no schema is specified
- [CORE-3123] - ResourceComparator is not applied for includeAll
- [CORE-3139] - ClassLoaderResourceAccessor cannot read jar path resources from SpringLiquibase
- [CORE-3015] - Oracle: diffChangeLog TIMESTAMP WITH LOCAL TIME ZONE correctly

Changes in version 3.5.3 (2016.10.13)

- No changes

Changes in version 3.5.2 (2016.09.21)

-  [CORE-1863] - PostgreSQL blob is mapped to bytea instead of oid
-  [CORE-2693] - Postgresql dropAll with serial columns fails because tables are dropped then sequences which no longer exist
-  [CORE-2698] - <sqlFile> Oracle scripts with ending / are not actually getting executed even with splitStatements="false"
-  [CORE-2752] - Jtds has silent exceptions in db.getConnectionSchemaName
-  [CORE-2753] - defaultValueSequenceNext forgets schema name
-  [CORE-2754] - update table columns are not quoted when requested
-  [CORE-2756] - Null pointer exception from FileSystemResourceAccessor
-  [CORE-2757] - Databasechangelog ORDEREXECUTED and DEPLOYMENT_ID not updated when a changeSet is reran
-  [CORE-2758] - Debian package shouldn't symlink liquibase binary to absolute path
-  [CORE-2761] - 3.5.1: includeAll from the command line uses the absolute path as the changeSet path
-  [CORE-2763] - Postgresql schemas should default to lower case
-  [CORE-2765] - dbms in preConditions yaml changelog causes parsing error
-  [CORE-2770] - Can't read remarks from mssql 2000
-  [CORE-2774] - Can't read all columns from mssql2000
-  [CORE-2775] - Oracle Sequences not Generated in generateChangeLog after 3.4.1
-  [CORE-2778] - Sybase ASE: Incorrect syntax near the keyword 'ALTER'
-  [CORE-2780] - java.sql.SQLException: PooledConnection has already been closed
-  [CORE-2781] - DB2: custom-generated indexes for primary keys are not preserved in diff/generateChangeLog
-  [CORE-2784] - REGRESSION: Column creation of type 'TIMESTAMP WITHOUT TIMEZONE' fails on PostgreSQL
-  [CORE-2785] - Status command inconsistent with databasechangelog table
-  [CORE-2786] - Incorrect xml scheme for changlog file
-  [CORE-2787] - YAML Snapshot parser not handling strings that get stored as binary
-  [CORE-2789] - Postgres does not have a type "BINARY"
-  [CORE-2791] - Strip off trailing end delimiter in createProcedure on update, include on updateSql
-  [CORE-2793] - using property as startWith attribute
-  [CORE-2794] - Make CSV files created by Liquibase readable by Liquibase
-  [CORE-2795] - Fix a NullPointerException in DiffToChangeLog.sortMissingObjects
-  [CORE-2804] - defaultValueSequenceNext forgets schema name H2/PG/etc
-  [CORE-2805] - Multiple calls to generateChecksum() impacting deploy performance
-  [CORE-2806] - JsonSnapshotParser does not close stream after parsing
-  [CORE-2807] - Column data type "real" incorrectly translated to "double precision" for PostgreSQL, should be "real"
-  [CORE-2810] - defaultValueBoolean="false" generates wrong SQL for MySQL
-  [CORE-2811] - FileSystemResourceAccessor basepath/includeAll
-  [CORE-2813] - java.lang.NullPointerException when creating new ClassLoaderResourceAccessor();
-  [CORE-2814] - DB2: Quoting strategy not respected, objects are always saved as upper case
-  [CORE-2815] - Rollback by tag doesn't roll back tagDatabase changeSet
-  [CORE-2816] - Snapshot error when snapshotting an index or primary key against a case-sensitive column
-  [CORE-2818] - DEPLOYMENT_ID not created for Sybase in DATABASECHANGELOG, liquibase 3.5.1
-  [CORE-2819] - AbstractJdbcData getConnectionSchemaName() methods fails for Sybase
-  [CORE-2823] - DROP PRIMARY KEY fails for Sybase database update
-  [CORE-2827] - MSSQL: misc default value fixes
-  [CORE-2828] - MSSQL not capturing that primary key are non-clustered in generateChangeLog
-  [CORE-2830] - GenerateChangeLog does not handle tables with compound primary keys
-  [CORE-2831] - MySql BIT(1) defaultValue not snapshotted as Boolean
-  [CORE-2835] - GenerateChangeLog doesn't correctly "numeric DEFAULT '" " columns
-  [CORE-2836] - addAutoIncrement generates inconsistent sequence name for mixed-case table
-  [CORE-2837] - addAutoIncrement doesn't apply default schema in nextval call (PostgreSQL)
-  [CORE-2838] - createProcedure schema in the changelog is overwritten by defaultSchemaName
-  [CORE-2840] - MSSQL createProcedure for CREATE MERGE AS procedures need a trailing semicolon
-  [CORE-2843] - Sql wrong lexical analysis for string literals - escaped single quotes are misparsed
-  [CORE-2846] - DATABASECHANGELOG table query failed on postgres on first run
-  [CORE-2849] - Fail to execute with sequences
-  [CORE-2853] - Diff comparisions reporting differences between '0.0' and '0' in decimals
-  [CORE-2863] - Issue with Spring boot 1.4.0
-  [CORE-2864] - Regression for defaultSchemaName on MSSQL
-  [CORE-2867] - liquibase with MySQL raises exception "Table 'DATABASECHANGELOG' already exists" when using separate liquibase schema
-  [CORE-2868] - <createProcedure> Adds schema/username to package and package body in oracle making them invalid
-  [CORE-2869] - Without a specified classpath, using includeAll with relativeToChangelogFile="true" fails
-  [CORE-2872] - "ON DELETE" not supported for FK constraints in Sybase
-  [CORE-2876] - Issue with Spring boot 1.4.0 Repackaged
-  [CORE-2878] - MSSQL setTableRemarks limited to 200 chars
-  [CORE-2881] - DiffChangeLog unnecessarily includes referenceTableCatalogName attribute if comparing Schema with different names
-  [CORE-2885] - AddColumn with defaultValueSequenceNext generated incorrect SQL for PostgreSQL
-  [CORE-2788] - handle VARBINARY type in Oracle and H2
-  [CORE-2800] - Add flag to diff command that suppresses reporting of column order difference in tables
-  [CORE-1984] - Support for non-split rollback statements in Formatted SQL
-  [CORE-2768] - Have the .deb and .rpm part of the release on github
-  [CORE-2782] - Update SnakeYAML version to 1.17
-  [CORE-2801] - Add method to Logger to allow closing of the output file stream
-  [CORE-2844] - Traverse parent changelogs for rollbacks
-  [CORE-2848] - Oracle: primary keys that use a pre-existing index drop the index along with the primary key on rollback
-  [CORE-2852] - Postgresql snapshots not correctly handling serial-backing sequences
-  [CORE-2857] - Support clustered primary keys in postgresql
-  [CORE-2873] - Postgresql custom types are snapshotted as having length 2147483647
-  [CORE-2874] - Ensure consistent charset encoding usage

Changes in version 3.5.1 (2016.05.10)

- [CORE-2727] - NPE in DiffToReport.print() method
- [CORE-2728] - Classloading broken in 3.5.0 with nested jars
- [CORE-2729] - NullPointerException on Diff
- [CORE-2731] - diff fails with NullPointerException
- [CORE-2732] - releaseLock fails because ObjectQuotingStrategy is reset to LEGACY
- [CORE-2733] - relativeToChangelogFile fails with FileSystemResourceAccessor
- [CORE-2734] - Liquibase no longer handle newline correctly in endDelimiter when using sqlFile change
- [CORE-2743] - CSV whitespace trimmed in 3.5.0
- [CORE-2744] - changeset with loadUpdateData changes checksum in 3.5.0
- [CORE-2745] - Performance degradation of sqlFile change
- [CORE-2746] - Oracle: handle case when schema name contains a hyphen
- [CORE-2750] - MSSQL catalog/database included all the time

Changes in version 3.5.0 (2016.04.21)

- New "created" attribute on changeSet
- New runOrder="first|last" attribute on changeSet to override where in the changelog it is ran
- New "context" attribute on <include> and <includeAll> to control when changelogs are included
- Support for AND/OR context expressions in formatted SQL changelogs
- Support for changelog parameters in formatted SQL chagnelogs
- Improved multi-schema snapshot and comparison
- Improved SQL parsing
- Performance improvements
- Lots of bug fixes

- [CORE-155] - Default-context attribute in databaseChangeLog tag
- [CORE-575] - foreign key constraint is generated with onUpdate and onDelete restrict
- [CORE-910] - Lock is not released if nocount is on for sql server 2008
- [CORE-1166] - Diff doesn't detect 'on delete cascade' statements
- [CORE-1447] - Inconsistent line endings in updateSQL produced files
- [CORE-1468] - Number / Numeric handling must differ between different database systems
- [CORE-1679] - Changelog SQL routines are partially aware of Database quoting strategy
- [CORE-1690] - OSGiPackageScanClassResolver does not search extensions in Fragment bundles
- [CORE-1836] - Oracle Integer mapping
- [CORE-1839] - H2 multicolumn unique constraints
- [CORE-1883] - Performance issue with large datamodels
- [CORE-1887] - Including the same ChangeSet twice causes ValidationFailedException
- [CORE-1897] - stripComments from SQL is trimming quoted string
- [CORE-1935] - Formatted SQL generateChangeLog failed
- [CORE-1966] - Invalid object name 'INFORMATION_SCHEMA.SEQUENCES' when running generateChangeLog with SQL Azure
- [CORE-1985] - constraints tag is missing referencedTableSchemaName
- [CORE-2024] - Move SDK to its own module
- [CORE-2056] - generateChangeLog generates too many 'constraints'
- [CORE-2059] - escapeObjectName has no respect for quotingStrategy QUOTE_ONLY_RESERVED_WORDS on Postgres
- [CORE-2184] - diff doesn't write correct Changesets for foreign key constraints with ON DELETE CASCADE
- [CORE-2211] - Liquibase tries to execute commented lines in custom SQL file
- [CORE-2213] - Liquibase does not support comments in a line after the semicolon
- [CORE-2333] - loadData fails with quoted string containing a comma
- [CORE-2376] - LiquibaseCatalogName in commandLine not working correctly
- [CORE-2438] - DeleteGenerator does not handle parameter names and values with $ or \ properly
- [CORE-2456] - includeObjects/excludeObjects not work for UniqueConstraint
- [CORE-2474] - dropFirst does not drop objects in not default schemas on Postgres 9+ using sql format with multiple schemas
- [CORE-2483] - Liquibase does not delete unzip directories from temporary directory
- [CORE-2501] - Derby sequenceCurrentValue Incorrect SQL
- [CORE-2502] - Handle single-tag <databaseChangeLog/> tag on diffChangeLog
- [CORE-2504] - NumberFormatException while checking precondition
- [CORE-2508] - GlobalConfiguration liquibase.should.run alias is incorrect
- [CORE-2512] - Both defautValueComputed and defaultValue included in addColumn diffChangeLog outputs if value is a computed value
- [CORE-2513] - DiffChangeLog fixing changed indexes misses "unique" attribute
- [CORE-2514] - bad Maven documentation for outputDefaultCatalog and outputDefaultSchema
- [CORE-2517] - Foreign key snapshot improvements for DB2
- [CORE-2518] - DB2: snapshot sees DATE types as TIMESTAMP
- [CORE-2520] - Spurious warning with includeAll: file is not a recognized file type
- [CORE-2521] - addAutoIncrement on Postgres generates invalid SQL when specific schema is used
- [CORE-2522] - Derby: support for findForeignKeyConstraints
- [CORE-2523] - H2: use "REAL" as datatype for "FLOAT"
- [CORE-2524] - MSSQL: need to escape default value constraint names
- [CORE-2525] - Error on dropping sequence
- [CORE-2526] - Oracle 12: TIMESTAMP(3) not handled
- [CORE-2528] - Oracle: improve unique constraint snapshot query performance
- [CORE-2529] - MSSQL auto_increment numeric is 1 smaller in generateChangeLog
- [CORE-2531] - Wrong type mapping of BINARY type in MySQL, H2, HSQLDB and Postgresql
- [CORE-2533] - Poor runtime performance
- [CORE-2538] - regenerate maven documentation for liqubase on website
- [CORE-2545] - MSSQL: createProcedure fails if replaceIfExists=true and body uses "create proc" rather than "create procedure"
- [CORE-2547] - liquibase.database.core.DB2Database - Improper Resource Shutdown or Release
- [CORE-2548] - primaryKeyTablespace is ignored in PostgreSQL
- [CORE-2550] - Proper handling BINARY type in MySQL and H2
- [CORE-2552] - Oracle performance: fetch view definition along with original view to reduce the number of needed queries
- [CORE-2553] - DB2: add ability to disable automatic reorg statements
- [CORE-2556] - Cannot execute update code including quoted strings containing semicolons
- [CORE-2558] - includeAll incorrectly sorting by including WEB-INF/classes when running in an ear
- [CORE-2561] - Add "cycle" attribute to alterSequence
- [CORE-2562] - MSSQL: Snapshot errors if table names have single quote marks in them
- [CORE-2563] - DiffChangeLog that adds columns does not preserve column order
- [CORE-2565] - Escaping of the sequence names with schema generates invalid statements for Oracle DB
- [CORE-2581] - mysql update emits incorrect sql for BIT(1) when default specified
- [CORE-2587] - Validation not performed before rollback
- [CORE-2591] - autoincrement problem with ORA 12c + COMPATIBLE param to 11
- [CORE-2595] - DefaultPackageScanClassResolver fails if "fat" jar has dirs and files with same name
- [CORE-2599] - generateChangelog produces incorrect values for binary type
- [CORE-2601] - ORDER keyword isn't escaped for Oracle
- [CORE-2602] - StackOverflowError generating snapshot
- [CORE-2604] - PostgreSQL datatype bit(n) column gives BOOLEAN(n) type for CreateTableChange genearated sql statement
- [CORE-2605] - addColumn with multiple columns, does not create the constraints
- [CORE-2606] - PostgreSQL : Table creation with datatype smallserial fails.
- [CORE-2609] - Liquibase command line fails when using JTDS driver with SSO
- [CORE-2611] - Sybase ASE generated table name is wrong. Roll back CORE-2447
- [CORE-2615] - Multi-schema snapshot bugfixes
- [CORE-2623] - Oracle: primary keys on tables recovered from recyclebin are not properly snapshotted
- [CORE-2624] - MSSQL: better support for user defined types
- [CORE-2625] - Diff: should not be case sensitive in column default value functions
- [CORE-2629] - SQL syntax doesn't allow commenting indepenrent parts.
- [CORE-2632] - Postgres index drop needs schema
- [CORE-2635] - Applied changeset not detected
- [CORE-2636] - includeAll uses full file path
- [CORE-2637] - Creating column with tinyint(1) instead created as default tinyint
- [CORE-2641] - runOnChange change set runs every time even if there wasn't changed
- [CORE-2642] - Xsd files are not resolved from classpath when resolving from resourceAccessor fails.
- [CORE-2643] - loadData
- [CORE-2645] - Rollback referencing a change set in current child file cannot be parsed
- [CORE-2653] - Liquibase show difference for some UniqueConstraint and Views in SQL SERVER databases but Actually there is NO difference when i compare the SQL scripts manually
- [CORE-2660] - Multiple contexts not recognized in formatted SQL when using AND
- [CORE-2662] - NumberFormatException with default values of type 'real' in postgresql
- [CORE-2663] - New MySQL 5.7.x reserved keywords are not being escaped
- [CORE-2664] - createChangeLog has incorrect nesting of constraints in YAML format
- [CORE-2666] - InsertSetGenerator hard-codes the InsertGenerator
- [CORE-2669] - Impossible to extend BaseLiquibaseTask in a non-deprecated way
- [CORE-2670] - Impossible to create table on mssql with remarks containing apostrophes
- [CORE-2671] - oracle timestamps with time zone
- [CORE-2672] - createSequence with order produces invalid statement on postgresql
- [CORE-2673] - includeAll relativeToChangelogFile doesn't work for FileSystemResourceAccessor
- [CORE-2674] - LoadUpdateData with onlyUpdate="true" generates invalid statements for Oracle DB
- [CORE-2677] - Dropping a postgres index fails
- [CORE-2679] - Hibernate diffChangeLog NullPointerException @ MissingPrimaryKeyChangeGenerator.fixMissing(MissingPrimaryKeyChangeGenerator.java:76)
- [CORE-2680] - Generating a futureRollbackSql when using "classpath:" prefix doesn't recognise any of the ran change sets.
- [CORE-2681] - H2 (automatic mixed mode): createTable: columns remarks ignored
- [CORE-2683] - dropAll dropping sequences that have been dropped via earlier cascade
- [CORE-2684] - Context is ignored with runOnChange and including file (sqlFile/loadUpdateData)
- [CORE-2686] - StandardChangeLogHistoryService.hasDatabaseChangeLogTable value is cached too aggressively
- [CORE-2687] - <sqlFile endDelimiter="go" if endDelimiter has whitespace in my sql it is not spliting
- [CORE-2688] - Loading data from csv with number of rows dividable by 51
- [CORE-2689] - TIMESTAMP parameters dropped for DB2
- [CORE-2690] - SetTableRemarksGenerator double escapes remark
- [CORE-2693] - Postgresql dropAll with serial columns fails because tables are dropped then sequences which no longer exist
- [CORE-2694] - "national character varying" type is not recognized
- [CORE-2698] - <sqlFile> Oracle scripts with ending / are not actually getting executed even with splitStatements="false"
- [CORE-2699] - concurrency causes NullPointerException in DatabaseObjectComparatorFactory.getInstance()
- [CORE-2705] - diffChangeLog generates dropColumn when dropping an index with a computed column
- [CORE-2706] - Two sides of equal are the same
- [CORE-2709] - endDelimiter regexp problem
- [CORE-2711] - Cannot load CSV via loadData
- [CORE-2713] - CreateView disregards replaceIfExists=true when fullDefinition=true
- [CORE-2715] - tableExists and columnExists preconditions fail on MySQL
- [CORE-2718] - H2 Database should query for default schema instead always use PUBLIC
- [CORE-2719] - Oracle: Cannot snapshot primary keys on lower-case tables
- [CORE-2720] - DB2: Capture full view definition in snapshot/generateChangeLog to support column options
- [CORE-2721] - endDelimiter regex does not work in SQL changelogs/changesets
- [CORE-2725] - DB2: Don't include system-generated indexes
- [CORE-521] - Handle Timestamp with Time Zone types where supported
- [CORE-2448] - New "created" attribute on changeSet
- [CORE-2478] - Liquibase dropAll command line does not allow dropping multiple schemas
- [CORE-2540] - Allow vendor independent SEQUENCE definition
- [CORE-2541] - Add support for registering a Change Exec Listener on command line
- [CORE-2560] - Add new runOrder="first|last" attribute to control when a changeSet is ran
- [CORE-2577] - Add resourceComparator attribute to includeAll to override sorting
- [CORE-2578] - Added addUniqueConstraint deferrable"support for Postgresql
- [CORE-449] - Same changeLog can be included multiple times
- [CORE-1969] - Support for AND/OR context expressions in formatted sql
- [CORE-2100] - formatted sql validCheckSum
- [CORE-2115] - Really slow when using fat jars
- [CORE-2225] - please add the OLD check sum to the validation error message
- [CORE-2336] - Use a grammer for parsing SQL rather than regexps
- [CORE-2419] - Support fluent/builder-style change properties
- [CORE-2455] - Improve messages in databasechangelog.description column
- [CORE-2463] - Don't include liquibase tables in dbdoc
- [CORE-2493] - ExecuteShellCommand improvements
- [CORE-2497] - Support setColumnRemarks and setTableRemarks on MSSQL
- [CORE-2499] - Support for commenting lines in csv files
- [CORE-2539] - Ensure each data row in CSV has same # cols as header
- [CORE-2584] - ValidCheckSum is valid for both stored database values and current changeSet checksum
- [CORE-2589] - Output xml changelogs as xml version="1.1"
- [CORE-2612] - Oracle: include BYTE in CHAR and VARCHAR types from snapshot/generateChangeLog
- [CORE-2619] - Make changeLogFile optional for changeSetExecuted
- [CORE-2622] - Maven: Support ISO date syntax for rollback
- [CORE-2626] - Use schemaName in createProcedure
- [CORE-2638] - GenerateChangeLog should include replaceIfExists=true for changed views
- [CORE-2640] - Better handling of replaceIfExists in createProcedure
- [CORE-2651] - Derby: Default unknown version to 10.6 to support sequences
- [CORE-2652] - Formatted sql precondition not expanding changelog parameters
- [CORE-2657] - Add new DATABASECHANGELOG.DEPLOYMENT_ID column to track changeSets deployed together
- [CORE-2658] - Move the liquibase-debian module to a separate profile
- [CORE-2703] - MySQL NCLOB should conver to LONGTEXT CHARACTER SET utf8
- [CORE-2724] - Support offline databases in ant-tasks

Changes in version 3.4.2 (2015.11.24)

- [CORE-2328] - AbstractExecutor should implement execute(Change)
- [CORE-2475] - Informix: Insert valueComputed not used correctly
- [CORE-2543] - Improved support for time stamps in oracle insert change data types
- [CORE-1778] - createSequence doesn't work on MSSQL
- [CORE-1840] - Liquibase fails when run on a computer that can't connect to the internet
- [CORE-2273] - Oracle char column snapshot not differentiating between a default value of 0 and '0'
- [CORE-2285] - Code in Liquibase class inconsistent
- [CORE-2317] - Custom ConfigurationValueProvider not working
- [CORE-2349] - loadUpdateData does not escape column names in "ON DUPLICATE" clause
- [CORE-2407] - Derby keywords not escaped when used as column/table names
- [CORE-2447] - In sybase, schema is bad preixed
- [CORE-2466] - Rollback referencing a change set in another file cannot be parsed
- [CORE-2467] - SSO with jtds MSSQL doesn't work after 3.3.5 for update - null user error
- [CORE-2469] - Error in method ColumnConfig.setValueNumeric(String)
- [CORE-2470] - MSSQL: FindForeignKeyConstraintsGeneratorMSSQL doesn't honor specified schema
- [CORE-2480] - Primary key exist works fine with 3.3.3 not with 3.4.0 in SQL SERVER
- [CORE-2481] - Primary key creation issue with informix
- [CORE-2482] - Number type issue with informix
- [CORE-2484] - dropAll command crashes because it drops sequences before tables
- [CORE-2487] - updateSql does not output anything for prepared statements
- [CORE-2490] - If you have more than one :name token in the where clause of a delete change, you get an Exception
- [CORE-2491] - Shouldn't a custom change produce a warning if run in updateSql mode
- [CORE-2492] - Logger extension liquibase-slf4j no longer usable with 3.4.1
- [CORE-2494] - Pgsql: Exporting/generating badly formatted SQL
- [CORE-2498] - Generation of TIMESTAMP(29) causing error in PSQL log TIMESTAMP(6) WITHOUT TIME ZONE
- [CORE-2500] - Fast check of ColumnExistsPrecondition causing transaction abort on PostgreSQL database
- [CORE-2505] - Missing keywords for H2 database
- [CORE-2510] - loadData on MySQL with > 50 rows fails
- [CORE-2544] - LogFactory does not get reset
- [CORE-2549] - Performance regression in resolving local host
- [CORE-2554] - updateSql command fails on validation when upgrading (2.0.5 ->3.4.1)
- [CORE-2566] - Maven - setting skip= true does not work
- [CORE-2571] - primaryKeyExists precondition generating wrong query
- [CORE-2576] - The 'dbms' attribute on <createProcedure> is not not taken into account when parsing changes
- [CORE-2579] - dropAll failed for Oracle 12c
- [CORE-2580] - Escape column, table and schema on ColumnExistsPrecondition
- [CORE-2588] - Bad cast
- [CORE-2590] - Default constraint names are not quoted
- [CORE-2596] - DatabaseChangeLogLock race condition exists if two nodes both try to create the table
- [CORE-2598] - Postgres generateChangeLog: "length for type varchar cannot exceed ********"

Changes in version 3.4.1 (2015.07.28)

- [CORE-998] - Changing index columns leads to wrong output order in the change log xml file.
- [CORE-2104] - ConcurrentModificationException iterating over System.getProperties().entrySet()
- [CORE-2385] - IncludeAll does not work when runing liquibase from inside a jar
- [CORE-2405] - Collation not preserved, depending on configuration
- [CORE-2406] - Escaped built-in data types should be lower case
- [CORE-2408] - Unknown column 'LABELS' in 'field list'
- [CORE-2410] - Snapshot should not include paramaters for MSSQL geometry, geography or sql_variant types
- [CORE-2411] - BLOB string default values not quoted
- [CORE-2412] - Handle Oracle BFILE types
- [CORE-2414] - CLONE - generateChangeLog creates DOUBLE(22) instead of double in MySql
- [CORE-2415] - Custom Logger configuration does not work anymore
- [CORE-2416] - Diff drops and creates primary keys for all tables
- [CORE-2418] - Liquibase 3.4.0 tries to do INSERT instead of UPDATE-Statements with Postgres
- [CORE-2421] - MySQL column sizes are off by 1 in BIGINT and INT for diffChangeLog
- [CORE-2422] - Liquibase intialisation failed
- [CORE-2423] - Sybase IQ : strange procedure called
- [CORE-2426] - Default schema name missing quotes.
- [CORE-2427] - Better handle MSSQL stored procedures with a different defaultSchema and replaceOnExists=true
- [CORE-2428] - liquibase 2.0.3 to 3.3.3
- [CORE-2435] - includeSystemClasspath switch actually includes SystemClassLoader if false
- [CORE-2436] - Logging in ClassLoaderResourceAccessor prevents installation of custom Logger
- [CORE-2437] - Index.toString() contains "unique" if and only if index is NOT unique
- [CORE-2438] - DeleteGenerator does not handle parameter names and values with $ or \ properly
- [CORE-2440] - Not possible to override DefaultLogger using a Logger in a non-liquibase package.
- [CORE-2441] - Creation of foreign key fails in MySQL if database name contains dashes
- [CORE-2442] - Creating MD5 checksum fails if changeSet id contains the character "?"
- [CORE-2443] - Liquibase 3.4.0 ignores third party loggers in certain situations
- [CORE-2446] - endDelimiter splitting does not work in plain SQL files (regression)
- [CORE-2452] - Index names should be quoted on SQL Server
- [CORE-2458] - loadUpdateData will not update
- [CORE-2460] - Postgres index names cannot include schema name
- [CORE-2433] - quoting error in table creation
- [CORE-2359] - Consistently read dataTypeId for all databases
- [CORE-2419] - Support fluent/builder-style change properties
- [CORE-2449] - Correctly detect "Sybase IQ"
- [CORE-2450] - Non-bash /bin/sh gives "[[ not found" error
- [CORE-2451] - SQL scripts should have "USE <database>" in the header on SQL Server
- [CORE-2453] - Informix: Return null for connection schema name
- [CORE-2459] - Un-change Formatted SQL stripComments default back to true
- [CORE-2461] - Don't do DATABASECHANGELOG ALTER statements if column types are different

Changes in version 3.4.0 (2015.06.24)

- [CORE-14] - Dropping default values with MS-SQL
- [CORE-822] - Add a tag to add/update table/column remarks
- [CORE-864] - loaddata performance enhancement
- [CORE-1411] - MariaDB support
- [CORE-2254] - Ability to save snapshot for later comparison
- [CORE-2257] - Ability to use a previously saved database snapshot in diff and generateChangeLog
- [CORE-2302] - Add ability to load nested object/collection properties and BigDecimal properties automatically
- [CORE-2306] - Support passing in a script for rollback to override rollback logic included in the changelog
- [CORE-2308] - Track changeSet contexts and labels in databasechangelog table
- [CORE-2345] - Add XML Type
- [CORE-419] - Allowing ASC and DESC in index definitions
- [CORE-562] - Allow naming of not null constraints
- [CORE-715] - indexExists does a full snapshot
- [CORE-1731] - Support autoincrement in oracle 12c
- [CORE-2124] - Ability to pass properties to a JDBC driver required
- [CORE-2132] - Error message for missing sqlFile reference should be more descriptive
- [CORE-2147] - HsqlDatabase should emit uppercase names when quoting reserved words to preserve case insensitivity
- [CORE-2171] - New ChangeExecListener.runFailed method
- [CORE-2177] - Support NOT(X) syntax for labels
- [CORE-2185] - Few fields needs to be changed as protected and need additional field in RanChangeSet
- [CORE-2217] - Add DataTypeFactory support for delimited data type names, improve resolution of MSSQL data types
- [CORE-2228] - New usingIndexName attribute on addPrimaryKey
- [CORE-2236] - Support .yml extension in YamlChangeLogSerializer
- [CORE-2244] - Handle generating SQL Server DDL where ANSI NULL Default is false
- [CORE-2249] - Index and UniqueConstraint equivalence check should take name into account
- [CORE-2288] - Do not check/updatedatabasechangelog table on status
- [CORE-2292] - New tagExists command for command line
- [CORE-2298] - Allow HSQL to use defaultValueComputed for certain allowed functions on datetime type columns
- [CORE-2299] - Add capability to ignore missing or empty folder with includeAll
- [CORE-2307] - ChangeLog table name option in command line tool
- [CORE-2309] - global/local properties on changesets
- [CORE-2320] - MinGW (Git Bash) support for shell
- [CORE-2334] - Disable CREATE TABLE DATABASECHANGELOG generation when running on OfflineConnection
- [CORE-2336] - Use a grammer for parsing SQL rather than regexps
- [CORE-2358] - Improve data types for Liquibase tables in MSSQL
- [CORE-2359] - Consistently read dataTypeId for all databases
- [CORE-2363] - Improve robustness of MSSQL database case-insensitivity check
- [CORE-2371] - Improve Oracle snapshot performance
- [CORE-2386] - Set the connection default catalog/schema if defaultCatalogName or defaultSchemaName is set
- [CORE-2397] - MSSQL View Snapshot should not use sp_helptext
- [CORE-2399] - DBDoc improvements
- [CORE-2404] - Ability to preserve TEXT type in mssql snapshot and update
- [CORE-842] - Tag database not taking orderexecuted into account
- [CORE-1296] - drop column on ms sql server
- [CORE-1424] - SQL Generation ignores DATETIME parameters
- [CORE-1542] - Sequence is dropped not until a second run of dropAllDatabaseObjects on PostgreSQL
- [CORE-1738] - loadData from csv fails for boolean column (if another column present)
- [CORE-1749] - Update change command does not respect whereParams
- [CORE-1803] - DropAll doesn't delete sequences if they are used as default value in postgres
- [CORE-1904] - Slow indexExists performance in Oracle
- [CORE-1924] - SQLServer diff - DATETIME2 not being handled correctly
- [CORE-2005] - /usr/bin/liquibase: Syntax error: "else" unexpected
- [CORE-2018] - Quotes stripped from index filter_conditions on snapshot
- [CORE-2019] - Comments in empty rollback prevent execution
- [CORE-2041] - Escaping of reserved keywords in HSQLDB
- [CORE-2096] - DiffChangeLog with changed indexes generates drop/add in the wrong order
- [CORE-2109] - dropAll fails for statements the database requires to run non-transactionally
- [CORE-2113] - Informix text datatype fixes
- [CORE-2133] - Oracle: GenerateChangeLog of a table with DEFAULT VALUE NULL creates defaultValueComputed="NULL"
- [CORE-2167] - Issues with generateChangeLog of unique constraints on DB2
- [CORE-2169] - offline mode seems non-deterministic
- [CORE-2178] - Fatal exception acquiring lock in SQL Server databases with case-sensitive collation
- [CORE-2196] - Ant: "Unable to update database." without explanation
- [CORE-2209] - Oracle snapshot sometimes creates "GENERATED ALWAYS AS (null)"
- [CORE-2211] - Liquibase tries to execute commented lines in custom SQL file
- [CORE-2219] - DB2 for zOs - adding primary key always emits REORG but REORG does not exist on Db2 for zOs
- [CORE-2222] - TIMESTAMP parameters dropped for PostgreSQL
- [CORE-2224] - Index uniqueness is not always recognized correctly
- [CORE-2227] - CLONE - UpdateSQL needs to append a "/" to the end of createProcedure for Oracle
- [CORE-2232] - Support schema other than public on PostgreSQL
- [CORE-2233] - Oracle Timestamp precision lost in generateChangeLog
- [CORE-2234] - columnExists precondition could be much faster (Oracle, mssql)
- [CORE-2237] - YamlChangeLogSerializer does not correctly serialize a changeset
- [CORE-2251] - Adding column with type DATETIME doesn't work for PostgreSQL
- [CORE-2252] - XMLChangeLogSerializer writes array object for rollback tag content
- [CORE-2253] - Handle oracle varchar <-> clob conversions in diffChangeLog
- [CORE-2256] - Drop Sequences before Tables
- [CORE-2266] - DiffChangeLog: Invalid changelog when a primary key backing index is changed
- [CORE-2267] - Rollback fails for mixed-case objects created with QUOTE_ALL_OBJECTS
- [CORE-2270] - Doubledash inside quoted text causes parsing error
- [CORE-2271] - CLONE - DiffChangeLog: Invalid changelog when a unique constraint backing index is changed
- [CORE-2272] - DiffChangeLog must drop foreign keys before primary keys
- [CORE-2273] - Oracle char column snapshot not differentiating between a default value of 0 and '0'
- [CORE-2275] - YAML update fails with Unexpected error running Liquibase: java.util.LinkedHashMap cannot be cast to java.util.List
- [CORE-2281] - Oracle snapshot performance issue with many multiple views
- [CORE-2291] - ObjectQuotingStrategy not reset correctly after changeSet
- [CORE-2295] - includeAll tries to load all files instead only *.xml
- [CORE-2300] - Unsigned Int / Bigint cannot be created
- [CORE-2305] - Snapshot output too verbose
- [CORE-2316] - Data type registry occasionally returns wrong data type implementation
- [CORE-2321] - Liquibase tag command tags too much
- [CORE-2324] - diffChangeLog does not handle changes in sequence incrementBy, maxValue or ordered
- [CORE-2331] - Support for MSSQL collation in data type description broken
- [CORE-2340] - Add support for extensions to override the built-in change log, snapshot serializers
- [CORE-2355] - Improve updateSQL performance
- [CORE-2361] - preConditions, rollback, property, include, includeAll cannot be serialized
- [CORE-2373] - Local DTD files not found in subdirectory
- [CORE-2378] - Adding a new "CustomChange" triggers ClassNotFoundException
- [CORE-2380] - Support reading of gzip files
- [CORE-2381] - Fix unique constraint generator for informix
- [CORE-2383] - Change formatted SQL stripComments default from "true" to "false"
- [CORE-2385] - IncludeAll does not work when runing liquibase from inside a jar
- [CORE-2387] - dropPrimaryKey without constraint name on sql server doesn't query schema correctly
- [CORE-2388] - Views not equal in different schemas
- [CORE-2390] - NullPointerException when generating changelog
- [CORE-2391] - column type doesn't respect unsigned
- [CORE-2393] - changeSet contexts created with maven generateChangeLog are in parentheses
- [CORE-2401] - MSSQL handling timestamp according to sql standard, not sqlserver usage
- [CORE-2402] - Oracle NCLOB defaultValues not read correctly


Changes in version 3.3.5 (2015.05.28)

-  [CORE-2379] - Project.version set to 3.3.4-SNAPSHOT in 3.3.4 final


Changes in version 3.3.4 (2015.05.27)

-  [CORE-2360] - Maven - Skip is active by default
-  [CORE-2199] - Liquibase adds a semicolon after a stored proc definition making the stored proc unusable
-  [CORE-2344] - Unknown host exception on OS RHEL 6.5
-  [CORE-2346] - IncludeAll does not work when runing liquibase from inside a jar
-  [CORE-2357] - alterSequence does not work as expected when you need to change the cache size
-  [CORE-2366] - Derby Network server works with command line but not with maven "Liquibase skipped due to maven configuration"
-  [CORE-2368] - No SQL outputted for <update> change

Changes in version 3.3.3 (2015.04.28)

-  [CORE-1768] - Oracle dropAll fails on spatial tables and sequences
-  [CORE-1840] - Liquibase fails when run on a computer that can't connect to the internet
-  [CORE-1857] - Wrong column size detection on varchar2 fields with char as datatype
-  [CORE-1866] - Filtering changelog list by includeAll tag is not working
-  [CORE-1943] - Handle Error: InetAddress.getLocalHost().getHostName() UnknownHostException results in NoClassDefFoundError
-  [CORE-1958] - Column type of "TIMESTAMP(6)" under MySql converted to TIMESTAMP dropping fractional seconds
-  [CORE-1967] - includeAll uses full file path for sql changelogs
-  [CORE-2023] - Problem using includeAll with SpringLiquibase
-  [CORE-2126] - Postgres 9.3 - Drop table With Cascade - Not Supported
-  [CORE-2156] - Resource loader can't load changelog file
-  [CORE-2186] - AbstractResourceAccessor#convertToPath(String, String) fails for processing includeAll from Classpath
-  [CORE-2192] - NoSuchMethodException when generating offline Oracle migration script
-  [CORE-2199] - Liquibase adds a semicolon after a stored proc definition making the stored proc unusable
-  [CORE-2202] - liquibase.should.run inverted boolean
-  [CORE-2204] - valueNumeric not being set when using prepared statements
-  [CORE-2206] - diffChangeLog with JPA-annotated entities causes ConcurrentModificationException
-  [CORE-2208] - Typo in message
-  [CORE-2210] - java.lang.NullPointerException when file is empty
-  [CORE-2214] - When inserting string value starting and ending with apostrophes (quotes) the value is not quoted in the generated SQL
-  [CORE-2218] - Regression on modifyDataType : VARCHAR2 was supported on 3.2...and fails on 3.3
-  [CORE-2239] - Remarks attribute in renameColumn causes parse error
-  [CORE-2240] - setDropFirst(true) still broken on empty database
-  [CORE-2262] - 3.3.2 ant task dies on NPE in ChangeLogParameters
-  [CORE-2263] - Index Snapshot - doesn't include upper cased name indexes when db is NOT case sensitive
-  [CORE-2274] - Ant Upade Task does not consider changeLogFile correctly if it is contained in a JAR
-  [CORE-2279] - Rollback fails in MS SQL 2008 using liquibase 3.3.2
-  [CORE-2284] - Creating a DatabaseChangeLog() results in NPE
-  [CORE-2290] - Liquibase gives different results from Ant and the command line
-  [CORE-2301] - Regression from 3.2.3 in mssql 2000 unsupported usage of varchar(max) and sys.extenden_properties
-  [CORE-2304] - Autoincrement on type INT4 fails
-  [CORE-2310] - IncludeAll Fails with Unknown Reason Error
-  [CORE-2315] - NPE in CommandlineResourceAccessor
-  [CORE-2325] - Liquibase - New versions break DB create
-  [CORE-2329] - Escaped reserved keywords in HSQL are stored in lower case instead of upper case.
-  [CORE-2330] - includeAll uses full file path with includeAll
-  [CORE-2261] - UpdateSQL needs to append a "/" to the end of createProcedure for Oracle
-  [CORE-2287] - Improve support for Groovy-based tests in Eclipse
-  [CORE-2296] - Upgrade Groovy and Spock to maintained versions
-  [CORE-2318] - Add support for converting BigDecimal objects to a SQL string via DataTypeFactory

Changes in version 3.3.2 (2015.01.08)
- Support for ignoring objects in diffChangeLog and generateChangeLog
- Bugfixes

- [CORE-875] - Ignore tables for diffs and generateChangelog
- [CORE-1877] - SQLOutput prints endDelimiter regexes
- [CORE-2114] - AddAutoIncrement on Postgres does not work when changes are applied on a specific schema
- [CORE-2141] - handling dependencies and WAR as classpath
- [CORE-2166] - SpringLiquibase: includeAll within jar causes SetupException
- [CORE-2172] - dropPrimaryKey without constraint name on sql server doesn't honour schema information
- [CORE-2174] - Bad exception handling in OracleDatabase.setConnection
- [CORE-2180] - NPE with bad name
- [CORE-2182] - ClassLoader leak due to shutdown hooks


Changes in version 3.3.1 (2014.12.08)
- IncludeAll bugfixes
- Performance improvements
- Datatype handling fixes
- Fix for JSON parsing
- Other bugfixes

- [CORE-1920] - SpringLiqubase includeAll is not including files
- [CORE-2009] - ClassCastException when executing a custom task change (AntClassLoader problem)
- [CORE-2097] - "mvn liquibase:futureRollbackSQL" asks for tag, count or date
- [CORE-2099] - SQLAnywhere support (Driver not capable)
- [CORE-2103] - changelogSchemaName/changelogCatalogName configuration options will not work on Oracle DB
- [CORE-2104] - ConcurrentModificationException iterating over System.getProperties().entrySet()
- [CORE-2105] - Maven profile performing dropAll and update on Oracle failing with an error on populated database.
- [CORE-2107] - LOWER() keyword fails on Postgres createIndex task
- [CORE-2108] - dropAll command trying to drop column on table that has already been dropped
- [CORE-2114] - AddAutoIncrement on Postgres does not work when changes are applied on a specific schema
- [CORE-2116] - Could not find implementation of liquibase.logging.Logger
- [CORE-2118] - Change default diffChangeLog/generateChangeLog objectQuotingStrategy back to LEGACY
- [CORE-2119] - Bad finally block in SpringLiquibase.afterPropertiesSet()
- [CORE-2120] - LoadUpdateData with value=NUMERIC quoting values
- [CORE-2121] - DB2: DiffChangeLog/GenerateChangeLog/DropAll sees alias column and tries to drop/add them
- [CORE-2127] - updateSQL creates duplicate DATABASECHANGELOGLOCK tables
- [CORE-2130] - setFetchSize to a negative value breaks Oracle JDBC Driver
- [CORE-2134] - ExecuteCommand won't run with no os attribute.
- [CORE-2136] - Mysql must quote PARTITION as a keyword
- [CORE-2137] - Special characters (&#13;) copied during generateChangelog on DB2/400
- [CORE-2139] - H2Database.supportsDropTableCascadeConstraints() returns false
- [CORE-2141] - handling dependencies and WAR as classpath
- [CORE-2142] - generateChangeLog not including all columns in a table
- [CORE-2146] - snakeyaml is pulled in as transitive dependency for using projects
- [CORE-2149] - Liquibase command line fails
- [CORE-2150] - On the 3.3.4.11.0, liquibase --version returns 3.2.0
- [CORE-2153] - Liquibase 3.2.1 is no longer compatible with Oracle 9
- [CORE-2155] - diffTypes=data fails with java.sql.SQLException: Attribute value not valid (dataOutputDirectory attribute causes build to fail)
- [CORE-2156] - Resource loader can't load changelog file
- [CORE-2157] - SQLException if there are single quotes in ChangeSet
- [CORE-2159] - Datetime2 no longer used for MSSQL
- [CORE-2161] - includeAll relativeToChangelogFile="true" doesn't work
- [CORE-2164] - SpringLiquibase: includeAll within jar causes NullPointerException
- [CORE-2115] - Really slow when using fat jars
- [CORE-2125] - Make DatabaseChangeLog#include(String, boolean, ResourceAccessor) public
- [CORE-2148] - Build failure on jdk-1.8
- [CORE-2152] - Change logs in json format not processed by liquibase - parsing errors

Changes in version 3.3.0 (2014.11.03)
- New "label" attribute on changeSet
- New empty and output change tags
- Support for clustered/nonclustered primary keys and indexes
- Saving of remarks in mysql and mssql
- Official RPM and DEB packages
- Refactoring/update of Ant integration

- [CORE-16] - Support for nonclustered primary keys in mssql
- [CORE-54] - Support System Properties in Maven Plugin
- [CORE-1528] - Installer for Liquibase
- [CORE-1598] - support for rename sequence
- [CORE-1914] - New Change function: output
- [CORE-1942] - Support for changeSet labels
- [CORE-549] - relativeToChangelogFile for loadData, loadUpdateData, sqlFile
- [CORE-1438] - createView should support having the entire view definition in the change body
- [CORE-1502] - CLONE - UpdateSQL needs to append a \r/\n to the end of createProcedure for Oracle
- [CORE-1654] - logicalFilePath support in formatted sql
- [CORE-1660] - remarks attribute is ignored in MSSQL
- [CORE-1932] - support for encrypted passwords / custom properties
- [CORE-1946] - Have a rpm package for liquibase (built with maven)
- [CORE-1963] - Ability to define full CREATE VIEW statement in <createView> change.
- [CORE-1990] - Preserve inline comments in view snapshots in mssql
- [CORE-2060] - Support liquibase.properties files with unknown properties
- [CORE-2061] - Improvements to Informix support
- [CORE-2062] - Add onlyUpdate flag to loadUpdateData
- [CORE-2064] - Use ignoreClassPathPrefix for rollback as well
- [CORE-2065] - Use DOUBLE PRECISION for DOUBLE with Firebird
- [CORE-2066] - Support for outputFile in command line
- [CORE-2067] - Refactor Ant Task codebase
- [CORE-2068] - New liquibase.hostDescription property for additional details in the DATABASECHANGELOGLOCK table
- [CORE-2069] - Use prepared statement in <update> change whenever a clob type is used
- [CORE-2072] - Do not include Oracle internal tables in snapshot/diff
- [CORE-870] - Postgres, in an ALTER TABLE ALTER COLUMN statement, sometimes needs USING clause
- [CORE-945] - Oracle : Temporary tables are created as regular tables
- [CORE-1463] - Views not generated correctly with generateChangelog
- [CORE-1556] - remarks attribute ignored for mysql
- [CORE-1723] - unable to update on DB2/400, version V6R1, on jt400-6.7.jar
- [CORE-1745] - afterColumn not working in MySQL
- [CORE-1774] - Autocommit not restored on close in SpringLiquibase
- [CORE-1882] - NullPointerException when MySQL foreign key points to an invalid table
- [CORE-1919] - SpringLiquibase fails when dropFirst is true
- [CORE-1922] - Sequence is not a reserved object name in HSQLDB
- [CORE-1925] - liquibase scripts can not represent clustered indexes
- [CORE-1937] - Oracle Float and VARCHAR precisions in changelog generated by generateChangeLog are incorrect
- [CORE-1952] - liquibase loadData does not properly load numeric field in boolean always as false
- [CORE-1956] - Double and float converted to FLOAT8(*, 17) and FLOAT4(*, 8) in PostgreSQL
- [CORE-1958] - Column type of TIMESTAMP(6) under MySql converted to TIMESTAMP dropping fractional seconds
- [CORE-1974] - dbchangelog-3.1.xsd missing <empty>
- [CORE-1977] - CreateSequence with cacheSize=0 failing on Oracle
- [CORE-1979] - MSSQL should not include parameters in SYSNAME data types
- [CORE-1981] - Parameters set in included file are no longer set in 3.2.0
- [CORE-1982] - Snapshot outputs defautlValueDate as defaultValueComputed on MSSQL for dates not in ISO format with a T in the middle
- [CORE-1986] - includeAll from changeLogs within a jar is not working
- [CORE-1988] - Reported size for Oracle NVARCHAR2 columns is wrong
- [CORE-1993] - Drop table with cascade is not supported by Sybase
- [CORE-1996] - addNotNullConstraint on h2 database has unexpected side effects
- [CORE-1997] - Bit changelog default value of 1 executed as 0
- [CORE-2002] - AbstractResourceAccessor generates path in a unpredictable way
- [CORE-2010] - Oracle data type SDO_GEOMETRY snapshotted as SDO_GEOMETRY(1)
- [CORE-2014] - applyToRollback property ignored when rollback changes are specified
- [CORE-2015] - DiffChangeLog writes to the wrong point in the file on windows if file uses \n not \r\n
- [CORE-2020] - Oracle default value current_timestamp converted to systimestamp
- [CORE-2021] - Column remarks not snapshotted in mssql
- [CORE-2026] - Oracle columns of type ANYDATA are snapshotted with a size
- [CORE-2028] - generateChangeLog on SQL Anywhere 11.0.1 throws DatabaseException Driver Not Capable
- [CORE-2032] - Snapshot incorrectly including clob/blob sizes on diff
- [CORE-2051] - Not quoting VIEW params with spaces when snapshotting
- [CORE-2054] - Add new 'computed' column attribute to differentiate between an actual column name and a function as a column
- [CORE-2063] - Fix for H2 autoincrement start with and increment by syntax
- [CORE-2070] - dropAllForeignKeyConstraints does not work on Firebird databases
- [CORE-2075] - generateChangelog generates bad definition for TIME type
- [CORE-2080] - Liquibase empty change not present in XSD version 3.2
- [CORE-2081] - PrimaryKeyExists precondition without tableName is broken
- [CORE-2082] - Column snapshot on PostgreSQL does not include precision information for numeric data type
- [CORE-2087] - Executing against Oracle doesn't respect liquibaseSchemaName or liquibaseCatalogName
- [CORE-2088] - outputDefaultSchema and outputDefaultCatalog command line parameters not respected
- [CORE-2093] - Error: Property 'relativeToChangelogFile' not found on object type liquibase.change.core.LoadDataChange
- [CORE-2094] - Liquibase.dropAll() should reset the lock service
- [CORE-2095] - Invalid generated changeset for mysql bit with defaultValue 0

Changes in version 3.2.3 (2014.11.03)
- Bugfixes

- [CORE-1919] - SpringLiquibase fails when dropFirst is true
- [CORE-1987] - 'mvn liquibase:diff' does not find any differences between databases
- [CORE-1988] - Reported size for Oracle NVARCHAR2 columns is wrong
- [CORE-1989] - Cannot set objectQuotingStrategy on root databaseChangeLog node
- [CORE-2002] - AbstractResourceAccessor generates path in a unpredictable way
- [CORE-2003] - Could not find implementation of liquibase.logging.Logger
- [CORE-2042] - If liquibase.jar is nested in another jar/war/ear, it fails to start with a 'cannot find implementation of liquibase.logging.Logger' error
- [CORE-2058] - Load/Update tags should use 'is null' not '= null' for null comparisons
- [CORE-2070] - dropAllForeignKeyConstraints does not work on Firebird databases
- [CORE-2075] - generateChangelog generates bad definition for TIME type
- [CORE-2080] - Liquibase 'empty' change not present in XSD version 3.2
- [CORE-2065] - Use DOUBLE PRECISION for DOUBLE with Firebird
- [CORE-54] - Support System Properties in Maven Plugin


Changes in version 3.2.2 (2014.07.23)
- Fix for bug in checksum computation vs. 3.1.1

- [CORE-1938] - defaultValueNumeric="0" or defaultValue="0" is translated to 0.0
- [CORE-1950] - Checksum validation failed after Liquibase upgrade (3.1.1 -> 3.2.0)
- [CORE-1959] - generateChangeLog without changeLogFile - better error message

Changes in version 3.2.1 (2014.07.18)
- Fixes to checksums on valueNumeric, defaultValueNumeric fields
- Fixes to checksums on createProcuedure and sql changes
- Fixed null pointer exception performing a tag
- Fixes to Spring support
- Fixed issue handling changeSet with multiple comments
- Fix support for sqlFile tags in rollback
- Fixed sqlFile not using changelog parameters
- Other bug fixes

- [CORE-1844] - bulkFetch of foreign keys on Oracle is very slow
- [CORE-1918] - Multiple comment blocks in changesets no longer works
- [CORE-1920] - SpringLiqubase includeAll is not including files
- [CORE-1922] - 3.2.0 Regression due to CORE-1721
- [CORE-1923] - Error raised: Unknown Reason when doing tagging via command line
- [CORE-1930] - Snapshot command returns no information on MSSQL
- [CORE-1933] - [3.0.8 -> 3.2.0] pom.xml: 2Mb of superfluous dependencies
- [CORE-1934] - file path in databasechangelog is absoulte since liquibase 3.2.0 when using includeAll inside a jar
- [CORE-1936] - NullPointerException while diffing database against hibernate.cfg.xml
- [CORE-1938] - defaultValueNumeric="0" or defaultValue="0" is translated to 0.0
- [CORE-1940] - Maximum Oracle Sequence fails to be parsed as a BigInteger
- [CORE-1944] - NullPointerException when tagging through Ant before applying changesets
- [CORE-1947] - Liquibase dependency to jetty-servlet/velocity should be <optional>true</optional>
- [CORE-1950] - Checksum validation failed after Liquibase upgrade (3.1.1 -> 3.2.0)
- [CORE-1957] - Using VARCHAR2 column type fails for Hsqldb running in oracle syntax mode
- [CORE-1960] - "Could not find implementation of liquibase.logging.Logger" starts appearing after migration from 3.1.1 to 3.2.0
- [CORE-1970] - NullPointerException on rollback with <sqlFile> tag
- [CORE-1746] - Support <sqlFile> parameters
- [CORE-1951] - Regression on 3.2.0 : --diffTypes=data generates Unknown Reason Exception

Changes in version 3.2.0 (2014.06.02)

- Support for and/or/not logic in context expressions
- Better matching of default schemas in database comparisons
- Improvements to resource accessor logic
- Improved memory usage when diffing data
- Improved UTF8 usage
- Better handling if TIMESTAMP [WITH | WITHOUT] TIME ZONE datatypes in pgsql
- Standardization of parsing logic across XML/YAML and JSON
- New command line function: snapshot
- Start of SDK for easier Liquibase extension development and testing:
--- Database watch command
--- Standard "sdk workspace"
--- Beta version of database vagrant configurations
- Snapshot listener interface

- [CORE-1742] - Standardized system for managing configuration options in core and extensions
- [CORE-1751] - More detailed API for returning changeSet statuses
- [CORE-1783] - Command line option: snapshot
- [CORE-1815] - Liquibase SDK command to easily watch database
- [CORE-1821] - Snapshot listener interface

- [CORE-1116] - Allow for both "AND" and "OR" specifications of which contexts to run
- [CORE-1422] - Support changeset comments in formatted SQL
- [CORE-1536] - Support specifying the referenceDefaultSchemaName for diff operations
- [CORE-1635] - Generated changelog missing unique constraints should be output before foreign keys
- [CORE-1682] - More machine-independent handling of filesystem-located resources
- [CORE-1695] - Better handling of Postgres timestamp with/without time zone
- [CORE-1706] - Excessive reading from databasechangelog table
- [CORE-1726] - Added offline parameter to generate insert databasechangelog statements
- [CORE-1758] - Add outputDefaultSchema flags to ant
- [CORE-1776] - System-independent checksums
- [CORE-1823] - Search for liquibase.properties in classpath in additional to local filesystem
- [CORE-1874] - Improve XML and other parsers for better extensiblity
- [CORE-1905] - Yaml parser supports .yml extension as well as .yaml

- [CORE-209] - Double Create Database Lock Table using updateSQL
- [CORE-421] - NPE in MySqlDatabaseSnapshot during diff operation when "enum" or "timestamp" column was removed from table
- [CORE-461] - Wrong datatypes for timestamps generated with generateChangeLog
- [CORE-871] - Unable to use changeSetPath in rollback tag to refer to another change log file
- [CORE-877] - Bug with column type "TIMESTAMP WITHOUT TIME ZONE"
- [CORE-976] - GenerateChangeLog with data: java heap space error
- [CORE-1097] - Liquibase adds a semicolon after a stored proc definition making the stored proc unusable
- [CORE-1108] - Oracle : Unable to create complex primary key for loadUpdateData
- [CORE-1284] - Memory leak in ExecutorService
- [CORE-1563] - dropAll does not delete Sequences on Derby
- [CORE-1572] - Regression: diff generates full schema instead of changes when using liquibase-hibernate4
- [CORE-1578] - changeset checksum calculates differently on linux and windows
- [CORE-1601] - updateSql generates SQL with unexpected linefeeds, breaks SQLPlus
- [CORE-1642] - Special character issue after upgrade
- [CORE-1643] - DB2: defaultSchemaName and changelogSchemaName are not respected
- [CORE-1650] - dropAll doesn't work on Informix
- [CORE-1668] - defaultSchemaName ignored on createTable in H2
- [CORE-1673] - Empty default value is not recorded for MySQL
- [CORE-1676] - Colons in changeSet IDs no longer supported
- [CORE-1688] - The 'valuum' extension seems not to be working under version 3.0.8 of liquibase
- [CORE-1701] - Oracle: snapshot of default date/time values come back as to_date functions
- [CORE-1714] - 2->3.1 migration error
- [CORE-1715] - 2 -> 3.1 migration, 3.1 errors if there are single quotes in comments
- [CORE-1718] - foreignKeyConstraintExists precondition fails with constraints on unique keys
- [CORE-1721] - HsqlDatabase.escapeObjectName(...) ignores QUOTE_ALL_OBJECTS
- [CORE-1727] - Drop default constraint syntax for DB2 not correct
- [CORE-1728] - Only sequence name is captured by snapshot process and used in generateChangeLog
- [CORE-1733] - Data in diffChangeLog coming through as one row per changeSet
- [CORE-1734] - updateSQL is not including content from sqlFile (at least not when ran from Linux)
- [CORE-1739] - Liquibase ignores Oracle varchar precision
- [CORE-1743] - Snapshot VARCHAR(MAX) correctly on sqlserver
- [CORE-1744] - Derby: UpdateSQL not including creating databasechangeloglock table if needed
- [CORE-1748] - Maven Plugin does not use outputDefaultCatalog property
- [CORE-1750] - liquibase-modify-column 3.0 broken with liquibase-core 3.1.1
- [CORE-1752] - Oracle XML Datatype snapshot containing size
- [CORE-1753] - HSQLDB 1.8 does not support catalogs
- [CORE-1754] - Default value functions on Oracle snapshoted as "defaultValue"
- [CORE-1755] - DefaultValueComputed is quoted for Char, Varchar, Clob, MediumInt and SmallInt types
- [CORE-1756] - Oracle indexes with functions not returned correctly in generateChangeLog
- [CORE-1765] - Failed to drop a VIEW pointing on not existing table from another schema
- [CORE-1767] - Oracle snapshot not capturing all indexes
- [CORE-1772] - Informix cannot drop constraints
- [CORE-1774] - Autocommit not restored on close in SpringLiquibase
- [CORE-1775] - Informix error when creating primary key
- [CORE-1779] - User Defined Types Come back with Unnecessary Size specifier in diff
- [CORE-1782] - dropALL fails when tables are referenced by other tables in another schema
- [CORE-1784] - GenerateChangeLog with objects in multiple schemas returns objects from multiple schemas and empty createTable statements
- [CORE-1788] - dropAll does not work in not default schemas using postgresql
- [CORE-1794] - Drop index failing for sybase
- [CORE-1797] - Autoincrement on type Serial8 fails
- [CORE-1798] - Invalid type syntax in Informix for Int(10) and SMALLINT(5) and FRACTION(3)
- [CORE-1799] - Attempted recreation of DATABASECHANGELOG in informix
- [CORE-1817] - Foreign Key Constraints Built on Unique Constraints are not captured in generateChangeLog
- [CORE-1818] - SQL Server "smalldatetime" converted to "datetime" on update
- [CORE-1824] - GUID Default values not quoted correctly in MSSQL
- [CORE-1828] - sqlFile should fail when file does not exists
- [CORE-1831] - CREATE/DROP INDEX issue: Sybase ASE 15.7.0
- [CORE-1834] - generateChangeLog creates DOUBLE(22) instead of double in MySql
- [CORE-1840] - Liquibase fails when run on a computer that can't connect to the internet
- [CORE-1843] - includeAll does not alphabetize classpath loaded files
- [CORE-1853] - Liquibase generates invalid SQL for 'character varying' type
- [CORE-1856] - Ability for Change classes to verify update and rollback succeeded
- [CORE-1859] - DataTypeFactory doesn't take database into account
- [CORE-1861] - MSSQLDatabase.isCaseSensitive() provides a wrong information
- [CORE-1878] - Maven plugin behaviour different than command line with the same parameters
- [CORE-1881] - Case sensitivity issue with tableExists precondition
- [CORE-1893] - type="DATETIME" in changelog is converted to TIMESTAMP in MySql
- [CORE-1899] - Non-existing referenced sql files doesn't report error
- [CORE-1901] - DropPrimaryKey - Fails in Postgres
- [CORE-1906] - Diff between objects in different named default schemas doesn't see them as the same object
- [CORE-1912] - Unit tests fail due to dependency on system time zone

Changes in version 3.1.1 (2014.01.16)
Bug Fix release. Most critical bug is CORE-1704

- [CORE-1704] - Checksum errors for changeSets with createProcedure in 3.1.0 vs 3.0.x
- [CORE-1707] - TableRowCountGenerator shouldn't pass tableName as catalogName
- [CORE-1710] - Oracle: NUMBER data type size specification is deployed with a precision specifier even though precision isn't specified in the change log
- [CORE-1711] - rowCount doesn't work if only the tableName is given
- [CORE-1713] - liquibase.precondition.core.ForeignKeyExistsPrecondition exception
- [CORE-1715] - Errors if there are single quotes in comments
- [CORE-1709] - generateChangeLog returns tables created by Materialized Views as standard tables

Changes in version 3.1.0 (2014.01.09)
- Major Changes:
     Offline Datatabase Support
     CreateProcedures supports external files and dbms attribute
     New dropProcedure change
     New tableIsEmpty precondition
     New rowCount precondition
     Mysql enum & set support
     Mysql date/time/timestamp size parameter support
     MSSQL 2012 sequence support
     General Sybase & DB2 improvements
     Change- and precondition- level callbacks in ChangeExecListener
     Extensions can define custom XSDs and namespaces
     Maven support for futureRollbackSql command

- [CORE-1424] - SQL Generation ignores DATETIME parameters
- [CORE-1579] - DefaultPackageScanClassResolver should suppress LinkageErrors
- [CORE-1626] - createIndex does not work for function based indexes anymore
- [CORE-1659] - "dbms" attribute is ignored for "sql" tag inside "rollback"
- [CORE-1666] - CommandLineResourceAccessor throwing exception from getResourceAsStream()
- [CORE-1669] - DB2Database the method getDefaultCatalogName returns the defaultSchemaName.
- [CORE-1677] - Default Sybase schema name
- [CORE-1680] - DbDoc fails with NullPointerException
- [CORE-1685] - NPE at SchemaComparator
- [CORE-1692] - generateChangeLog does not handle sets correct
- [CORE-1693] - SqlCheck precondition's 'expectedResult' accounts for the amount of columns instead of the amount of rows
- [CORE-1696] - Syntax error in command line UNIX shell script
- [CORE-1698] - Add FormattedSQL support for comma separated list dbms attribute
- [CORE-1428] - Support Springs classpath*: prefix for includeAll
- [CORE-1458] - Maven support for futureRollbackSql
- [CORE-1473] - JSON/YAML support for onFail/onError and onFailMessage/onErrorMessage on preconditions
- [CORE-1474] - Support 'dbms' attribute on <createProcedure>
- [CORE-1476] - Support reading unique constraints and sequences in DB2 for iSeries
- [CORE-1544] - liquibase-maven-plugin: diffChangeLog goal is missing
- [CORE-1638] - SnapshotGenerator extension system does not allow replacement of generators
- [CORE-1639] - Support foreignKeyConstraintExists precondition specifying table and columns instead of name
- [CORE-1656] - Extension to allow updateSql without a live database connection
- [CORE-1657] - Extension support for external DatabaseChangeLog tables
- [CORE-1663] - Support replaceIfExists for createView on H2
- [CORE-1671] - Add millisecond precision Time, Timestamp, and DateTime for MySQL
- [CORE-1681] - Log change and precondition execution into the ChangeExecListener
- [CORE-641] - Support ${} params in included sql files
- [CORE-883] - updateSQL without database access
- [CORE-929] - Support for ordering of columns in addColumn
- [CORE-1310] - Support column remarks for mysql
- [CORE-1513] - tableIsEmpty precondition
- [CORE-1514] - rowCount precondition
- [CORE-1558] - Modify Liquibase to allow extensions to specify prefix for xml tags
- [CORE-1617] - dropProcedure change
- [CORE-1618] - Support defining createProcedure body in an external file
- [CORE-1661] - Add support for MSSQL sequences

Changes in version 3.0.8 (2013.12.12)

- Major Changes:
    Improvements to data type handling
    Bug fixes
    Performance improvements

- [CORE-1224] - Enum column types are not appropriately represented in the change log created by generateChangeLog
- [CORE-1299] - modifyDataType does not auto reorg on DB2
- [CORE-1302] - MySQL syntax for autoincrement column with start value
- [CORE-1357] - Postgresql sequences create error in log files
- [CORE-1368] - Unable to recreate DB from generated change logs
- [CORE-1427] - Not generateChangeLog properly for INT2 type for Postgres
- [CORE-1430] - When I was in sybase database using liquibase create the index, reported "information_schema.constraints" not found error
- [CORE-1432] - Liquibase H2 InsertOrUpdateGeneratorH2 fails if primary key column name contains $ character
- [CORE-1446] - Liquibase changelog generation can add linefeed to defaultValueComputed if last column
- [CORE-1454] - Precondition negation does not seem to work
- [CORE-1455] - Value not taken into account when inserting data with YAML
- [CORE-1484] - very poor performance of dropAll on Oracle
- [CORE-1496] - Custom Change validation happening before being fully initialized
- [CORE-1531] - includeAll with relativeToChangeLogFile fails under certain conditions
- [CORE-1537] - Liquibase: diffChangeLog on DB2 performing out of order steps
- [CORE-1538] - diffChangeLog for DB2 injects invalid characters in xml
- [CORE-1540] - Call 'Reorg table' after modifyDataType on DB2
- [CORE-1541] - Column type="LONGVARCHAR" fails
- [CORE-1545] - Oracle wrong LONG datatype declaration
- [CORE-1547] - CLONE - H2 (and other) support for BLOB and CLOB is incorrect
- [CORE-1548] - Oracle Data Type: DATE - defaultValueComputed should be defaultValue
- [CORE-1549] - Oracle Data Type: INTERVAL YEAR TO MONTH - returned at "INTERNAL(2) YEAR"
- [CORE-1550] - Oracle Data Type: NCLOB - Receive SQL error when specifying NCLOB size
- [CORE-1551] - Oracle Data Type: NCHAR
- [CORE-1552] - Oracle Data Type: NVARCHAR2 - Column sizing is wrong
- [CORE-1553] - dropDefaultValue does not work on SQL Server
- [CORE-1554] - dbDoc for Informix IDS
- [CORE-1555] - Oracle UID reserved word not detected
- [CORE-1557] - Mysql: BLOB type changes to LONGBLOB
- [CORE-1559] - Columns defined as "serial" are created as "int" without autoincrement in Postgres
- [CORE-1562] - update throws Recoverable Exception
- [CORE-1564] - Broken links in dbDoc
- [CORE-1566] - DB2 Datatype - DBCLOB, GRAPHIC, VARGRAPHIC doubling datatype size
- [CORE-1567] - DB2 Datatype - defaultValueComputed being used instead of defaultValue for DBCLOB, TIME, TIMESTAMP, DATE
- [CORE-1568] - DB2 Datatype - REAL and XML should not have datatype sizes
- [CORE-1571] - updateDatabase can't find a changelog
- [CORE-1572] - Regression: diff generates full schema instead of changes when using liquibase-hibernate4
- [CORE-1580] - Mysql SET type not handled correctly
- [CORE-1581] - modifyDataType ignores additional info of newDataType
- [CORE-1582] - SQLServer datatype for TIMESTAMP
- [CORE-1583] - Wrong datatype with renameColumn
- [CORE-1584] - Unable do rollback not in transaction block
- [CORE-1586] - MySQL DataType - defaultValueComputed being injected into TIMESTAMP, VARBINARY, BINARY, YEAR
- [CORE-1587] - MySQL Datatype - VARBINARY vs LONGBLOB confusion
- [CORE-1588] - MySQL DataType - BIT size and default not captured on Snapshot
- [CORE-1589] - MySQL DataType - BLOB becomes LONGBLOB
- [CORE-1590] - MySQL DataType - DOUBLE sizing not persisted on snapshot
- [CORE-1591] - MySQL DataType - TIME not persisting seconds (getting hours and minutes..so I've got that going for me.)
- [CORE-1592] - MySQL DataType - TEXT converted to LONGTEXT
- [CORE-1594] - PostgreSQL 'bigserial' type is automatically converted to 'bigint'
- [CORE-1595] - Schema version incompatibility is logged as INFO instead of WARN
- [CORE-1596] - Liquibase 3.0.5 will not create tables in dbo schema in SQL Server
- [CORE-1597] - ORA-01000: maximum open cursors exceeded
- [CORE-1600] - "Collation" and "Lateral" are not escaped in Postgres (new reserved words in v9.3)
- [CORE-1602] - DatabaseException in changesets that include date fields with default values
- [CORE-1603] - MySQL Datatype - BIGINT, INT, MEDIUMINT, SMALLINT, TINYINT all "shaving" off sizes
- [CORE-1606] - MySQL - TEXT vs. LONGTEXT for diffChangeLog presents CLOB
- [CORE-1607] - MSSQL java.sql.Types.TIMESTAMP should map to DateTime
- [CORE-1610] - MSSQL - NTEXT type being snapshot with MSSQL driver default size parameter.
- [CORE-1611] - Avoid reverse DNS lookup with getLocalHost().getHostName();
- [CORE-1612] - YAML/JSON changelogs not picking up "value" attribute on column configs
- [CORE-1614] - mysql case insensitive affects databasechangelog creation
- [CORE-1615] - Failed to drop default value in MSSQL
- [CORE-1616] - SQLFileChange uses too many file descriptors
- [CORE-1619] - MSSQL: preconditions treat object names in a case-sensitive way
- [CORE-1622] - Ant not respecting diffTypes=data
- [CORE-1623] - MSSQL: precondition primaryKeyExists doesn't work
- [CORE-1625] - H2 Blob Type support is incorrect
- [CORE-1626] - createIndex does not work for function based indexes anymore
- [CORE-1627] - Option 'diffTypes' does not accept type 'indexes'
- [CORE-1632] - It's impossible to setup Logger's log level with system property 'liquibase.defaultlogger.level'
- [CORE-1636] - Case sensitivity issue with tableExists precondition
- [CORE-1640] - foreignKeyExists precondition always failing
- [CORE-1641] - Add column with foreign key throws NPE
- [CORE-1644] - ValidationFailedException after update to 3.0.7
- [CORE-1646] - Exception when comparing two schemas
- [CORE-1647] - Mysql enum default values not quoted
- [CORE-1648] - Mysql columns with colons and other special chars are not quoted
- [CORE-1664] - MSSQL. "DATE" type doesn't exist for MSSQL 2005 and 2000.
- [CORE-1382] - Allow to create DATABASECHANGELOG* tables in another schema
- [CORE-1546] - Schema-Support on Informix
- [CORE-1577] - Oracle tinyint, smallint, int being created as number(38,0)
- [CORE-1604] - Minor maven pom cleanups
- [CORE-1635] - Generated changelog missing unique constraints should be output before foreign keys

Changes in version 3.0.7 (2013.10.24)

- Major Changes:
    Decreased memory usage, especially with large sql files
    Fixed SQLite support
    Improvements to data type handling, especially in MS SqlServer

- [CORE-1509] - Memory improvements for large sqlFile files
- [CORE-1533] - Performance improvements in dropAll
- [CORE-1247] - [Sqlite] bad syntax in create table statements with single autoincrement primary key
- [CORE-1519] - Allow relative file paths for valueClobFile and valueBlobFile inserts/updates.
- [CORE-1275] - LoadData insert statements do not escape column names containing an open paren
- [CORE-1312] - Error when creating changelog tables
- [CORE-1399] - IncludeAll Failing when running in WAR files
- [CORE-1440] - Invalid Reorg table statement gets generated on DB2
- [CORE-1504] - dbDoc should html-encode author names
- [CORE-1506] - updateSQL generates invalid SQL for oracle
- [CORE-1507] - dropAll fails with NullPointerException
- [CORE-1510] - Maven plugin fails to configure parameters within property file with trailing spaces, error message misleading
- [CORE-1511] - modifyDataType truncates enum values
- [CORE-1512] - Insert with valueClobFile fails with unicode encoding.
- [CORE-1515] - Liquibase MSSQL: Snapshot incorrectly injecting size for some datatypes
- [CORE-1516] - Liquibase MSSQL: Snapshot recording incorrect size for VARBINARY
- [CORE-1517] - Liquibase MSSQL: Autoincrement property of decimal datatype not supported
- [CORE-1518] - Liquibase MSSQL: Incorrect datatypes captured in the snapshot
- [CORE-1520] - Liquibase Snapshot: Default datetime is wrong
- [CORE-1521] - When generateChangeLog is run, objects do not always come back in a consistent order
- [CORE-1522] - PreparedStatement.setCharacterStream() not supported by Postgres JDBC driver.
- [CORE-1524] - Ant diff command running diffChangeLog
- [CORE-1525] - Liquibase MSSQL: Snapshot not capturing alter statements that add default values
- [CORE-1526] - Unicode string escaping in MSSQL
- [CORE-1530] - Column called VERSION is quoted in PostgreSQL
- [CORE-1531] - includeAll with relativeToChangeLogFile fails under certain conditions
- [CORE-1532] - NPE in ChangedIndexChangeGenerator
- [CORE-1534] - Single quotes in liquibase.bat causing issues

Changes in version 3.0.6 (2013.10.08)

- Major Changes:
     Split out CDI code to a separate jar
	 Remove primary key on DATABASECHANGELOG and increased column sizes
	 Case sensitivity fixes
	 General bug fixes

- [CORE-1485] - Extract CDI support into a separate module
- [CORE-602] - Increase the default size of the FILENAME column
- [CORE-1471] - Add support for Sybase IQ
- [CORE-1475] - Support entering password(s) interactively on the command line
- [CORE-1477] - Support overriding LockService changeLogWaitTime
- [CORE-1492] - DatabaseChangelog Description useless
- [CORE-1502] - CLONE - UpdateSQL needs to append a "/" to the end of createProcedure for Oracle
- [CORE-1088] - Derby fails to create databasechangelog tables
- [CORE-1093] - generateChangeLog fails on MSSQL with coalation CS on views named in lowerCase
- [CORE-1158] - Escaping of reserved keywords in HSQLDB
- [CORE-1170] - loadUpdateData sometimes needs / and sometimes not
- [CORE-1177] - Failure to write a change set to the DATABASECHANGELOG table still applies the change set
- [CORE-1325] - CDI injection does not work with openwebbeans
- [CORE-1343] - Include SQL file with relativeToChangelogFile doesn't work
- [CORE-1378] - Deploy fails on Glassfish
- [CORE-1409] - Package CDI support as a separate module
- [CORE-1436] - loadUpdateData does not escape apostrophes
- [CORE-1437] - Diff is case-sensitive on column names
- [CORE-1440] - Reorg Table gets called before table is created on DB2
- [CORE-1441] - Drop table with cascade does not work on MS SQL Server
- [CORE-1443] - Documentation for liquibase maven logging configuration needs update
- [CORE-1445] - Mysql Reserved Words Not quoted
- [CORE-1449] - Liquibase throws null pointer if included file does not exist
- [CORE-1464] - SQL changelog are not correctly handled (NPE)
- [CORE-1465] - GetViewDefinitionGeneratorMSSQL generates UPPER case view name
- [CORE-1469] - GenerateChangelog not working on Firebird
- [CORE-1470] - Use NUMERIC instead of NUMBER for Sybase ASE
- [CORE-1478] - MySQL keywords `key' not quoted as a column name
- [CORE-1479] - MySQL 5.5.27 fails to create DATABASECHANGELOG table (Liquibase support fully broken)
- [CORE-1480] - ChangedPrimaryKeyChangeGenerator missing name
- [CORE-1481] - Schema.toString NPE
- [CORE-1482] - Update fails with "Table DATABASECHANGELOGLOCK already exists"
- [CORE-1483] - Boolean values of "false" being incorrectly handled
- [CORE-1488] - Encoding issue with UpdateSQL
- [CORE-1490] - Oracle 10g: Changelog generation changes type NUMBER(*,0) to NUMBER(22)
- [CORE-1491] - Oracle 10g: changelog generation converts VARCHAR2(n CHAR) to VARCHAR2(n BYTE)
- [CORE-1497] - SQL SERVER: sysdiagram table is captured during generateChangeLog if system diagrams are enabled.
- [CORE-1499] - null appearing in liquibase maven output
- [CORE-1500] - Snapshots do not order objects alphabetically
- [CORE-1501] - Incorrect SQL generated for default column value in PostgreSQL for text columns
- [CORE-1448] - Throw a more helpful error message and continue on if snakeyaml isn't in the classpath
- [CORE-1472] - Support multi-tenant spring applications

Changes in version 3.0.5 (2013.09.24)
- Major Focus: Performance Improvements
- [CORE-1453] - Multi-column foreign keys not snapshotted correctly
- [CORE-845] - "DATE" data type is deployed as "SMALLDATETIME" in SQL 2008
- [CORE-859] - Nullable TIMESTAMP columns in MySQL are not nullable.
- [CORE-1257] - Oracle: generateChangeLog exports a column of type "NUMBER" as "NUMBER(0,-127)"
- [CORE-1288] - H2 (and other) support for BLOB and CLOB is incorrect
- [CORE-1338] - Using apostrophe in changeset's ID causes SQL error
- [CORE-1360] - Confirmation message of a custom change change is only printed if loglevel is debug
- [CORE-1364] - SQL Server incorrect INFORMATION_SCHEMA table case in native query
- [CORE-1365] - SQL Server: "Database 'dbo' does not exist" error
- [CORE-1401] - Unnecessary snapshot on liquibase update
- [CORE-1415] - Liquibase tables are not detected in oracle running in the SYSTEM schema
- [CORE-1426] - ORACLE: Check for existing tables is case sensitive
- [CORE-1429] - Random order of UniqueConstraint and ForeignKey drop statements
- [CORE-1459] - Maven updateSQL creates databasechangelog table, should only output SQL to do it
- [CORE-1462] - renameColumn with 'text' data type improperly trying to use 'clob' against MySql
- [CORE-1433] - Handle changes in column datatypes in diffChangeLog
- [CORE-1434] - Formatted Changelog format not parsing UTF8 .sql files with BOM

Changes in version 3.0.4 (2013.09.06)
- [CORE-548] - GenerateChangeLog generates invalid XML/SQL for mysql tables with autoincrement and compound PKs
- [CORE-1423] - NPE in ForeignKeyComparator

Changes in version 3.0.3 (2013.08.29)
- [CORE-1241] - Remove schema name from SQL generated when running updateSQL
- [CORE-1412] - UpdateSQL should write to stdout not stderr
- [CORE-844] - Integer with range limitations handled incorrectly on Oracle
- [CORE-1313] - Unique constraint name is ignored when adding a column
- [CORE-1316] - Column Type COMPUTED in loadData is not implemented correctly (shoud pass a DB Function instead of String to DB).
- [CORE-1327] - renameColumn and renameTable with Sybase
- [CORE-1329] - GenerateChangeLog on MSSQL exports a SMALLINT(5) - smallint does not allow precision
- [CORE-1336] - AutoIncrement not working with some types
- [CORE-1351] - PostgreSql 9 doesnt have datetime data type
- [CORE-1353] - AutoIncrement not working for numeric types
- [CORE-1354] - Sequences get dropped too early
- [CORE-1356] - Unnecessary size specifications on MEDIUMTEXT, TINYTEXT, MEDIUMBLOB, TINYBLOB from generateChangeLog
- [CORE-1358] - LiquiBase 3.0.2 writes '3.0.0-SNP' (Snapshot) to 'LIQUIBASE' column instead of '3.0.2'
- [CORE-1362] - Informix fails when schema is not equal catalog
- [CORE-1363] - ForeignKey NPE during dbDoc generation
- [CORE-1364] - SQL Server incorrect INFORMATION_SCHEMA table case in native query
- [CORE-1366] - Oracle database dbDoc generation performance issues
- [CORE-1367] - updateSQL generates an empty file using standard output in 3 version
- [CORE-1369] - Whitespace in author in SQL formatted changelog causes that migration to be silently skipped
- [CORE-1370] - Oracle doesnt have DATETIME datatype
- [CORE-1371] - MySQL syntax error is thrown if schema name contains hyphens
- [CORE-1372] - Version Command Line Argument Incorrect
- [CORE-1373] - dropAll attempts to drop objects in other schemas under Oracle
- [CORE-1375] - "group" is not included as a reserved word
- [CORE-1377] - liquibase version says 3.0.0-SNP
- [CORE-1380] - ^M present in liquibase unix running file (from jar on maven repo)
- - - - - [CORE-1381] - DropAll: Not working with MsSql
- - [CORE-1383] - Error attempting to re-create databasechangeloglock on second liquibase run if running in Oracle under SYSTEM user
- [CORE-1387] - dropAll fails with oracle since version 3.0.2
- [CORE-1388] - MSSQLServer: 'REAL' dataype is returned with unnecessary size modifier during generateChangeLog
- [CORE-1389] - AddLookupTable fails when using QUOTE_ALL_OBJECTS
- [CORE-1394] - additionalInformation lost in DataTypeFactory.fromDescription()
- [CORE-1395] - Bug in MSSQLDatabase.getViewDefinition()
- [CORE-1396] - Liquibase is loosing indices on foreign key columns
- [CORE-1397] - Informix DB: Size of VARCHAR more than 255
- [CORE-1398] - Missing or unexpected unique constraint not found on Postgres
- [CORE-1402] - Custom preconditions not passing XML validation
- [CORE-1403] - Default schema not correct when using <tableExists>
- [CORE-1405] - SpringLiquibase not closing connection if rollback exception
- [CORE-1406] - MySQL loadUpdateData using literal values for update
- [CORE-1408] - NVarcharType on Derby Fails
- [CORE-1413] - NPE in changeSetExecuted precondition in changelog-level preconditions
- [CORE-1414] - Improve error handling in empty databasechangeloglock tables
- [CORE-1417] - generateChangeLog failed
- [CORE-1418] - Foreign Key changes not detected in snapshot process

Changes in version 3.0.2 (2013.07.11)
- [CORE-628] - Specify the output encoding that liquibase should use to output data in Maven
- [CORE-169] - Oracle TIMESTAMPS not exported correctly
- [CORE-1301] - defaultschemaName not used
- [CORE-1315] - Database Objects do not get dropped on DB2
- [CORE-1317] - Not all FK Constraints get dropped on Oracle
- [CORE-1318] - Not all FK Constraints get dropped on SQL Server
- [CORE-1328] - includeAll trying to process invalid file types, should only process valid change log types
- [CORE-1331] - If logicalFilePath attribute is set on databaseChangeLog, I am unable to use relativeToChangelogFile="true" on
- [CORE-1332] - includeAll of changelog files throws duplicate identifiers error
- [CORE-1335] - Data export support broken in 3.0
- [CORE-1336] - AutoIncrement not working with some types
- [CORE-1337] - Problem with changeset defined as runAlways="true"
- [CORE-1339] - NPE on update if default ServiceLocator.packagesToScan is used
- [CORE-1340] - indexExists Changesets throwing PreconditionErrorException instead of PreconditionFailedException
- [CORE-1341] - foreignKeyConstraintExists precondition broken
- [CORE-1347] - dropAll function work incorrectly when table has 2 foreign keys

Changes in version 3.0.1 (2013.06.25)
- [CORE-898] - Custom precondition xsd failing
- [CORE-1171] - Maven plugin displaying password in plain text
- [CORE-1320] - Cannot include YAML file
- [CORE-1323] - Conversion from char to SMALLINT is unsupported
- [CORE-1324] - Formatted SQL does not support contexts such as "some-context" or "some/context"

Changes in version 3.0.0 (2013.06.17)
- Dropped Java 1.5 support
- Major changes in diff and snapshot log
- Support for "catalogs" in addition to schemas
- Separated "diff" and "snapshot" logic for better modularity
- Extension support for LockService
- Extension support for defining new DataTypes
- Extension support for defining new snapshot DatabaseObjects
- Extension support for defining new Snapshot log
- Extension support for how to compare DatabaseObjects
- Better extension support for changelog generation
- More object types are snapshotted
- Changes can provide metadata helpful for IDE and other integrations
- Added ability for objects to control how they are serialized via the LiquibaseSerializable interface
- Can define tablespace for liquibase tables
- Performance improvements
- Ability to specify sequences to read values from on <insert>
- Improve OSGi support
- Support preconditions in formatted SQL
- Maven supports generateChangeLog
- Better support for case sensitive databases
- API cleanup
- Ability to SKIP columns in a CSV file
- Improved Informix support
- Support JDBC escape syntax in SQL
- Added futureRollbackCountSQL
- Support ${} params in formatted SQL
- Many bug fixes

Change in version 2.0.5 (2012.05.01) https://liquibase.jira.com/secure/ReleaseNote.jspa?projectId=10020&version=10360
----------------------
- Reverted load blog/clob data from files feature
- Fixed linux distribution script
- Fixed command line output back to stdout

Change in version 2.0.4 (2012.04.18) https://liquibase.jira.com/secure/ReleaseNote.jspa?projectId=10020&version=10160
----------------------
- Support ${} params in included sql files
- Ability to load blog/clob data from files
- Bug Fixes


Change in version 2.0.2 (2011.09.15)
----------------------
- dropFirst parameter on SpringLiquibase bean
- Bug Fixes

Change in version 2.0.0 (2010.12.19)
----------------------
- Major change to codebase, focusing on extension/integration APIs

Changes in version 1.9.3 (2009.4.3)
----------------------
- Fixed index column nested tags in generated XML

Changes in version 1.9.2 (2009.4.6)
----------------------
- Fix for includeAll on windows
- Other bug fixes

Changes in version 1.9.1 (2009.2.20)
----------------------
- Escape hsql tables/columns if they are a keyword
- opensvn is included in our jar, not a dependency
- Bug in runInTransaction
- Additional bug fixes
- Grails 1.1-SNAPSHOT support

Changes in version 1.9.0 (2009.1.7)
----------------------
- Paths in <include> can be relative to changelog
- Sybase Adaptive SQL Anywhere support
- Better Derby support (drop column, rename column)
- <modifySql> support
- <stop> change
- <includeAll> support
- runInTransaction attribute for changeSet
- Stronger validation in .xsd
- Bug fixes

Changes in version 1.8.1 (2008.10.1)
----------------------
- Bug fixes

Changes in version 1.8.0 (2008.09.17)
----------------------
- Added SQLite support
- Bug fixes
- Added dropAllForeignKeyConstraints change
- Added onFail and onError precondition failure controls
- Auto-increment specifying on a non-auto-increment table does not fail but simply ignores flag
- Can control onDelete and onUpdate behavior for FK constraints
- Added ability to specify that an index should be unique
- Additional validation step checks for required attributes at run time
- Improved ordering of generated scripts with diff
- Improved command line migrator return codes
- Context checking is case insensitive
- Can specify end delimiter in <sql> change
- Added several new precondition checks
- Custom preconditions can be passed parameters

Changes in version 1.7.0 (2008.06.25)
----------------------
- Added loadData change
- Added support for changelog paramters
- Allow preconditions in <changeSet>
- Can export data with generateChangeLog
- Added markNextChangeSetRan command
- Added validCheckSum tag to changeSet
- Can use change tags in <rollback>
- Can reference previous changeSets in <rollback>
- Allow custom preconditions
- RowMapper interface is now public
- Misc bug fixes

Changes in version 1.6.1 (2008.04.23)
----------------------
- Maven/Grails/IntlliJ Plugin relase of 1.6.x
- Many misc bug fixes

Changes in version 1.6.0 (2008.03.27)
----------------------
- Added Hibernate diff support
- Fix to MD5Sum generation that requires updating all rows in databasechangelog table
- Added tagDatabase tag
- Added primaryKeyName attribute
- Supports multiple rollback tags per changeSet
- Column remarks stored in generated changelogs
- executeCommand output logged to logging system
- Many misc bug fixes

Changes in version 1.5.3 (2008.02.28)
----------------------
- Recompiled to work with Java 1.5

Changes in version 1.5.2 (2008.02.07)
----------------------
- Fixed bug with runOnChange and MD5sum check
- Handles generating changelog for tables with composite primary keys
- Other minor bug fixes

Changes in version 1.5.1 (2008.02.01)
----------------------
- Fixed failOnError logic
- Improved serial column detection in Postgres
- Rollback connections before close for DB2
- Other minor bug fixes

Changes in version 1.5.0 (2008.01.29)
----------------------
- Major Refactoring of XML parser and Migrator/Liquibase class
- Database table comments saved to generated change log
- ReplaceIfExists attribute added to createView
- Setting value/valueNumeric/valueBoolean/valueDate on addColumn will update all existing rows with the given value
- Added defaultSchemaName parameter for setting default schema
- Changelog file comparisons are case-insensitive on windows
- Output warning of old schema version
- Added updateCount and updateCountSQL commands
- Changed "migrate" command to "update".  Note: "migrate" is now an alias for "update"
- Added comments tag to generated SQL output
- Custom Database implementations can be specified with the databaseClassName parameter
- Created changeLogSync command
- Rollback commands can specify contexts
- createTable can specify unique constraint name
- XSDs are not pulled from network
- Handles Postgres datatypes better
- added <update> change
- added <delete> change
- Additional and enhanced Ant tasks
- Changed database.migrator.should.run system property to liquibase.should.run
- Moved liquibase.servlet.MigratorStatusServlet to liquibase.servlet.LiquibaseStatusServlet
- Moved liquibase.servlet.ServletMigrator to liquibase.servlet.LiquibaseServletListener
- Moved liquibase.spring.SpringMigrator to liquibase.spring.SpringLiquibase
- Moved liquibase.commandline.CommandLineMigrator to liquibase.commandline.Main
- Renamed checked servlet listener parameters
- Other bug fixes

Changes in version 1.4.1 (2007.11.30)
----------------------
- Bug fixes

Changes in version 1.4.0 (2007.11.19)
----------------------
- IntelliJ Plug-in Support
- Added support for specifying schemas in change log
- MaxDB/SAPDB Support
- Refactored Code
- Can specify data types as java.sql.Types.*
- Support for composite foreign keys
- Improved Maven support
- Bug Fixes

Changes in version 1.3.2 (2007.10.8)
----------------------
- Grails support
- Firebird support

Changes in version 1.3.1 (2007.10.2)
----------------------
- Bug fix for createStoredProcedure in Oracle

Changes in version 1.3.0 (2007.9.27)
----------------------
- Added tablespace support to createTable, createIndex, addPrimaryKey, and addUniqueConstraint
- Restored Java 6 Support
- Added defaultCascade attribute to addForeignKeyConstraint
- Can have multiple comma-separated contexts per change set
- Created <custom> change
- Added sqlCheck precondition
- Database Diff checks column types and nullability
- DiffChangeLog will write to a file specified with the --changeLog flag
- Added dbdoc documentation generator
- Database Diff checks views
- Package re-organization
- Added createProcedure change
- Added <executeCommand> change
- Table names are enclosed in []'s on MS SQL
- Added Spring migrator
- Misc bug fixes

Changes in version 1.2.1 (2007.8.31)
----------------------
- Include sequences in generateChangeLog
- Fixed ordering of statements default and null clauses based on database

Changes in version 1.2 (2007.8.28)
----------------------
- Added support for H2 database
- Added support for InterSystems Cache` database
- Support for sqlfile change
- Improved error messages
- Sybase fixes
- Handle --defaultsFile correctly
- Handle command line parameters correctly on Windows systems
- <sql> tag can contain a <comment> tag
- Change log file references can be absolute
- SQL can be ; or 'go' delimited
- Added clearCheckSums command
- Bug fixes

Changes in version 1.1.1 (2007.7.24)
-----------------------------------
- Bug fixes related to the diff tool

Changes in version 1.1 (2007.7.23)
-----------------------------------
- Support for additional databases: DB2, Derby, Sybase (not fully tested), and HSQL
- Support for "unsupported" databases
- Database Diff Tool
- Database Creation Script Generator
- new DBMS attribute on change set tag that allows you to specify what databases to run the change set against
- "verify" command that checks for change log problems without attempting to execute any
- "status" command that shows information on unrun change sets
- Handle date/time and numeric values better on inserts and setting default values
- Bug Fixes

Changes in version 1.0.1 (2007.7.07)
------------------------------------
- Do not quote "null" default values
- Documentation fixes

Changes in version 1.0 (2007.6.25)
------------------------------------
- Minor Bug Fixes

Changes in version 1.0 RC2 (2007.6.19)
------------------------------------
- Improved documentation
- Bug fixes
- Changes to exception throwing

Changes in version 1.0 RC1 (2007.6.8)
------------------------------------
- Added batch/shell scripts for command line migrator
- Improved documentation
- Bug fixes
- Blog started at http://blog.liquibase.org

Changes in version 0.9.1 (2007.5.23)
------------------------------------
- Bug fixes

Changes in version 0.9.0 (2007.5.14)
------------------------------------
- Rollback support
- Initial Maven support
- Performance improvements
- Can have a logical file and path for changelogs
- New Database Refactorings:
  - Add View
  - Rename View
  - Drop View
  - Merge Columns
  - Add Foreign-Key Constraint
  - Drop Foreign-Key Constraint
  - Add Lookup Table
  - Add Primary Key
  - Add Auto-Increment
  - Add Default Value
  - Drop Default Value
  - Drop Primary Key
  - Add Unique Constraint
  - Drop Unique Constraint
- Code refactorings

Changes in version 0.8.2 (2007.4.30)
-------------------------------------
- Bug fixes

Changes in version 0.8.1 (2007.4.25)
-------------------------------------
- Prompting for non-local database is now false by default
- Ant non-local database prompt attribute changed to promptOnNonLocalDatabase from promptOnNonDevDatabase
- Bug Fixes


Changes in version 0.8.0 (2007.4.22)
-------------------------------------
- Changelog Pre-conditions
- Execution contexts
- failOnError tag for changeSets
- addNotNullConstraint / dropNotNullConstraint
- allow "comment" tags
- Improved documentation
- Completed conversion to SourceForge
- Improved automated tests


Changes in version 0.7.0 (2007.3.16)
-------------------------------------
- Changed MD5Sum logic.  !! Need to run 'update databasechangelog set md5sum=null' !!
- Added support for autoIncrement columns in createTableChange
- Switched to Liquibase name, including all package structures
- Moved code to SourceForge


Changes in version 0.6.3 (2007.1.10)
--------------------------------------
- Fixed bug in create table refactoring where it ignored constraint clauses

Changes in version 0.6.2 (2006.12.19)
--------------------------------------
- Fixed missing non-upper cased insert statement

Changes in version 0.6.1 (2006.12.12)
--------------------------------------
- Classpath for command line migrator can now include .ear files
- Can specify a "database.migrator.should.run" system property that blocks the migrator from running if set to "false"
- Added a liquibase.migrator.servlet.MigratorStatusServlet servlet that can used to see the migrator logs
- Added a MIGRATOR_FAIL_ON_ERROR web.xml context parameter that controls if an exception should be raised if an exception is thrown, or if the listener should just return.  Throws an exception by default
- All database SQL is upper case.
- Fixed issues with postgres schemas and getTables() call
- Added retroweaver build for 1.4 JVMs
- Misc. Bug fixes

Changes in version 0.6 (2006.11.10)
--------------------------------------
- Added change log locking support, so multiple migrators running at the same time won't interfere with eachother
- Fixed bug with command line parsing and URLs with '='s in them
- Misc bug fixes

Changes in version 0.5.1 (2006.10.18)
--------------------------------------
- Bugfixes related to running ant task

Changes in version 0.5 (2006.10.16)
--------------------------------------
- LGPL Licence
- Oracle uses Timestamp for datetime columns
- Command line migrator can be run against WAR and EAR files
- Command line migrator can be run via java -jar
- ChangeSets can be marked as "runAlways" and "runOnChange"
- ServletListener can be configured to only run on certain hostnames
- Removed all dependencies
- Improved MD5Sum generation.  To update your MD5Sums, run "update databasechangelog set md5sum=null"
- MySQL Support
- Misc bugfixes
- Improved Documentation
