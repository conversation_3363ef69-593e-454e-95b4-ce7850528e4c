package com.dl.dops.biz.resourcecenter.mq.producer;

import cn.hutool.json.JSONUtil;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.resourcecenter.dal.category.po.CategoryPO;
import com.dl.dops.biz.resourcecenter.mq.MqChannels;
import com.dl.dops.biz.resourcecenter.mq.dto.DeleteCategoryMsgDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-05 17:11
 */
@Component
public class DeleteCategoryProducer {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeleteCategoryProducer.class);

    @Autowired
    private MqChannels mqChannels;

    public void sendMQ(CategoryPO categoryPO, List<Long> subCategoryBizIdList, Date deleteTime, Long operatorUserId) {
        DeleteCategoryMsgDTO msgDTO = new DeleteCategoryMsgDTO();
        msgDTO.setCategoryBizId(categoryPO.getBizId());
        msgDTO.setCategoryType(categoryPO.getCategoryType());
        msgDTO.setCategoryLevel(categoryPO.getCategoryLevel());
        if (!Const.ZERO_LONG.equals(categoryPO.getParentId())) {
            msgDTO.setParentBizId(categoryPO.getParentId());
        }
        msgDTO.setDeleteTime(deleteTime);
        msgDTO.setOperatorUserId(operatorUserId);
        if (CollectionUtils.isNotEmpty(subCategoryBizIdList)) {
            msgDTO.setSubCategoryBizIdList(subCategoryBizIdList);
        }

        try {
            this.doSendMQ(msgDTO);
        } catch (Exception e) {
            LOGGER.error("发送删除分类的mq 发送异常，msgDTO:{},,,,e:{}", JSONUtil.toJsonStr(msgDTO), e);
        }
    }

    private void doSendMQ(DeleteCategoryMsgDTO msgDTO) {
        Message message = MessageBuilder.withPayload(msgDTO).build();
        boolean sendResult = mqChannels.deleterccategory().send(message, 2000L);
        LOGGER.info("发送删除分类的mq ,msgDTO:{},sendResult:{}", JSONUtil.toJsonStr(msgDTO), sendResult);
    }
}
