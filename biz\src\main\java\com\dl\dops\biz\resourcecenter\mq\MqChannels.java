package com.dl.dops.biz.resourcecenter.mq;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-07 14:04
 */
public interface MqChannels {

    /*****************消费者***************/
    @Input("deleterccategoryconsumer")
    SubscribableChannel deleterccategoryconsumer();

    @Input("tenantstatuschangeconsumer")
    SubscribableChannel tenantstatuschangeconsumer();

    /*****************生产者***************/

    @Output("deleterccategory")
    MessageChannel deleterccategory();
}
