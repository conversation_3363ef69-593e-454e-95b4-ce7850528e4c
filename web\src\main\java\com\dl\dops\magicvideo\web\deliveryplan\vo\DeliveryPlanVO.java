package com.dl.dops.magicvideo.web.deliveryplan.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @describe: DeliveryPlanVO
 * @author: zhousx
 * @date: 2023/9/5 13:50
 */
@Data
public class DeliveryPlanVO {
    @ApiModelProperty("计划id")
    private String planId;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("简介")
    private String desc;

    @ApiModelProperty("租户编号")
    private String tenantCode;

    @ApiModelProperty("租户名称")
    private String tenantName;

    @ApiModelProperty("关联模板id")
    private String templateId;

    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("模板封面")
    private String templateCoverUrl;

    @ApiModelProperty("负责人")
    private String director;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("触发次数")
    private Integer limit;

    @ApiModelProperty("周期 -1-无，0-天，1-周，2-")
    private Integer period;

    @ApiModelProperty("是否有周期限制 0-否 1-是")
    private Integer hasPeriod;

    @ApiModelProperty("创建时间")
    private Date createDt;

    @ApiModelProperty("状态，0-停止，1-运行中")
    private Integer status;

    @ApiModelProperty("打开消息通知 0-否 1-是")
    private Integer isNotify;

    @ApiModelProperty("消息通知url")
    private String notifyUrl;

    @ApiModelProperty("回调通知url")
    private String callbackUrl;

    @ApiModelProperty("生产方式 0-接口单次触发 1-批量触发 2-定时触发")
    private Integer produceWay;
}
