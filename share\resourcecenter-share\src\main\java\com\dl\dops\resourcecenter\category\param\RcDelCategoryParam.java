package com.dl.dops.resourcecenter.category.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RcDelCategoryParam implements Serializable {

    @NotEmpty(message = "请输入分类ID")
    @ApiModelProperty("分类唯一标识")
    String categoryId;
}
