CREATE TABLE `tag` (
                       `id` bigint NOT NULL AUTO_INCREMENT,
                       `tag_id` bigint NOT NULL COMMENT '标签id',
                       `name` varchar(50) NOT NULL COMMENT '标签名称',
                       `ordered` int NOT NULL COMMENT '排序',
                       `tag_group_id` bigint NOT NULL COMMENT '标签组id',
                       `create_dt` datetime NOT NULL COMMENT '创建时间',
                       `create_by` bigint NOT NULL COMMENT '创建人userid',
                       `modify_dt` datetime NOT NULL COMMENT '修改时间',
                       `modify_by` bigint NOT NULL COMMENT '修改人userid',
                       `is_deleted` tinyint(1) NOT NULL COMMENT '是否删除：0否 1是',
                       PRIMARY KEY (`id`),
                       UNIQUE KEY `uniq_tag_id` (`tag_id`) USING BTREE,
                       KEY `idx_tag_group_id` (`tag_group_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='标签表';

CREATE TABLE `tag_group` (
                             `id` bigint NOT NULL AUTO_INCREMENT,
                             `tag_group_id` bigint NOT NULL COMMENT '标签组id',
                             `name` varchar(50) NOT NULL COMMENT '标签组名称',
                             `ordered` int NOT NULL COMMENT '排序',
                             `create_dt` datetime NOT NULL COMMENT '创建时间',
                             `create_by` bigint NOT NULL COMMENT '创建人userId',
                             `modify_dt` datetime NOT NULL COMMENT '修改时间',
                             `modify_by` bigint NOT NULL COMMENT '修改人userId',
                             `delete_dt` timestamp NULL DEFAULT NULL COMMENT '删除时间',
                             `is_deleted` tinyint(1) NOT NULL COMMENT '是否删除：0否 1是',
                             `type` int NOT NULL COMMENT '标签组类型',
                             PRIMARY KEY (`id`),
                             UNIQUE KEY `uniq_tag_group_id` (`tag_group_id`) USING BTREE,
                             UNIQUE KEY `uniq_tag_group_id_delete_dt` (`tag_group_id`,`delete_dt`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='标签组表';

CREATE TABLE `tag_rela` (
                            `id` bigint NOT NULL AUTO_INCREMENT,
                            `biz_id` bigint NOT NULL COMMENT '业务主键id',
                            `tag_id` bigint NOT NULL COMMENT '标签id',
                            `type` int NOT NULL COMMENT '业务类型',
                            `create_dt` datetime NOT NULL COMMENT '创建时间',
                            `create_by` bigint NOT NULL COMMENT '创建人userID',
                            `modify_dt` datetime NOT NULL COMMENT '修改时间',
                            `modify_by` bigint NOT NULL COMMENT '修改人userID',
                            `is_deleted` tinyint(1) NOT NULL COMMENT '是否删除：0否 1是',
                            PRIMARY KEY (`id`),
                            KEY `idx_tag_id` (`tag_id`) USING BTREE,
                            KEY `idx_biz_id` (`biz_id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='素材标签关联表';