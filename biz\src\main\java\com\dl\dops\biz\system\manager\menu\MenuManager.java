package com.dl.dops.biz.system.manager.menu;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.system.dal.menu.po.MenuPO;
import com.dl.dops.biz.system.manager.menu.dto.MenuDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 09:14
 */
public interface MenuManager extends IService<MenuPO>, CommonService {

    List<MenuDTO> listAllMenus();

    /**
     * 获取所有系统菜单列表,包含菜单对应的Functions列表
     *
     * @return
     */
    List<MenuDTO> listMenusAndFunctions();

}
