package com.dl.dops.magicvideo.web.music.convert;

import com.dl.dops.magicvideo.music.dto.BackGroundMusicInternalDTO;
import com.dl.dops.magicvideo.web.music.vo.BackgroundMusicVO;

import java.util.Objects;

public class BackgroundMusicConvert {

    public static BackgroundMusicVO cnvBackGroundMusicInternalDTO2VO(BackGroundMusicInternalDTO input){
        if (Objects.isNull(input)){
            return null;
        }
        BackgroundMusicVO result = new BackgroundMusicVO();
        result.setBizId(input.getBizId().toString());
        result.setName(input.getName());
        result.setType(input.getType());
        result.setDuration(input.getDuration());
        result.setUrl(input.getUrl());
        result.setCreateDt(input.getCreateDt());
        result.setUserName(input.getUserName());
        result.setUserId(input.getUserId()+"");
        return result;
    }
}
