package com.dl.dops.system.web.virtualvoice;

import com.dl.aiservice.share.digitalasset.DaVirtualVoiceAuthorizeDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceChannelDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDetailDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoicePageDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceUpdateDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.forest.aiservice.AiServiceManager;
import com.dl.dops.biz.common.forest.basicservice.dto.TenantInfoDTO;
import com.dl.dops.biz.common.service.tenant.TenantInfoService;
import com.dl.dops.biz.common.tencentcloud.cos.CosFileUploadManager;
import com.dl.dops.biz.common.util.DownloadUtil;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.system.web.virtualman.helper.VirtualVoiceHelper;
import com.dl.dops.system.web.virtualman.param.TtsParam;
import com.dl.dops.system.web.virtualman.vo.TtsVO;
import com.dl.dops.system.web.virtualvoice.param.DaVirtualVoiceAuthorizeParam;
import com.dl.dops.system.web.virtualvoice.param.DaVirtualVoiceDetailParam;
import com.dl.dops.system.web.virtualvoice.param.DaVirtualVoicePageParam;
import com.dl.dops.system.web.virtualvoice.param.DaVirtualVoiceParam;
import com.dl.dops.system.web.virtualvoice.param.DaVirtualVoiceUpdateParam;
import com.dl.dops.system.web.virtualvoice.vo.DaVirtualVoiceChannelVO;
import com.dl.dops.system.web.virtualvoice.vo.DaVirtualVoiceVO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@Api(tags = "智能语音管理")
@RequestMapping("/dops/voice")
public class VirtualVoiceController extends AbstractController {
    private static final Logger LOGGER = LoggerFactory.getLogger(VirtualVoiceController.class);

    @Resource
    private AiServiceManager aiServiceManager;
    @Resource
    private TenantInfoService tenantInfoService;
    @Resource
    private CosFileUploadManager cosFileUploadManager;

    @Value("${dl.fileTempPath}")
    public String localPathPrefix;

    private final static String AUDITION_TTS_COS_PATH = "/temp/visual/tts/audition";

    private static final String DEFAULT = "ALL-TENANT";

    @PostMapping("/page")
    @ApiOperation("智能语音列表")
    public ResultPageModel<DaVirtualVoiceVO> voicePage(@RequestBody DaVirtualVoicePageParam param) {

        DaVirtualVoicePageDTO request = new DaVirtualVoicePageDTO();
        BeanUtils.copyProperties(param, request);
        ResultPageModel<DaVirtualVoiceDTO> resultPageModel = aiServiceManager.voicePage(request);

        if (CollectionUtils.isEmpty(resultPageModel.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<TenantInfoDTO> tenantList = tenantInfoService.listAll();
        Map<String, TenantInfoDTO> tenantInfoMap = tenantList.stream()
                .collect(Collectors.toMap(TenantInfoDTO::getTenantCode, Function.identity()));

        return pageQueryModel(resultPageModel, resultPageModel.getDataResult().stream().map(i -> {
            DaVirtualVoiceVO daVirtualVoiceVO = VirtualVoiceHelper.cnvDaVirtualVoiceDTO2VO(i);
            if (CollectionUtils.isNotEmpty(daVirtualVoiceVO.getTenantCodeList())) {
                List<String> tenantCode = daVirtualVoiceVO.getTenantCodeList().stream()
                        .map(code -> Objects.nonNull(tenantInfoMap.get(code)) ? tenantInfoMap.get(code).getName() :
                                 "租户【" + code + "】已删除")
                        .collect(Collectors.toList());
                daVirtualVoiceVO.setTenantCodeList(tenantCode);
            }
            return daVirtualVoiceVO;
        }).collect(Collectors.toList()));
    }

    @PostMapping("/pageSearch")
    @ApiOperation("智能语音列表搜索")
    public ResultPageModel<DaVirtualVoiceVO> pageSearch(@RequestBody DaVirtualVoicePageParam param) {

        DaVirtualVoicePageDTO request = new DaVirtualVoicePageDTO();
        BeanUtils.copyProperties(param, request);
        ResultPageModel<DaVirtualVoiceDTO> resultPageModel = aiServiceManager.pageSearch(request);

        if (CollectionUtils.isEmpty(resultPageModel.getDataResult())) {
            return new ResultPageModel<>();
        }

        return pageQueryModel(resultPageModel, resultPageModel.getDataResult().stream().map(i -> {
            DaVirtualVoiceVO daVirtualVoiceVO = VirtualVoiceHelper.cnvDaVirtualVoiceDTO2VO(i);
            return daVirtualVoiceVO;
        }).collect(Collectors.toList()));
    }

    @PostMapping("/voiceDetail")
    @ApiOperation("智能语音详情接口")
    public ResultModel<DaVirtualVoiceVO> voiceDetail(@RequestBody @Validated DaVirtualVoiceDetailParam param) {
        DaVirtualVoiceDetailDTO request = new DaVirtualVoiceDetailDTO();
        BeanUtils.copyProperties(param, request);

        ResultModel<DaVirtualVoiceDTO> resultModel = aiServiceManager.voiceDetail(request);
        if (!resultModel.getCode().equals("0")) {
            throw BusinessServiceException.getInstance(resultModel.getCode(), resultModel.getMessage());
        }
        DaVirtualVoiceDTO dataResult = resultModel.getDataResult();
        DaVirtualVoiceVO daVirtualVoiceVO = VirtualVoiceHelper.cnvDaVirtualVoiceDTO2VO(dataResult);
        return ResultModel.success(daVirtualVoiceVO);
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("新增或修改数字人声音信息")
    public ResultModel voiceSaveOrUpdate(@RequestBody @Validated DaVirtualVoiceParam param) {
        DaVirtualVoiceDTO request = VirtualVoiceHelper.cnvDaVirtualVoiceParam2DTO(param);

        ResultModel resultModel = aiServiceManager.voiceSaveOrUpdate(request);
        if (!resultModel.getCode().equals("0")) {
            throw BusinessServiceException.getInstance(resultModel.getCode(), resultModel.getMessage());
        }
        return ResultModel.success(null);
    }

    @PostMapping("/authorize")
    @ApiOperation("授权智能语音")
    public ResultModel voiceAuthorize(@RequestBody @Validated DaVirtualVoiceAuthorizeParam param) {
        DaVirtualVoiceAuthorizeDTO request = new DaVirtualVoiceAuthorizeDTO();
        request.setBizId(Long.valueOf(param.getBizId()));
        request.setTenantCodeList(param.getTenantCodeList());
        if (CollectionUtils.containsAny(param.getTenantCodeList(), Lists.newArrayList(DEFAULT))) {
            List<TenantInfoDTO> tenantInfoList = tenantInfoService.listAll();
            request.setTenantCodeList(
                    tenantInfoList.stream().map(TenantInfoDTO::getTenantCode).distinct().collect(Collectors.toList()));
        }
        ResultModel resultModel = aiServiceManager.voiceAuthorize(request);
        if (!resultModel.getCode().equals("0")) {
            throw BusinessServiceException.getInstance(resultModel.getCode(), resultModel.getMessage());
        }
        return ResultModel.success(null);

    }

    @PostMapping("/enable")
    @ApiOperation("启用或停用智能语音")
    public ResultModel voiceEnable(@RequestBody @Validated DaVirtualVoiceUpdateParam param) {
        DaVirtualVoiceUpdateDTO request = new DaVirtualVoiceUpdateDTO();
        BeanUtils.copyProperties(param, request);
        request.setBizId(Long.valueOf(param.getBizId()));
        ResultModel resultModel = aiServiceManager.voiceEnable(request);
        if (!resultModel.getCode().equals("0")) {
            throw BusinessServiceException.getInstance(resultModel.getCode(), resultModel.getMessage());
        }
        return ResultModel.success(null);

    }

    @PostMapping("/delete")
    @ApiOperation("删除智能语音")
    public ResultModel voiceDelete(@RequestBody @Validated DaVirtualVoiceUpdateParam param) {
        DaVirtualVoiceUpdateDTO request = new DaVirtualVoiceUpdateDTO();
        BeanUtils.copyProperties(param, request);
        request.setBizId(Long.valueOf(param.getBizId()));
        ResultModel resultModel = aiServiceManager.voiceDelete(request);
        if (!resultModel.getCode().equals("0")) {
            throw BusinessServiceException.getInstance(resultModel.getCode(), resultModel.getMessage());
        }
        return ResultModel.success(null);
    }

    @GetMapping("/channel")
    @ApiOperation("查询声音渠道")
    public ResultModel<List<DaVirtualVoiceChannelVO>> channel() {
        ResultModel<List<DaVirtualVoiceChannelDTO>> resultModel = aiServiceManager.voiceChannel();
        if (!resultModel.getCode().equals("0")) {
            throw BusinessServiceException.getInstance(resultModel.getCode(), resultModel.getMessage());
        }
        List<DaVirtualVoiceChannelVO> resultList = resultModel.getDataResult().stream().map(result -> {
            DaVirtualVoiceChannelVO daVirtualVoiceChannelVO = new DaVirtualVoiceChannelVO();
            BeanUtils.copyProperties(result, daVirtualVoiceChannelVO);
            return daVirtualVoiceChannelVO;
        }).collect(Collectors.toList());

        return ResultModel.success(resultList);
    }

    @PostMapping("/producetts")
    @ApiOperation("语音合成")
    public ResultModel<TtsVO> produceTts(@RequestBody @Validated TtsParam param) {
        TTSProduceParamDTO paramDTO = new TTSProduceParamDTO();
        paramDTO.setVoiceName(param.getVoiceKey());
        paramDTO.setText(param.getText());
        paramDTO.setAudioEncode(this.decideTTsAudioEncode(param.getChannel()));
        paramDTO.setSpeed(param.getSpeed());
        paramDTO.setVolume(param.getVolume());
        paramDTO.setNeedSubtitle(Const.ZERO);
        paramDTO.setVoiceType(param.getVoiceType());

        TTSResponseDTO ttsResponseDTO = aiServiceManager.tts(param.getChannel(), paramDTO);

        String audioUrl = ttsResponseDTO.getAudioUrl();
        //腾讯云TTS音频链接是有时效性的，因此需要转存到永久桶
        if (ServiceChannelEnum.IVH.getCode().equals(param.getChannel()) || ServiceChannelEnum.FUJIA_IVH.getCode()
                .equals(param.getChannel())) {
            audioUrl = this.transfertToTtsBucket(ttsResponseDTO.getAudioUrl());
        }

        TtsVO ttsVO = new TtsVO();
        ttsVO.setDuration(new BigDecimal(ttsResponseDTO.getDuration()).multiply(new BigDecimal(1000)).longValue());
        ttsVO.setAudioUrl(audioUrl);
        return ResultModel.success(ttsVO);
    }

    private String decideTTsAudioEncode(Integer channel) {
        //深声科技音频格式
        if (ServiceChannelEnum.DEEP_SOUND.getCode().equals(channel)) {
            return "audio/mp3";
        } else {
            return "mp3";
        }
    }

    public String transfertToTtsBucket(String audioUrl) {
        File file = null;
        try {
            String decodeUrl = URLDecoder.decode(audioUrl, "UTF-8");
            String obejetKey = DownloadUtil.getCosKeyFromUrlString(decodeUrl);
            //要对obejctKey做下encode，不然一些字符无法识别。比如（）
            String encodeUrl = DownloadUtil.encodeCosKeyFromUrlString(decodeUrl);
            //文件下载
            file = DownloadUtil.downloadFile(encodeUrl, localPathPrefix + obejetKey);
        } catch (Exception e) {
            LOGGER.error("处理文件下载出现异常! audioUrl:{}, e:{}", audioUrl, e);
            FileUtils.deleteQuietly(file);
            throw BusinessServiceException.getInstance("tts音频下载失败");
        }

        String newUrl;
        try {
            newUrl = cosFileUploadManager.uploadFile(file, null, null, AUDITION_TTS_COS_PATH);
        } catch (Exception e) {
            LOGGER.error("转存到tts桶失败,audioUrl:{}", audioUrl, e);
            throw BusinessServiceException.getInstance("转存到tts桶失败");
        } finally {
            FileUtils.deleteQuietly(file);
        }
        LOGGER.info("转存到tts桶成功,原音频url:{}, 新音频url:{}", audioUrl, newUrl);
        return newUrl;
    }

}
