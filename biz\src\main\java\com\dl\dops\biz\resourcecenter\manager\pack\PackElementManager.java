package com.dl.dops.biz.resourcecenter.manager.pack;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.resourcecenter.dal.pack.po.PackElementPO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackElementBO;
import com.dl.dops.biz.resourcecenter.manager.pack.bo.PackElementSaveBO;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 13:36
 */
public interface PackElementManager extends IService<PackElementPO> {

    /**
     * 执行批量插入
     *
     * @param packId            服务包id  必填
     * @param chainPathId       链路id   非必填
     * @param branchId          分支id   非必填
     * @param elementSaveBOList 元素列表
     * @return
     */
    List<PackElementBO> doBatchInsert(Long packId, Long chainPathId, Long branchId,
            List<PackElementSaveBO> elementSaveBOList);

    /**
     * 执行批量修改
     *
     * @param elementSaveBOList
     * @return
     */
    List<PackElementBO> doBatchUpdate(List<PackElementSaveBO> elementSaveBOList);

    /**
     * 根据服务包id查询元素
     *
     * @param packId      必填
     * @param chainPathId 非必填
     * @return
     */
    List<PackElementBO> listByPackId(Long packId, Long chainPathId);

    /**
     * 根据服务包ids查询元素
     *
     * @param packIds 必填
     * @return
     */
    List<PackElementBO> listByPackIds(List<Long> packIds);
}
