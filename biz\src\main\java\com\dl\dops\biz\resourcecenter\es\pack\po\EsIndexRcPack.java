package com.dl.dops.biz.resourcecenter.es.pack.po;

import cn.easyes.annotation.TableField;
import cn.easyes.annotation.TableId;
import cn.easyes.annotation.TableName;
import cn.easyes.common.enums.FieldType;
import cn.easyes.common.enums.IdType;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-15 14:36
 */
@Data
@TableName("dl_rc_pack")
public class EsIndexRcPack {

    @TableId(type = IdType.CUSTOMIZE)
    private String id;

    @TableField(value = "pack_id", fieldType = FieldType.LONG)
    private Long packId;

    @TableField("title")
    private String title;

    @TableField(value = "category1", fieldType = FieldType.LONG)
    private Long category1;

    @TableField(value = "category2", fieldType = FieldType.LONG)
    private Long category2;

    @TableField("domain")
    private Integer domain;

    @TableField("scene_overview")
    private String sceneOverview;

    @TableField("detailed_description")
    private String detailedDescription;

    @TableField("suggest")
    private String suggest;

    @TableField("status")
    private Integer status;

    @TableField("scene")
    private Integer scene;

    @TableField("create_dt")
    private Date createDt;

    @TableField(value = "create_by", fieldType = FieldType.LONG)
    private Long createBy;

    @TableField("modify_dt")
    private Date modifyDt;

    @TableField(value = "modify_by", fieldType = FieldType.LONG)
    private Long modifyBy;

    @TableField("is_deleted")
    private Integer isDeleted;

    @TableField(value = "creator_name", fieldType = FieldType.KEYWORD)
    private String creatorName;

    @TableField(value = "modify_name", fieldType = FieldType.KEYWORD)
    private String modifyName;
}
