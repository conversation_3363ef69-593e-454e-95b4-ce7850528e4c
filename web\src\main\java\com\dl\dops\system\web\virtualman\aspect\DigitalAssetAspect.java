package com.dl.dops.system.web.virtualman.aspect;

import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.enums.SymbolE;
import com.dl.dops.biz.common.util.SpelExpressionUtils;
import com.dl.dops.biz.system.dal.operatelog.po.SysDigitalAssetOperateLogPO;
import com.dl.dops.biz.system.manager.operatelog.SysDigitalAssetOperateLogManager;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Objects;

@Slf4j
@Aspect
@Component
public class DigitalAssetAspect {

    @Resource
    private SysDigitalAssetOperateLogManager sysDigitalAssetOperateLogManager;

    @AfterReturning(value = "@annotation(com.dl.dops.system.web.virtualman.aspect.DigitalAssetOperate)", returning =
            "methodResult")
    public void around(JoinPoint joinPoint, Object methodResult) {
        try {
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            Method method = methodSignature.getMethod();
            DigitalAssetOperate operate = method.getAnnotation(DigitalAssetOperate.class);
            SysDigitalAssetOperateLogPO sysDigitalAssetOperateLogPO = new SysDigitalAssetOperateLogPO();
            sysDigitalAssetOperateLogPO.setOperateType(operate.operateType());
            String operateValue = operate.value();
            if (Const.NINETY_NIE == operate.specialFlag()) {
                // 需要从返回结果中获取 bizId信息 的情况
                if (methodResult instanceof ResultModel) {
                    sysDigitalAssetOperateLogPO.setBizId(((ResultModel<String>) methodResult).getDataResult());
                }
            } else {
                String bizId = SpelExpressionUtils.generateKeyBySpEL(operate.bizId(), joinPoint);
                sysDigitalAssetOperateLogPO.setBizId(bizId);
            }
            Assert.isTrue(StringUtils.isNotBlank(sysDigitalAssetOperateLogPO.getBizId()), "业务唯一标识必填");
            sysDigitalAssetOperateLogPO.setOperateTypeDesc(operateValue);
            Object[] args = joinPoint.getArgs();
            if (Objects.nonNull(args) && args.length > Const.ZERO) {
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < args.length; i++) {
                    buffer.append(JsonUtils.toJSON(args[i])).append(SymbolE.SINGLE_VERTICAL_BAR.getValue());
                }
                sysDigitalAssetOperateLogPO.setOperateCnt(buffer.toString());
            }
            sysDigitalAssetOperateLogManager.save(sysDigitalAssetOperateLogPO);
        } catch (Exception e) {
            log.error("写入operate审计日志异常！", e);
        }
    }

}
