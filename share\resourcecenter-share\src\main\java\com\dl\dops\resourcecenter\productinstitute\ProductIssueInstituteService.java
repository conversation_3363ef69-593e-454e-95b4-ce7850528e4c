package com.dl.dops.resourcecenter.productinstitute;

import com.dl.dops.resourcecenter.productinstitute.dto.ProductIssueInstituteDTO;

import java.util.List;

/**
 * 产品机构服务
 */
public interface ProductIssueInstituteService {

    /**
     * 查询产品机构
     * @param nameSearchPattern 机构名称过滤条件，null或者空串表示不过滤
     * @return 符合条件的机构清单
     */
    List<ProductIssueInstituteDTO> queryProductIssueInstitute(String nameSearchPattern);

    /**
     * 按照产品机构ID查找信息
     * @param instituteId 产品机构ID
     * @return 产品机构信息
     */
    ProductIssueInstituteDTO getByInstituteId(Long instituteId);
}
