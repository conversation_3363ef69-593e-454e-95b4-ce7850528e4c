package com.dl.dops.magicvideo.web.statistics;

import com.dl.dops.biz.common.forest.magicvideo.dto.CountVO;
import com.dl.dops.biz.common.forest.magicvideo.dto.EfficiencyVO;
import com.dl.dops.biz.common.forest.magicvideo.dto.FailJobStatisticsDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.StatisticsJobTenantSummaryDTO;
import com.dl.dops.biz.common.forest.magicvideo.param.CountParam;
import com.dl.dops.biz.common.forest.magicvideo.param.EfficiencyParam;
import com.dl.dops.biz.common.forest.magicvideo.param.FailJobStatisticsParam;
import com.dl.dops.biz.common.forest.magicvideo.param.StatisticsJobTenantSummaryQueryParam;
import com.dl.dops.biz.magicvideo.manager.MagicVideoManager;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @describe: 统计相关接口
 * @author: hongcj
 * @date: 2023/6/17 14:43
 */
@Slf4j
@RestController
@RequestMapping("/dops/magicvideo/statistics")
@Api("生产看板管理")
public class StatisticsInternalController {
    @Autowired
    private MagicVideoManager magicVideoManager;

    @PostMapping("/count")
    @ApiOperation("生产数量统计")
    public ResultModel<List<CountVO>> count(@RequestBody @Validated CountParam param) {
        return magicVideoManager.count(param);
    }

    @PostMapping("/efficiency")
    @ApiOperation("生产效率统计")
    public ResultModel<List<EfficiencyVO>> efficiency(@RequestBody @Validated EfficiencyParam param) {
        return magicVideoManager.efficiency(param);
    }

    @PostMapping("/failstatistics")
    @ApiOperation("生产失败数据统计")
    public ResultModel<List<FailJobStatisticsDTO>> failStatistics(
            @RequestBody @Validated FailJobStatisticsParam param) {
        return magicVideoManager.failStatistics(param);
    }

    @PostMapping("/specifictenantsummary")
    @ApiOperation("指定租户的任务数据统计汇总")
    public ResultModel<StatisticsJobTenantSummaryDTO> specificTenantSummary(
            @RequestBody @Validated StatisticsJobTenantSummaryQueryParam param) {
        return magicVideoManager.specificTenantSummary(param);
    }
}
