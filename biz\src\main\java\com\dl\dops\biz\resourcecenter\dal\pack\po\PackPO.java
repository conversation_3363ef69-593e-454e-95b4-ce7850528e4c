package com.dl.dops.biz.resourcecenter.dal.pack.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

@Data
@TableName("pack")
public class PackPO extends BasePO {
    private static final long serialVersionUID = 7141379160306035608L;

    @TableId
    private Long id;

    @TableField("pack_id")
    private Long packId;

    @TableField("title")
    private String title;

    @TableField("category1")
    private Long category1;

    @TableField("category2")
    private Long category2;

    @TableField("scene_overview")
    private String sceneOverview;

    @TableField("domain")
    private Integer domain;

    @TableField("detailed_description")
    private String detailedDescription;

    @TableField("suggest")
    private String suggest;

    @TableField("status")
    private Integer status;

    /**
     * 场景
     *
     */
    @TableField("scene")
    private Integer scene;

    @TableField("is_deleted")
    private Integer isDeleted;

    @TableField("source")
    private String source;

    @TableField("creator_name")
    private String creatorName;

    @TableField("modify_name")
    private String modifyName;

}
