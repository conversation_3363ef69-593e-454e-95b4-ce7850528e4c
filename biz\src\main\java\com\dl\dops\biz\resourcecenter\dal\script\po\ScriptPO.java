package com.dl.dops.biz.resourcecenter.dal.script.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

/**
 * @TableName script
 */
@TableName(value = "script")
@Data
public class ScriptPO extends BasePO {
    private static final long serialVersionUID = 754028304199417806L;
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 话术id
     */
    private Long scriptId;

    /**
     * 问题
     */
    private String question;

    /**
     * 内容
     */
    private String content;

    /**
     * 一级分类id
     */
    private Long category1;

    /**
     * 二级分类id
     */
    private Long category2;

    /**
     * 是否删除  0否 1是
     */
    private Integer isDeleted;

    @TableField("publish_status")
    private Integer publishStatus;

    @Deprecated
    @TableField("source")
    private String source;

    private String remark;

    @TableField("creator_name")
    private String creatorName;

    @TableField("modify_name")
    private String modifyName;
}