package com.dl.dops.biz.resourcecenter.manager.sop.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-07 09:45
 */
@Data
public class SopEventAddBO {

    /**
     * sopId
     */
    private Long sopId;

    /**
     * 触达形式 1群发 2任务下发 3纯提醒 4企微朋友圈群发 5修改旅程阶段 6一对一私聊
     */
    private Integer reachType;

    /**
     * 事项名称
     */
    private String name;

    /**
     * 时间规则类型 1定时推送   2周期推送
     */
    private Integer ruleType;

    /**
     * 规则内容，定时推送时为天-HH:mm:ss  周期推送为首次天-天-HH:mm:ss
     */
    private String ruleContent;

    /**
     * 发送内容
     */
    private String content;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效期
     */
    private Integer validityPeriod;

}
