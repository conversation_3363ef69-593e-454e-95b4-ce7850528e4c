package com.dl.dops.biz.resourcecenter.manager.sop.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.resourcecenter.dal.sop.SopMapper;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopAddBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopModifyBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopSearchBO;
import com.dl.dops.biz.resourcecenter.manager.sop.helper.SopHelper;
import com.dl.dops.biz.resourcecenter.dal.sop.po.SopPO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.resourcecenter.manager.sop.SopManager;
import com.dl.dops.resourcecenter.sop.enums.RcSopStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SopManagerImpl extends ServiceImpl<SopMapper, SopPO> implements SopManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(SopManagerImpl.class);

    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Resource
    private OperatorUtil operatorUtil;

    @Override
    public Long add(SopAddBO bo) {
        Assert.isTrue(StringUtils.isNotBlank(bo.getName()), "SOP名称不可为空");
        Assert.isTrue(StringUtils.isNotBlank(bo.getName()), "租户编号不可为空");

        Long sopId = Objects.isNull(bo.getSopId()) ? hostTimeIdg.generateId().longValue() : bo.getSopId();
        SopPO po = new SopPO();
        po.setCategory1(bo.getCategory1());
        po.setCategory2(bo.getCategory2());
        po.setSopId(sopId);
        po.setRemark(bo.getRemark());
        po.setName(bo.getName());
        po.setSource(bo.getSource());
        po.setStatus(RcSopStatusEnum.DRAFT.getCode());
        po.setSopType(bo.getSopType());
        po.setCreateBy(operatorUtil.getOperator());
        po.setCreatorName(operatorUtil.getOperatorName());
        po.setModifyBy(operatorUtil.getOperator());
        po.setModifyName(operatorUtil.getOperatorName());
        baseMapper.insert(po);
        return sopId;
    }

    @Override
    public void modify(SopModifyBO bo) {
        SopPO po = new SopPO();
        po.setSopId(bo.getSopId());
        if (Objects.nonNull(bo.getCategory1())) {
            po.setCategory1(bo.getCategory1());
        }
        if (Objects.nonNull(bo.getCategory2())) {
            po.setCategory2(bo.getCategory2());
        }
        if (StringUtils.isNotBlank(bo.getName())) {
            po.setName(bo.getName());
        }
        if (StringUtils.isNotBlank(bo.getRemark())) {
            po.setRemark(bo.getRemark());
        }
        if (Objects.nonNull(bo.getStatus())) {
            po.setStatus(bo.getStatus());
        }
        po.setModifyBy(operatorUtil.getOperator());
        po.setModifyName(operatorUtil.getOperatorName());

        baseMapper.update(po, Wrappers.lambdaUpdate(SopPO.class).eq(SopPO::getSopId, bo.getSopId()));
    }

    @Override
    public IPage<SopBO> pageQuery(SopSearchBO bo) {
        LambdaQueryWrapper<SopPO> queryWrapper = Wrappers.lambdaQuery(SopPO.class);
        queryWrapper.likeRight(StringUtils.isNotBlank(bo.getName()), SopPO::getName, bo.getName())
                .eq(Objects.nonNull(bo.getStatus()), SopPO::getStatus, bo.getStatus())
                .eq(Objects.nonNull(bo.getSopType()), SopPO::getSopType, bo.getSopType())
                .in(CollectionUtils.isNotEmpty(bo.getStatusList()), SopPO::getStatus, bo.getStatusList())
                .eq(Objects.nonNull(bo.getCategory2()), SopPO::getCategory2, bo.getCategory2())
                .eq(SopPO::getIsDeleted, 0).orderByDesc(SopPO::getId);

        if (Objects.nonNull(bo.getCategory1())) {
            //0表示未分类 ，-1表示查询全部
            if (bo.getCategory1() == 0) {
                queryWrapper.isNull(SopPO::getCategory1);
            } else if (bo.getCategory1() != -1) {
                queryWrapper.eq(SopPO::getCategory1, bo.getCategory1());
            }
        }

        IPage<SopPO> pageResult = baseMapper.selectPage(convert(bo), queryWrapper);
        return pageResult.convert(SopHelper::cnvSopPO2DTO);
    }

    @Override
    public SopBO selectBySopId(Long sopId) {
        Assert.isTrue(Objects.nonNull(sopId), "sopId不能为空");

        SopPO po = baseMapper
                .selectOne(Wrappers.lambdaQuery(SopPO.class).eq(SopPO::getSopId, sopId).eq(SopPO::getIsDeleted, 0));
        return SopHelper.cnvSopPO2DTO(po);
    }

    @Override
    public Map<Long, SopBO> getSopMapBySopIds(List<Long> sopIds) {
        if (CollectionUtils.isEmpty(sopIds)) {
            return Collections.emptyMap();
        }
        List<SopPO> poList = baseMapper
                .selectList(Wrappers.lambdaQuery(SopPO.class).in(SopPO::getSopId, sopIds).eq(SopPO::getIsDeleted, 0));
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyMap();
        }

        return poList.stream().map(po -> {
            SopBO sopBO = new SopBO();
            SopHelper.fillSopDTO(po, sopBO);
            return sopBO;
        }).collect(Collectors.toMap(SopBO::getSopId, Function.identity()));
    }

    @Override
    public void logicDelete(Long sopId) {
        Assert.isTrue(Objects.nonNull(sopId), "sopId不能为空");
        SopPO updatePO = new SopPO();
        updatePO.setIsDeleted(1);
        updatePO.setDeleteDt(System.currentTimeMillis());
        baseMapper.update(updatePO, Wrappers.lambdaUpdate(SopPO.class).eq(SopPO::getSopId, sopId));
    }

    @Override
    public void tplPublishOrCancel(Long sopId) {
        Assert.notNull(sopId, "sop模板id为空");
        SopPO tpl = baseMapper.selectOne(
                Wrappers.lambdaQuery(SopPO.class).eq(SopPO::getSopId, sopId).eq(SopPO::getIsDeleted, Const.ZERO));
        Assert.notNull(tpl, "sop模板不存在");

        Integer needToBePublistStatus = RcSopStatusEnum.CANCEL_RELEASED.getCode().equals(tpl.getStatus()) ?
                RcSopStatusEnum.RELEASED.getCode() :
                RcSopStatusEnum.CANCEL_RELEASED.getCode();

        this.update(
                Wrappers.lambdaUpdate(SopPO.class).eq(SopPO::getSopId, sopId).eq(SopPO::getModifyDt, tpl.getModifyDt())
                        .set(SopPO::getStatus, needToBePublistStatus).set(SopPO::getModifyDt, new Date())
                        .set(SopPO::getModifyBy, operatorUtil.getOperator()));
    }

}
