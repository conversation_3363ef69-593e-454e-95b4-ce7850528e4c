package com.dl.dops.resourcecenter.pack.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 17:14
 */
@Data
@ApiModel("服务包元素附件保存参数")
public class RcPackElementAttachmentSaveParam implements Serializable {

    private static final long serialVersionUID = 7061774015266417644L;
    @ApiModelProperty("素材id")
    private String materialId;

    @ApiModelProperty("海报id")
    private String posterId;

}
