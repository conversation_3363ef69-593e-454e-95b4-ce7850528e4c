package com.dl.dops.biz.resourcecenter.dal.organize.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 金融机构组织
 *
 * @TableName organize
 */
@Data
@TableName("organize")
public class OrganizePO extends BasePO implements Serializable {
    /**
     * 表ID
     */
    private Long id;

    /**
     * 组织id
     */
    private Long organizeId;

    /**
     * 组织名称
     */
    private String organizeName;

    /**
     * 组织logo小图标
     */
    private String logoIconUrl;

    /**
     * 组织logo高清图
     */
    private String logoHighResUrl;

    /**
     * 排序（数字越大排序越靠前）
     */
    private Integer sort;

    private static final long serialVersionUID = 1L;

}