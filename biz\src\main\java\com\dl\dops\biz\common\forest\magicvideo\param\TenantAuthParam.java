package com.dl.dops.biz.common.forest.magicvideo.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:TenantAuthParam
 */
 @Data
public class TenantAuthParam {
    @ApiModelProperty(value = "授权模板id", required = true)
    @NotBlank
    private String templateId;

    @ApiModelProperty("状态 0-启用 1-禁用")
    @NotNull
    private Integer status;

    @ApiModelProperty("租户名称")
    @NotBlank
    private String tenantName;

    @ApiModelProperty("租户编码")
    @NotBlank
    private String tenantCode;

}
