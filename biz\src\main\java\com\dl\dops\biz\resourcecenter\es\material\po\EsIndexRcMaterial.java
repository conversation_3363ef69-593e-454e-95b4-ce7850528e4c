package com.dl.dops.biz.resourcecenter.es.material.po;

import cn.easyes.annotation.TableField;
import cn.easyes.annotation.TableId;
import cn.easyes.annotation.TableName;
import cn.easyes.common.enums.FieldType;
import cn.easyes.common.enums.IdType;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Date;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@TableName(value = "dl_rc_material")
public class EsIndexRcMaterial {

    @TableId(value = "biz_id", type = IdType.CUSTOMIZE)
    Long bizId;

    @TableField(value = "title", fieldType = FieldType.KEYWORD_TEXT, analyzer = "ik_smart", searchAnalyzer = "ik_smart")
    String title;

    @TableField(value = "remark", fieldType = FieldType.KEYWORD)
    String remark;

    @TableField(value = "content", fieldType = FieldType.KEYWORD)
    String content;

    @TableField(value = "logo_img", fieldType = FieldType.KEYWORD)
    String logoImg;

    @TableField(value = "category_ids", fieldType = FieldType.KEYWORD)
    String categoryIds;

    @TableField(value = "create_by", fieldType = FieldType.LONG)
    Long createBy;

    @TableField(value = "creator_name", fieldType = FieldType.KEYWORD)
    String creatorName;

    @TableField(value = "modify_name", fieldType = FieldType.KEYWORD)
    String modifyName;

    @TableField(value = "modify_by", fieldType = FieldType.LONG)
    Long modifyBy;

    @TableField(value = "modify_dt", fieldType = FieldType.DATE)
    Date modifyDt;

    @TableField(value = "create_dt", fieldType = FieldType.DATE)
    Date createDt;

    @TableField(value = "is_deleted", fieldType = FieldType.BOOLEAN)
    Boolean isDeleted;

    @TableField(value = "material_type", fieldType = FieldType.INTEGER)
    String materialType;

    @TableField(value = "size", fieldType = FieldType.INTEGER)
    Integer size;

    @TableField(value = "publish_status", fieldType = FieldType.INTEGER)
    Integer publishStatus;

    @TableField(value = "create_from", fieldType = FieldType.INTEGER)
    Integer createFrom;

    @TableField(value = "tag_ids", fieldType = FieldType.ARRAY)
    List<String> tagIds;
}
