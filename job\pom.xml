<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dl.dops.web</groupId>
        <artifactId>dl-dops-web</artifactId>
        <version>1.0.2-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dl-dops-web-job</artifactId>
    <packaging>jar</packaging>
    <name>dl-dops-web-job</name>

    <dependencies>
        <dependency>
            <groupId>com.dl.dops.web</groupId>
            <artifactId>dl-dops-web-biz</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- xxl-job-core -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.3.0</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>3.0.10</version>
        </dependency>
    </dependencies>
</project>