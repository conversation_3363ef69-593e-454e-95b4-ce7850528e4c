package com.dl.dops.biz.common.service.menu.impl;

import cn.hutool.json.JSONUtil;
import com.dl.dops.biz.common.forest.basicservice.MenuClient;
import com.dl.dops.biz.common.forest.basicservice.dto.BasicServiceMenuObjectDTO;
import com.dl.dops.biz.common.forest.basicservice.param.BasicServiceSysMenuParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.BasicServiceTenantMenuSaveParamDTO;
import com.dl.dops.biz.common.service.menu.BasicServiceMenuService;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-15 15:40
 */
@Component
public class BasicServiceMenuServiceImpl implements BasicServiceMenuService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BasicServiceMenuServiceImpl.class);

    @Resource
    private MenuClient menuClient;

    @Resource
    private OperatorUtil operatorUtil;

    @Override
    public BasicServiceMenuObjectDTO tenantMenuList(BasicServiceSysMenuParamDTO param) {
        ResultModel<BasicServiceMenuObjectDTO> resultModel = menuClient.tenantMenuList(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("查询可分配给租户的系统菜单列表失败，param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询可分配给租户的系统菜单列表失败," + resultModel.getMessage());
        }

        return resultModel.getDataResult();
    }

    @Override
    public Boolean saveTenantMenu(BasicServiceTenantMenuSaveParamDTO param) {
        param.setOperatorId(operatorUtil.getOperator());
        param.setOperatorName(operatorUtil.getOperatorName());
        ResultModel<Boolean> resultModel = menuClient.saveTenantMenu(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("保存指定租户的菜单和功能项失败，param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("保存指定租户的菜单和功能项失败," + resultModel.getMessage());
        }

        return resultModel.getDataResult();
    }
}
