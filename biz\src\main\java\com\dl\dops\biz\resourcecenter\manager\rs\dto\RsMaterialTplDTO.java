package com.dl.dops.biz.resourcecenter.manager.rs.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-10-21 14:48
 */
@Data
public class RsMaterialTplDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("素材id")
    private String materialId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("摘要")
    private String remark;

    @ApiModelProperty("logo图地址")
    private String logoImg;

    @ApiModelProperty("分类Id")
    private String categoryId;

    @ApiModelProperty("素材大小")
    private String size;

    @ApiModelProperty("素材类型")
    private Integer materialType;

    private Integer articleType;

    @ApiModelProperty("素材内容")
    private String content;

    private Long createBy;

    private Long modifyBy;

    @ApiModelProperty("微信公众号文章地址")
    private String mpArticleSourceUrl;

    @ApiModelProperty("创建时间")
    private Date createTime;

    private Integer publishStatus;

}
