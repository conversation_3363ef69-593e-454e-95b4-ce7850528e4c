package com.dl.dops.biz.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dl.dops.biz.common.util.spider.HttpTool;
import com.dl.dops.biz.common.util.spider.Resp;
import com.dl.dops.biz.common.util.spider.SpiderUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.Charsets;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpGet;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.springframework.util.Assert;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName SpiderUtilTest
 * @Description
 * <AUTHOR>
 * @Date 2022/7/11 9:07
 * @Version 1.0
 **/
@Slf4j
public class SpiderUtilTest {

    private final static String VIDEO =
            "<video src=\"%s\" poster=\"%s\" webkit-playsinline=\"isiPhoneShowPlaysinline\" "
                    + "playsinline=\"isiPhoneShowPlaysinline\" preload=\"metadata\" crossorigin=\"anonymous\" "
                    + "controlslist=\"nodownload\" class=\"video_fill\"> 您的浏览器不支持 video 标签 </video>";
    private final static String GET_MP4 =
            "https://mp.weixin.qq.com/mp/videoplayer?action=get_mp_video_play_url&preview=0&__biz"
                    + "=MjM5Mjc5NDAzMw==&mid=2650985977&idx=1&vid=%s";

    @Test
    public void test() {
        String url = "https://mp.weixin.qq.com/s/ligKIbSY-JZBF565GT4nFA";
        Resp<JSONObject> resp = SpiderUtil.getArticle(url);
        JSONObject body = resp.getBody();
        String title = body.getString("title");
        System.out.println(title);
        if (resp.isSuccess()) {
            System.out.println(">>>" + resp.getBody().toString());
        } else {
            System.out.println(resp.getMsg());
        }
    }

    @Test
    public void test1() throws UnsupportedEncodingException {
        String url = "https://mp.weixin.qq.com/s/zkcSoSarZwzZnq6NBFAGOg";
        Resp<Document> resp = SpiderUtil.getDocument(url);
        Assert.isTrue(resp.isSuccess(), resp.getMsg());
        Element articlePart = resp.getBody().getElementById(SpiderUtil.KEY_JS_ARTICLE);
        if (Objects.isNull(articlePart)) {
            log.error("不存在要抓取的内容！url={}", url);
            return;
        }
        Elements allElements = articlePart.getAllElements();
        for (Element e : allElements) {
            if (Objects.isNull(e) || !e.children().isEmpty()) {
                continue;
            }
            String tagName = e.tagName();
            switch (tagName) {
            case "img":
                String attr = e.attr("data-src");
                if (StringUtils.isNotBlank(attr)) {
                    e.attr("src", "liuxt");
                }
                break;
            case "iframe":
                String attr1 = e.attr("data-mpvid");
                String attr2 = URLDecoder.decode(e.attr("data-cover"), Charsets.UTF_8.name());
                Element parent = e.parent();
                e.remove();
                parent.append(String.format(VIDEO, attr1, attr2));
            }
        }
        System.out.println(articlePart.html());
    }

    @Test
    public void test2() {
        String mpVid = "wxv_2480444702190911490";
        HttpGet post = new HttpGet(String.format(GET_MP4, mpVid));
        String respString = HttpTool.getRespString(post);
        JSONObject jsonObject = JSON.parseObject(respString);
        JSONArray urlArray = jsonObject.getJSONArray("url_info");
        List<Object> filesize = urlArray.stream()
                .sorted(Comparator.comparing(x -> (Integer) ((JSONObject) x).get("filesize")))
                .collect(Collectors.toList());
        filesize.stream().forEach(x -> {
            System.out.println(((JSONObject) x).toString());
        });
        if (CollectionUtils.isNotEmpty(filesize)) {
            Object o = filesize.get(filesize.size() - 1);
            String url = (String) ((JSONObject) o).get("url");
            System.out.println(url);
        }
    }
}
