package com.dl.dops.resourcecenter.material.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-08 14:42
 */
@Data
public class RcMaterialUpdateParam implements Serializable {

    private static final long serialVersionUID = 239857819022666517L;
    @ApiModelProperty("素材id")
    @NotBlank
    private String materialId;

    @ApiModelProperty("标题")
    @Length(min = 1, max = 100)
    private String title;

    @ApiModelProperty("摘要")
    @Length(max = 100, message = "摘要长度0-100")
    private String remark;

    @ApiModelProperty("logo图地址")
    @Length(max = 500, message = "图片名超长，建议使用中文首字母缩写")
    private String logoImg;

    @ApiModelProperty("分类id")
    @Deprecated
    private String categoryId = "0";

    @ApiModelProperty("标签组类型")
    private Integer tagGroupType;

    @ApiModelProperty("标签id列表")
    private List<String> tagIds = new ArrayList<>();

    @ApiModelProperty("文件大小，单位KB")
    private Integer size;

    @ApiModelProperty("素材内容")
    private String content;

    @ApiModelProperty("微信公众号文章地址")
    private String mpArticleSourceUrl;

}
