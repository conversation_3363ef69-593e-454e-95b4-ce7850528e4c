package com.dl.dops.biz.magicvideo.manager;

import cn.hutool.json.JSONUtil;
import com.dl.dops.biz.common.forest.magicvideo.MagicVideoClient;
import com.dl.dops.biz.common.forest.magicvideo.dto.InternalChareletDeleteParam;
import com.dl.dops.biz.common.forest.magicvideo.dto.InternalChartletCategoryDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.InternalChartletInfoAddParam;
import com.dl.dops.biz.common.forest.magicvideo.dto.InternalChartletInfoDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.InternalChartletInfoUptParam;
import com.dl.dops.biz.common.forest.magicvideo.dto.InternalChartletPageParam;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-16 09:54
 */
@Component
public class MagicVideoChartletManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(MagicVideoChartletManager.class);

    @Autowired
    private MagicVideoClient magicVideoClient;

    public Long add(InternalChartletInfoAddParam param) {
        ResultModel<Long> resultModel = magicVideoClient.addChartlet(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("新增贴图失败，param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("新增贴图失败");
        }
        return resultModel.getDataResult();
    }

    public void update(InternalChartletInfoUptParam param) {
        ResultModel<Void> resultModel = magicVideoClient.updateChartlet(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("修改贴图失败，param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("修改贴图失败");
        }
    }

    public ResultPageModel<InternalChartletInfoDTO> page(InternalChartletPageParam param) {
        ResultPageModel<InternalChartletInfoDTO> resultModel = magicVideoClient.pageChartlet(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("分页查询贴图信息失败，param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("分页查询贴图信息失败");
        }
        return resultModel;
    }

    public void delete(InternalChareletDeleteParam param) {
        ResultModel<Void> resultModel = magicVideoClient.deleteChartlet(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("删除贴图失败，param:{},,,resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("删除贴图失败");
        }
    }

    public List<InternalChartletCategoryDTO> listAllCategory() {
        ResultModel<List<InternalChartletCategoryDTO>> resultModel = magicVideoClient.listAllCategory();
        if (!resultModel.isSuccess()) {
            LOGGER.error("获取全部贴图分类失败，resultModel:{}", JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("获取全部贴图分类失败");
        }
        return resultModel.getDataResult();
    }

}
