<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.dops.biz.resourcecenter.dal.pack.PackElementMapper">
    <resultMap id="resultMap" type="com.dl.dops.biz.resourcecenter.dal.pack.po.PackElementPO">
        <id column="id" property="id"/>
        <result column="pack_id" property="packId"/>
        <result column="element_id" property="elementId"/>
        <result column="chain_path_id" property="chainPathId"/>
        <result column="branch_id" property="branchId"/>
        <result column="type" property="type"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="attachments" property="attachments"/>
        <result column="ext_data" property="extData"/>
        <result column="sort" property="sort"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_dt" property="createDt"/>
        <result column="create_by" property="createBy"/>
        <result column="modify_dt" property="modifyDt"/>
        <result column="modify_by" property="modifyBy"/>
    </resultMap>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update pack_element
            set
            title = #{item.title},
            content = #{item.content},
            attachments = #{item.attachments},
            ext_data = #{item.extData},
            <if test="item.type != null">
                type = #{item.type},
            </if>
            <if test="item.sort != null">
                sort = #{item.sort},
            </if>
            <if test="item.modifyBy != null">
                modify_by = #{item.modifyBy},
            </if>
            modify_dt = now()
            where element_id = #{item.elementId} and is_deleted = 0
        </foreach>
    </update>

</mapper>