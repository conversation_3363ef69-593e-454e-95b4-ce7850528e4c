package com.dl.dops.biz.common.forest.magicvideo.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-02 09:53
 */
@Data
public class AssetProdInfoListParam {

    /**
     * 代码
     */
    @ApiModelProperty("代码列表")
    private List<String> secuCodeList;

    /**
     * 产品简称
     */
    @ApiModelProperty("产品简称列表")
    private List<String> prodShortnameList;

    /**
     * 分类
     *
     * @see: com.dl.magicvideo.biz.manager.data.enums.AssetProdCategoryEnum
     */
    @NotBlank(message = "分类不能为空")
    @ApiModelProperty("分类，股票:STOCK")
    private String secucategory;
}
