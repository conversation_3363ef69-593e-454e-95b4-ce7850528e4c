package com.dl.dops.system.web.virtualman;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.aiservice.share.digitalasset.DaVirtualManAuthDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManPageRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.dops.biz.common.annotation.Permission;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.enums.SymbolE;
import com.dl.dops.biz.common.forest.aiservice.AiServiceManager;
import com.dl.dops.biz.common.forest.basicservice.dto.TenantInfoDTO;
import com.dl.dops.biz.common.service.tenant.TenantInfoService;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.system.web.virtualman.aspect.DigitalAssetOperate;
import com.dl.dops.system.web.virtualman.param.EnableStateParam;
import com.dl.dops.system.web.virtualman.param.SceneAddParam;
import com.dl.dops.system.web.virtualman.param.SceneEnableStateParam;
import com.dl.dops.system.web.virtualman.param.SceneUpdateParam;
import com.dl.dops.system.web.virtualman.param.VmAddParam;
import com.dl.dops.system.web.virtualman.param.VmAuthParam;
import com.dl.dops.system.web.virtualman.param.VmSearchParam;
import com.dl.dops.system.web.virtualman.param.VmUpdateParam;
import com.dl.dops.system.web.virtualman.vo.SceneDetailInfoVO;
import com.dl.dops.system.web.virtualman.vo.VmDetailInfoVO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName VirtualManController
 * @Description
 * <AUTHOR>
 * @Date 2023/8/21 11:04
 * @Version 1.0
 **/
@Slf4j
@RestController
@Api(tags = "数字人管理")
@RequestMapping("/dops/vm")
public class VirtualManController extends AbstractController {

    private static final String DEFAULT = "DEFAULT";

    @Resource
    private AiServiceManager aiServiceManager;
    @Resource
    private TenantInfoService tenantInfoService;

    @PostMapping("/list")
    @Permission("adm:vmmanage:vm:list")
    @ApiOperation("查询数字人列表")
    public ResultPageModel<VmDetailInfoVO> vmList(@RequestBody VmSearchParam param) {
        DaVirtualManPageRequestDTO pageReq = new DaVirtualManPageRequestDTO();
        pageReq.setPageIndex(param.getPageIndex());
        pageReq.setPageSize(param.getPageSize());
        pageReq.setVmName(param.getName());
        pageReq.setVmCode(param.getVmCode());
        pageReq.setChannel(param.getChannel());
        ResultPageModel<DaVirtualManDTO> result = aiServiceManager.pageVm(pageReq);
        if (!result.isSuccess() || Const.ZERO.longValue() == result.getTotal()) {
            return pageQueryModel(new Page(param.getPageIndex(), param.getPageSize(), Const.ZERO),
                    Lists.newArrayList());
        }
        List<TenantInfoDTO> tenantList = tenantInfoService.listAll();
        Map<String, String> tenantCodeNameMap = tenantList.stream()
                .collect(Collectors.toMap(TenantInfoDTO::getTenantCode, TenantInfoDTO::getName, (a, b) -> b));
        return pageQueryModel(new Page(result.getPageIndex(), result.getPageSize(), result.getTotal()),
                result.getDataResult().stream().map(x -> convert(x, tenantCodeNameMap)).collect(Collectors.toList()));
    }

    private VmDetailInfoVO convert(DaVirtualManDTO source, Map<String, String> tenantCodeNameMap) {
        VmDetailInfoVO vmDetailInfoVO = new VmDetailInfoVO();
        vmDetailInfoVO.setVmBizId(source.getBizId().toString());
        vmDetailInfoVO.setVmCode(source.getVmCode());
        vmDetailInfoVO.setName(source.getVmName());
        vmDetailInfoVO.setExpireDate(source.getExpiryDt());
        vmDetailInfoVO.setGender(source.getGender());
        vmDetailInfoVO.setChannel(source.getChannel());
        vmDetailInfoVO.setHeadImg(source.getHeadImg());
        vmDetailInfoVO.setEnableState(source.getIsEnabled());
        vmDetailInfoVO.setDefaultSpeed(source.getDefaultSpeed());
        vmDetailInfoVO.setEnableSpeed(source.getEnableSpeed());
        vmDetailInfoVO.setVmVoiceKey(source.getVmVoiceKey());
        Long voiceBizId = source.getVoiceBizId();
        if (Objects.nonNull(voiceBizId)) {
            vmDetailInfoVO.setVoiceBizId(voiceBizId.toString());
        }
        List<String> tenantNameList = Lists.newArrayList();
        String authTenantCode = source.getAuthTenantCode();
        if (StringUtils.isNotBlank(authTenantCode)) {
            List<String> tenantCodeList = Arrays.stream(StringUtils.split(authTenantCode, SymbolE.COMMA.getValue()))
                    .filter(x -> !StringUtils.equals(x, DEFAULT))
                    .collect(Collectors.toList());
            tenantCodeList.stream()
                    .filter(x -> tenantCodeNameMap.containsKey(x))
                    .forEach(x -> tenantNameList.add(tenantCodeNameMap.get(x)));
        }
        vmDetailInfoVO.setTenantList(tenantNameList);
        String sceneNameList = source.getSceneNameList();
        if (StringUtils.isNotBlank(sceneNameList)) {
            vmDetailInfoVO.setVmSceneNameList(
                    Arrays.asList(StringUtils.split(sceneNameList, SymbolE.COMMA.getValue())));

        }
        return vmDetailInfoVO;
    }

    @PostMapping("/edit")
    @Permission("adm:vmmanage:vm:edit")
    @ApiOperation("修改数字人信息")
    @DigitalAssetOperate(value = "修改数字人信息", bizId = "#param.vmBizId", operateType = 1)
    public ResultModel vmEdit(@RequestBody @Validated VmUpdateParam param) {
        DaVirtualManDTO daVirtualMan = new DaVirtualManDTO();
        daVirtualMan.setBizId(param.getVmBizId());
        daVirtualMan.setVmName(param.getName());
        daVirtualMan.setHeadImg(param.getHeadImg());
        daVirtualMan.setChannel(param.getChannel());
        daVirtualMan.setVmCode(param.getVmCode());
        daVirtualMan.setGender(param.getGender());
        daVirtualMan.setExpiryDt(param.getExpireDate());
        daVirtualMan.setDefaultSpeed(param.getDefaultSpeed());
        daVirtualMan.setVoiceBizId(param.getVoiceBizId());
        daVirtualMan.setVmVoiceKey(param.getVmVoiceKey());
        return aiServiceManager.vmSaveOrUpdate(daVirtualMan);
    }

    @PostMapping("/add")
    @Permission("adm:vmmanage:vm:add")
    @ApiOperation("新增数字人信息")
    @DigitalAssetOperate(value = "新增数字人", specialFlag = 99)
    public ResultModel<String> vmAdd(@RequestBody @Validated VmAddParam param) {
        DaVirtualManDTO daVirtualMan = new DaVirtualManDTO();
        daVirtualMan.setVmName(param.getName());
        daVirtualMan.setHeadImg(param.getHeadImg());
        daVirtualMan.setChannel(param.getChannel());
        daVirtualMan.setVmCode(param.getVmCode());
        daVirtualMan.setGender(param.getGender());
        daVirtualMan.setExpiryDt(param.getExpireDate());
        daVirtualMan.setDefaultSpeed(param.getDefaultSpeed());
        daVirtualMan.setVmVoiceKey(param.getVmVoiceKey());
        daVirtualMan.setVoiceBizId(param.getVoiceBizId());
        return aiServiceManager.vmSaveOrUpdate(daVirtualMan);
    }

    @PostMapping("/enable")
    @Permission("adm:vmmanage:vm:enable")
    @ApiOperation("数字人启用停用")
    @DigitalAssetOperate(value = "数字人启用停用", bizId = "#param.vmBizId", operateType = 1)
    public ResultModel vmEnable(@RequestBody @Validated EnableStateParam param) {
        DaVirtualManDTO daVirtualMan = new DaVirtualManDTO();
        daVirtualMan.setBizId(param.getVmBizId());
        daVirtualMan.setIsEnabled(param.getEnableState());
        return aiServiceManager.vmSaveOrUpdate(daVirtualMan);
    }

    @PostMapping("/auth")
    @Permission("adm:vmmanage:vm:auth")
    @ApiOperation("数字人租户授权")
    @DigitalAssetOperate(value = "数字人租户授权", bizId = "#param.vmBizId", operateType = 1)
    public ResultModel vmAuth(@RequestBody @Validated VmAuthParam param) {
        if (CollectionUtils.containsAny(param.getAuthTenantCode(), Lists.newArrayList(DEFAULT))) {
            List<TenantInfoDTO> tenantInfoList = tenantInfoService.listAll();
            param.setAuthTenantCode(
                    tenantInfoList.stream().map(TenantInfoDTO::getTenantCode).distinct().collect(Collectors.toList()));
        }
        DaVirtualManAuthDTO daVirtualManAuth = new DaVirtualManAuthDTO();
        daVirtualManAuth.setChannel(param.getChannel());
        daVirtualManAuth.setVmCode(param.getVmCode());
        daVirtualManAuth.setAuthTenantCodeList(param.getAuthTenantCode());
        aiServiceManager.vmAuth(daVirtualManAuth);
        return ResultModel.success(null);
    }

    @PostMapping("/scene/list/{vmBizId}")
    @ApiOperation("查询数字人场景列表")
    public ResultModel<List<SceneDetailInfoVO>> sceneList(@PathVariable Long vmBizId) {
        DaVirtualManRequestDTO daVirtualManRequest = new DaVirtualManRequestDTO();
        daVirtualManRequest.setBizId(vmBizId);
        ResultModel<List<DaVirtualManScenesDTO>> result = aiServiceManager.vmSceneList(daVirtualManRequest);
        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getDataResult())) {
            return ResultModel.success(Lists.newArrayList());
        }
        return ResultModel.success(result.getDataResult().stream().map(x -> convert(x)).collect(Collectors.toList()));
    }

    private SceneDetailInfoVO convert(DaVirtualManScenesDTO source) {
        SceneDetailInfoVO sceneDetailInfoVO = new SceneDetailInfoVO();
        sceneDetailInfoVO.setId(source.getId().toString());
        sceneDetailInfoVO.setVmBizId(source.getBizId().toString());
        sceneDetailInfoVO.setVmCode(source.getVmCode());
        sceneDetailInfoVO.setChannel(source.getChannel());
        sceneDetailInfoVO.setSceneId(source.getSceneId());
        sceneDetailInfoVO.setSceneName(source.getSceneName());
        sceneDetailInfoVO.setCloth(source.getCloth());
        sceneDetailInfoVO.setPose(source.getPose());
        sceneDetailInfoVO.setResolution(source.getResolution());
        sceneDetailInfoVO.setCoverUrl(source.getCoverUrl());
        sceneDetailInfoVO.setExampleUrl(source.getExampleUrl());
        sceneDetailInfoVO.setEnableState(source.getIsEnabled());
        sceneDetailInfoVO.setExampleText(source.getExampleText());
        sceneDetailInfoVO.setExampleDuration(source.getExampleDuration());
        return sceneDetailInfoVO;
    }

    @PostMapping("/scene/edit")
    @Permission("adm:vmmanage:vm:scene:edit")
    @ApiOperation("修改数字人场景信息")
    @DigitalAssetOperate(value = "修改数字人场景信息", bizId = "#param.vmBizId", operateType = 1)
    public ResultModel sceneEdit(@RequestBody @Validated SceneUpdateParam param) {
        DaVirtualManScenesDTO daVirtualManScenes = new DaVirtualManScenesDTO();
        daVirtualManScenes.setId(param.getId());
        daVirtualManScenes.setBizId(param.getVmBizId());
        daVirtualManScenes.setSceneName(param.getSceneName());
        daVirtualManScenes.setChannel(param.getChannel());
        daVirtualManScenes.setVmCode(param.getVmCode());
        daVirtualManScenes.setSceneId(param.getSceneId());
        daVirtualManScenes.setCoverUrl(param.getCoverUrl());
        daVirtualManScenes.setCloth(param.getCloth());
        daVirtualManScenes.setPose(param.getPose());
        daVirtualManScenes.setResolution(param.getResolution());
        daVirtualManScenes.setExampleUrl(param.getExampleUrl());
        daVirtualManScenes.setExampleDuration(param.getExampleDuration());
        daVirtualManScenes.setExampleText(param.getExampleText());
        return aiServiceManager.sceneSaveOrUpdate(daVirtualManScenes);
    }

    @PostMapping("/scene/add")
    @Permission("adm:vmmanage:vm:scene:add")
    @ApiOperation("新增数字人场景信息")
    @DigitalAssetOperate(value = "新增数字人场景信息", bizId = "#param.vmBizId", operateType = 1)
    public ResultModel sceneAdd(@RequestBody @Validated SceneAddParam param) {
        DaVirtualManScenesDTO daVirtualManScenes = new DaVirtualManScenesDTO();
        daVirtualManScenes.setBizId(param.getVmBizId());
        daVirtualManScenes.setSceneName(param.getSceneName());
        daVirtualManScenes.setChannel(param.getChannel());
        daVirtualManScenes.setVmCode(param.getVmCode());
        daVirtualManScenes.setSceneId(param.getSceneId());
        daVirtualManScenes.setCoverUrl(param.getCoverUrl());
        daVirtualManScenes.setCloth(param.getCloth());
        daVirtualManScenes.setPose(param.getPose());
        daVirtualManScenes.setResolution(param.getResolution());
        daVirtualManScenes.setExampleUrl(param.getExampleUrl());
        daVirtualManScenes.setExampleDuration(param.getExampleDuration());
        daVirtualManScenes.setExampleText(param.getExampleText());
        return aiServiceManager.sceneSaveOrUpdate(daVirtualManScenes);
    }

    @PostMapping("/scene/enable")
    @Permission("adm:vmmanage:vm:scene:enable")
    @ApiOperation("数字人场景启用停用")
    @DigitalAssetOperate(value = "数字人场景启用停用", bizId = "#param.vmBizId", operateType = 1)
    public ResultModel sceneEnable(@RequestBody @Validated SceneEnableStateParam param) {
        DaVirtualManScenesDTO daVirtualManScenes = new DaVirtualManScenesDTO();
        daVirtualManScenes.setId(param.getId());
        daVirtualManScenes.setChannel(param.getChannel());
        daVirtualManScenes.setVmCode(param.getVmCode());
        daVirtualManScenes.setSceneId(param.getSceneId());
        daVirtualManScenes.setIsEnabled(param.getEnableState());
        daVirtualManScenes.setBizId(param.getVmBizId());
        return aiServiceManager.sceneSaveOrUpdate(daVirtualManScenes);
    }

}
