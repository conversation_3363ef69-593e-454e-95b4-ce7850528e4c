package com.dl.dops.biz.system.manager.menu.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 16:17
 */
@Data
public class MenuSaveBO {

    @Size(min = 0, max = 5000)
    @ApiModelProperty("菜单主键数组")
    private List<Long> menuIds;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("功能编码数组")
    private List<Long> functionIds;

    @ApiModelProperty(hidden = true)
    private Long loginUserId;
}
