package com.dl.dops.resourcecenter.pack.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

@Data
@ApiModel("查询服务包")
public class RcPackPageQueryParam extends AbstractPageParam implements Serializable {
    private static final long serialVersionUID = 6948516547406759700L;
    @ApiModelProperty("标题")
    @Length(max = 50)
    private String title;

    @ApiModelProperty("适用行业")
    private Integer domain;

    @ApiModelProperty("状态")
    private Integer status;

    //todo:跟前端说一下
    @Deprecated
    @ApiModelProperty("创建员工id")
    private String createEmpId;

    @ApiModelProperty("创建员工id")
    private String createUserId;

    @ApiModelProperty("一级分类id")
    private String category1;

    @ApiModelProperty("二级分类id")
    private String category2;

    /**
     * 场景 1-助你营，2-助你拍
     *
     * @see
     */
    @ApiModelProperty("场景 1-助你营，2-助你拍")
    private Integer scene;

}
