CREATE TABLE `sys_function` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `function_id` bigint NOT NULL COMMENT '功能id',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '名称',
  `function_code` varchar(255) NOT NULL DEFAULT '' COMMENT '权限代码',
  `icon` varchar(100) NOT NULL DEFAULT '' COMMENT '图标标识',
  `sort` int NOT NULL DEFAULT '1' COMMENT '排序',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `modify_dt` datetime NOT NULL COMMENT '更新时间',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `modify_by` bigint NOT NULL COMMENT '更新人',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_function_id` (`function_id`),
  UNIQUE KEY `uniq_function_code` (`function_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统功能表';


CREATE TABLE `sys_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `menu_id` bigint NOT NULL COMMENT '菜单id',
  `parent_id` bigint NOT NULL COMMENT '父级ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '名称',
  `menu_level` tinyint NOT NULL DEFAULT '1' COMMENT '菜单等级【1-一级菜单2-二级菜单···】',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '菜单url',
  `icon` varchar(100) NOT NULL DEFAULT '' COMMENT '图标标识',
  `disable` int NOT NULL DEFAULT '0' COMMENT '是否禁用【0-开启1-禁用】',
  `sort` int NOT NULL DEFAULT '1' COMMENT '排序',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `modify_dt` datetime NOT NULL COMMENT '更新时间',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `modify_by` bigint NOT NULL COMMENT '更新人',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_menu_id` (`menu_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统菜单表';



CREATE TABLE `sys_menu_function` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `function_id` bigint NOT NULL COMMENT '功能ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  `modify_dt` datetime NOT NULL COMMENT '更新时间',
  `modify_by` bigint NOT NULL COMMENT '更新人',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `create_by` bigint NOT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_menu_function_id` (`menu_id`,`function_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统菜单-功能表';



CREATE TABLE `sys_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `role_id` bigint NOT NULL COMMENT '角色id',
  `name` varchar(25) NOT NULL DEFAULT '' COMMENT '角色名称',
  `role_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '角色类型',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `modify_dt` datetime NOT NULL COMMENT '更新时间',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `modify_by` bigint NOT NULL COMMENT '修改人',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_role_id` (`role_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='租户-角色表';


CREATE TABLE `sys_role_function` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `role_id` bigint NOT NULL COMMENT '角色id',
  `function_id` bigint NOT NULL COMMENT '权限id',
  `modify_dt` datetime DEFAULT NULL COMMENT '修改时间',
  `modify_by` bigint NOT NULL COMMENT '修改人',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `create_by` bigint NOT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_role_function_id` (`role_id`,`function_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='租户-角色-功能表';


CREATE TABLE `sys_role_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `role_id` bigint NOT NULL COMMENT '角色id',
  `menu_id` bigint NOT NULL COMMENT '菜单id',
  `modify_dt` datetime NOT NULL COMMENT '更新时间',
  `modify_by` bigint NOT NULL COMMENT '更新人',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `create_by` bigint NOT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_role_menu_id` (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色-菜单表';



CREATE TABLE `sys_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `account` varchar(32) NOT NULL DEFAULT '' COMMENT '登录名',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '姓名',
  `password` varchar(255) NOT NULL DEFAULT '' COMMENT '密码',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态: 0=默认，1=正常，2=已禁用，4=未激活',
  `is_super_admin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为超级管理员 - 0否1是',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `modify_dt` datetime NOT NULL COMMENT '更新时间',
  `create_by` bigint NOT NULL COMMENT '创建人',
  `modify_by` bigint NOT NULL COMMENT '更新人',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';


CREATE TABLE `sys_user_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `role_id` bigint NOT NULL COMMENT '角色id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `modify_dt` datetime NOT NULL COMMENT '更新时间',
  `modify_by` bigint NOT NULL COMMENT '更新人',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `create_by` bigint NOT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_role_user_id` (`user_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户-角色表';
