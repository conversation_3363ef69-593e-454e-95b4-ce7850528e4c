package com.dl.dops.resourcecenter.pack.enums;


import java.util.Objects;

/**
 * 服务包状态枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-08 15:46
 */
public enum RcPackStatusEnum {

    DRAFT(0, "草稿", true, true),
    RELEASED(3, "已发布", false, true),
    CANCEL_RELEASED(4, "取消发布", false, true);

    private Integer status;

    private String desc;

    /**
     * 是否是服务包的状态
     */
    private boolean isPackStatus;

    /**
     * 是否是服务包模板的状态
     */
    private boolean isPackTplStatus;

    RcPackStatusEnum(Integer status, String desc, boolean isPackStatus, boolean isPackTplStatus) {
        this.status = status;
        this.desc = desc;
        this.isPackStatus = isPackStatus;
        this.isPackTplStatus = isPackTplStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public boolean getIsPackStatus() {
        return isPackStatus;
    }

    public boolean getIsPackTplStatus() {
        return isPackTplStatus;
    }

    public static String getDescByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (RcPackStatusEnum statusEnum : RcPackStatusEnum.values()) {
            if (code.equals(statusEnum.getStatus())) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }
}
