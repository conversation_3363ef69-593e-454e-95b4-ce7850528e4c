package com.dl.dops.resourcecenter.web.controllers.organize;

import com.dl.dops.resourcecenter.web.controllers.organize.vo.OrganizeVO;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-11 15:20
 */
@Slf4j
@RestController
@RequestMapping("/dops/resource/organize")
@Api("资源中心 - 金融机构组织")
public class OrganizeController {

    @Autowired
    private OrganizeProcess packProcess;

    @PostMapping("/tenantauth/page")
    @ApiOperation("分页查询组织")
    public ResultPageModel<OrganizeVO> page(@RequestBody AbstractPageParam param) {
        return packProcess.page(param);
    }
}
