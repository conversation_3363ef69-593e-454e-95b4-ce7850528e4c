package com.dl.dops.biz.resourcecenter.manager.poster.bo;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PosterParamBO {

    String bizId;

    String name;

    String categoryId;

    /**
     * 是否推荐：0 否（默认）；1 是
     */
    private Integer isRecommend;

    /**
     * 是否企业海报：0 否（默认）；1 是
     */
    private Integer isEnterprise;

    String logoImgUrl;

    String imgUrl;

    String optDetail;

    String tenantCode;

    Integer isDeleted;
}
