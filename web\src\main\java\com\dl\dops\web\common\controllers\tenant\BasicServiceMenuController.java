package com.dl.dops.web.common.controllers.tenant;

import com.dl.dops.biz.common.forest.basicservice.dto.BasicServiceMenuObjectDTO;
import com.dl.dops.biz.common.forest.basicservice.param.BasicServiceSysMenuParamDTO;
import com.dl.dops.biz.common.forest.basicservice.param.BasicServiceTenantMenuSaveParamDTO;
import com.dl.dops.biz.common.service.menu.BasicServiceMenuService;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-15 15:35
 */
@Api("基础服务-菜单管理")
@RestController
@RequestMapping(value = "/dops/basicservice/menu")
public class BasicServiceMenuController {
    
    @Resource
    private BasicServiceMenuService basicServiceMenuService;

    @PostMapping("/tenant/list")
    @ApiOperation("查询可分配给租户的系统菜单列表")
    public ResultModel<BasicServiceMenuObjectDTO> tenantMenuList(
            @RequestBody @Validated BasicServiceSysMenuParamDTO param) {
        return ResultModel.success(basicServiceMenuService.tenantMenuList(param));
    }

    @PostMapping("/tenant/save")
    @ApiOperation("保存指定租户的菜单和功能项")
    public ResultModel<Boolean> saveTenantMenu(@RequestBody @Validated BasicServiceTenantMenuSaveParamDTO param) {
        return ResultModel.success(basicServiceMenuService.saveTenantMenu(param));
    }
}
