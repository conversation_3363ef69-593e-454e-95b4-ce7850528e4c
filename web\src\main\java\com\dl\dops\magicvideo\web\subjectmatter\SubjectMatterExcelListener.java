package com.dl.dops.magicvideo.web.subjectmatter;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.dl.dops.biz.common.forest.magicvideo.dto.AssetProdInfoVO;
import com.dl.dops.biz.common.forest.magicvideo.param.AssetProdInfoListParam;
import com.dl.dops.biz.magicvideo.manager.MagicVideoDictManager;
import com.dl.dops.magicvideo.web.subjectmatter.vo.StockVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-20 15:38
 */
public class SubjectMatterExcelListener extends AnalysisEventListener<Map<Integer, String>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(SubjectMatterExcelListener.class);

    private List<StockVO> stockList;

    private MagicVideoDictManager magicVideoDictManager;

    private List<String> prodShortnameList;

    public SubjectMatterExcelListener(List<StockVO> stockList, MagicVideoDictManager magicVideoDictManager) {
        this.stockList = stockList;
        this.magicVideoDictManager = magicVideoDictManager;

        prodShortnameList = new ArrayList<>();
    }

    @Override
    public void invoke(Map<Integer, String> dataMap, AnalysisContext context) {
        // 从第三行开始处理数据
        if (context.readRowHolder().getRowIndex() >= 2) {
            // 第五列的数据输出
            String fifthColumnData = dataMap.get(4);
            LOGGER.info("Fifth Column Data: " + fifthColumnData);
            prodShortnameList.add(fifthColumnData);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 数据解析完毕后的操作
        AssetProdInfoListParam param = new AssetProdInfoListParam();
        param.setSecucategory("STOCK");
        param.setProdShortnameList(prodShortnameList);

        List<AssetProdInfoVO> prodInfoList = magicVideoDictManager.listAssetProdInfo(param);
        for (AssetProdInfoVO assetProdInfo : prodInfoList) {
            StockVO stockVO = new StockVO();
            stockVO.setStockCode(assetProdInfo.getSecuCode());
            stockVO.setStockShortName(assetProdInfo.getProdShortname());
            stockList.add(stockVO);
        }
    }
}
