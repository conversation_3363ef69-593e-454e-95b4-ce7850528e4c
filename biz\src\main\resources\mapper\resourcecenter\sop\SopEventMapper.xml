<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.dops.biz.resourcecenter.dal.sop.SopEventMapper">
    <resultMap id="resultMap" type="com.dl.dops.biz.resourcecenter.dal.sop.po.SopEventFullPO">
        <id column="id" property="id"/>
        <result column="event_id" property="eventId"/>
        <result column="sop_id" property="sopId"/>
        <result column="name" property="name"/>
        <result column="reach_type" property="reachType"/>
        <result column="content" property="content"/>
        <result column="rule_type" property="ruleType"/>
        <result column="rule_content" property="ruleContent"/>
        <result column="validity_period" property="validityPeriod"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_dt" property="createDt"/>
        <result column="create_by" property="createBy"/>
        <result column="modify_dt" property="modifyDt"/>
        <result column="modify_by" property="modifyBy"/>
        <result column="begin_dt" property="beginDate"/>
    </resultMap>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into sop_event
        (event_id,sop_id,name,reach_type,content,
        rule_type,rule_content,
        is_deleted,create_dt,create_by,modify_dt,modify_by)
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.eventId},#{item.sopId},#{item.name},#{item.reachType},#{item.content},
            #{item.ruleType},#{item.ruleContent},
            0,#{item.createDt},#{item.createBy},#{item.modifyDt},#{item.modifyBy})
        </foreach>
    </insert>

    <update id="batchLogicDeleteByIds">
        update sop_event
        set is_deleted = 1,
        modify_by = #{operatorId},
        modify_dt = now()
        where id in
        (
        <foreach collection="list" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </update>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update sop_event
            set
            <if test="item.reachType != null ">
                reach_type = #{item.reachType},
            </if>
            <if test="item.name != null and item.name != '' ">
                name = #{item.name},
            </if>
            <if test="item.content != null and item.content != ''">
                content = #{item.content},
            </if>
            <if test="item.ruleType != null">
                rule_type = #{item.ruleType},
            </if>
            <if test="item.ruleContent != null and item.ruleContent != ''">
                rule_content = #{item.ruleContent},
            </if>
            <if test="item.validityPeriod != null">
                validity_period = #{item.validityPeriod},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark},
            </if>
            modify_dt = now()
            where event_id = #{item.eventId} and is_deleted = 0
        </foreach>
    </update>

</mapper>