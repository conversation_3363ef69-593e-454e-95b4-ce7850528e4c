package com.dl.dops.magicvideo.web.template;

import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.forest.magicvideo.dto.TagVO;
import com.dl.dops.biz.common.forest.magicvideo.dto.VisualTemplateInternalVO;
import com.dl.dops.biz.common.forest.magicvideo.param.DetailParam;
import com.dl.dops.biz.common.forest.magicvideo.param.TagPageQueryParam;
import com.dl.dops.biz.common.forest.magicvideo.param.TemplatePageQueryParam;
import com.dl.dops.biz.magicvideo.manager.MagicVideoManager;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @describe: 模板相关
 * @author: hongcj
 * @date: 2023/6/17 14:43
 */
@Slf4j
@RestController
@RequestMapping("/dops/magicvideo/template")
@Api("模板相关")
public class TemplateInternalController {
    @Autowired
    private MagicVideoManager magicVideoManager;

    @PostMapping("/detail")
    ResultModel<VisualTemplateInternalVO> detail(@RequestBody DetailParam param){
        param.setTenantCode(Const.DEFAULT_TENANT_CODE);
        return magicVideoManager.detail(param);
    }

    @GetMapping("/delete/{templateId}")
    @ApiOperation("删除模板")
    public ResultModel<Void> delete(@PathVariable Long templateId) {
        return magicVideoManager.delete(templateId);
    }

    @PostMapping("/list")
    @ApiOperation("模板列表")
    public ResultPageModel<VisualTemplateInternalVO> list(@RequestBody @Validated TemplatePageQueryParam param) {
        param.setTenantCode(Const.DEFAULT_TENANT_CODE);
        return magicVideoManager.list(param);
    }

    @PostMapping("/tagList")
    @ApiOperation("标签列表")
    public ResultPageModel<TagVO> list(@RequestBody @Validated TagPageQueryParam param) {
        return magicVideoManager.tagList(param);
    }
}
