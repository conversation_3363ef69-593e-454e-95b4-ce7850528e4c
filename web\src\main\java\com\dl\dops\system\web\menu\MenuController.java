/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.dl.dops.system.web.menu;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.dops.biz.common.annotation.Permission;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.biz.system.dal.role.po.RoleFunctionPO;
import com.dl.dops.biz.system.dal.role.po.RoleMenuPO;
import com.dl.dops.biz.system.manager.function.dto.FunctionDTO;
import com.dl.dops.biz.system.manager.menu.MenuManager;
import com.dl.dops.biz.system.manager.menu.bo.RoleMenuParamBO;
import com.dl.dops.biz.system.manager.menu.dto.MenuDTO;
import com.dl.dops.biz.system.manager.role.RoleFunctionManager;
import com.dl.dops.biz.system.manager.role.RoleManager;
import com.dl.dops.biz.system.manager.role.RoleMenuManager;
import com.dl.dops.biz.system.manager.user.dto.UserDTO;
import com.dl.dops.system.web.menu.param.RoleMenuParam;
import com.dl.dops.system.web.menu.param.RoleMenuSaveParam;
import com.dl.dops.system.web.menu.vo.FunctionVO;
import com.dl.dops.system.web.menu.vo.MenuObjectVO;
import com.dl.dops.system.web.menu.vo.MenuVO;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Api(tags = "系统：菜单管理")
@RequestMapping("/dops/menu")
public class MenuController extends AbstractController {

    @Autowired
    private MenuManager menuManager;
    @Autowired
    private RoleMenuManager roleMenuManager;
    @Autowired
    private RoleFunctionManager roleFunctionManager;

    @Autowired
    private RoleManager roleManager;

    @PostMapping("/role/list")
    @Permission("adm:rolemenu:list")
    @ApiOperation("查询角色的拥有的菜单列表")
    public ResultModel<MenuObjectVO> tenantlist(@RequestBody @Validated RoleMenuParam p) {
        Assert.isTrue(p.getRoleId() != null, "角色id参数为空");

        MenuObjectVO vo = new MenuObjectVO();

        //租户下的系统菜单列表
        List<MenuVO> sysmenulist = this.getsyslist();
        if (CollectionUtils.isEmpty(sysmenulist)) {
            return ResultModel.success(vo);
        }
        List<MenuVO> menuList = new ArrayList<>(sysmenulist);
        //重置权限标识
        this.restOwnerTo0(menuList);

        //第2步：获取当前角色拥有的菜单和功能列表
        List<RoleMenuPO> rmlist = roleMenuManager
                .list(Wrappers.lambdaQuery(RoleMenuPO.class).eq(RoleMenuPO::getRoleId, p.getRoleId()));
        Map<String, String> menuMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(rmlist)) {
            rmlist.forEach(m -> {
                String menuId = String.valueOf(m.getMenuId());
                menuMap.put(menuId, menuId);
            });
        }

        List<RoleFunctionPO> rflist = roleFunctionManager
                .list(Wrappers.lambdaQuery(RoleFunctionPO.class).eq(RoleFunctionPO::getRoleId, p.getRoleId()));
        Map<String, String> functionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(rflist)) {
            rflist.forEach(mf -> {
                String functionId = String.valueOf(mf.getFunctionId());
                functionMap.put(functionId, functionId);
            });
        }

        //第3步：根据当前角色拥有的菜单和功能列表，重置权限标识为1
        this.restOwnerTo1(menuList, menuMap, functionMap);
        vo.setAll(menuList);

        //获取有分配权限的菜单id列表
        if (CollectionUtils.isNotEmpty(menuList)) {
            List<String> owners = new ArrayList<>();
            this.getOwners(owners, menuList);
            vo.setOwners(owners);
        }

        return ResultModel.success(vo);
    }

    @PostMapping("/role/save")
    @Permission("adm:rolemenu:save")
    @ApiOperation("某角色下的角色-菜单,角色-功能配置")
    public ResultModel<Boolean> saveRoleMenu(@Validated @RequestBody RoleMenuSaveParam p) {
        RoleMenuParamBO bo = new RoleMenuParamBO();
        bo.setRoleId(Long.valueOf(p.getRoleId()));
        bo.setMenuIds(p.getMenuIds().stream().map(Long::parseLong).collect(Collectors.toList()));
        bo.setLoginUserId(getUserId());
        bo.setFunctionIds(p.getFunctionIds().stream().map(Long::parseLong).collect(Collectors.toList()));
        Boolean f = roleMenuManager.saveRoleMenu(bo);
        return ResultModel.success(f);
    }

    @PostMapping("/menu/list")
    @Permission("adm:menu:list")
    @ApiOperation("查询可分配的系统菜单列表")
    public ResultModel<MenuObjectVO> syslist() {
        //此菜单只有是定力科技的租户，并且当前用户是超级管理员才能有权限
        UserDTO user = getCurrentDetail();
        Assert.isTrue(user != null, "无法获取当前用户信息");
        //Assert.isTrue(Objects.equals(user.getIsSuperAdm(), 9), "当前用户非定力超级管理员");
        //获取系统菜单列表
        List<MenuVO> all = this.getsyslist();
        MenuObjectVO vo = new MenuObjectVO();
        vo.setAll(all);
        if (CollectionUtils.isNotEmpty(all)) {
            List<String> owners = new ArrayList<>();
            this.getOwners(owners, all);
            vo.setOwners(owners);
        }
        return ResultModel.success(vo);
    }

    //获取有分配权限的菜单id列表
    private void getOwners(List<String> owners, List<MenuVO> list) {
        list.stream().filter(entity -> entity.getOwner() == 1).forEach(menu -> {
            owners.add(String.valueOf(menu.getMenuId()));
            List<FunctionVO> functions = menu.getFunctions();
            if (CollectionUtils.isNotEmpty(functions)) {
                functions.stream().filter(f -> f.getOwner() == 1).forEach(function -> {
                    //为了前端处理方便，跟菜单id区分，加F_字母前缀
                    owners.add("F_" + String.valueOf(function.getFunctionId()));
                });
            }
            this.getOwners(owners, menu.getChildren());
        });
    }

    //根据当前角色拥有的菜单和功能列表，重置权限标识为1
    private void restOwnerTo1(List<MenuVO> menuList, Map<String, String> menuMap, Map<String, String> functionMap) {
        if (CollectionUtils.isNotEmpty(menuList) && MapUtils.isNotEmpty(menuMap)) {
            menuList.forEach(m -> {
                String menuId = String.valueOf(m.getMenuId());
                if (menuMap.containsKey(menuId)) {
                    m.setOwner(1);
                }
                List<FunctionVO> functions = m.getFunctions();
                if (CollectionUtils.isNotEmpty(functions) && MapUtils.isNotEmpty(functionMap)) {
                    functions.forEach(f -> {
                        String functionId = String.valueOf(f.getFunctionId());
                        if (functionMap.containsKey(functionId)) {
                            f.setOwner(1);
                        }
                    });
                }
                restOwnerTo1(m.getChildren(), menuMap, functionMap);
            });
        }
    }

    //把分配权限标识重新设置为未分配，后面再根据角色是否有权限再重新打上1
    private void restOwnerTo0(List<MenuVO> menuList) {
        if (CollectionUtils.isNotEmpty(menuList)) {
            menuList.forEach(m -> {
                m.setOwner(0);
                List<FunctionVO> functions = m.getFunctions();
                if (CollectionUtils.isNotEmpty(functions)) {
                    functions.forEach(f -> f.setOwner(0));
                }
                restOwnerTo0(m.getChildren());
            });
        }
    }

    //过滤租户拥有权限的菜单列表
    private List<MenuVO> gettenantlist(List<MenuVO> list) {
        return list.stream().filter(entity -> entity.getOwner() == 1).peek(menu -> {
            List<FunctionVO> functions = menu.getFunctions();
            if (CollectionUtils.isNotEmpty(functions)) {
                List<FunctionVO> result = new ArrayList<>();
                for (FunctionVO f : functions) {
                    if (f.getOwner() == 1) {
                        result.add(f);
                    }
                }
                functions = result;
                menu.setFunctions(functions);
            }
            menu.setChildren(this.gettenantlist(menu.getChildren()));
        }).collect(Collectors.toList());
    }

    //获取租户系统菜单列表
    private List<MenuVO> getsyslist() {
        //获取系统菜单列表
        List<MenuDTO> list = menuManager.listMenusAndFunctions();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 组装成父子的树形结构
        return this.getChildrens(list);
    }

    // 递归查找所有菜单的子菜单
    private List<MenuVO> getChildrens(List<MenuDTO> list) {
        return list.stream().map(this::apply).collect(Collectors.toList());
    }

    private MenuVO copyMenus(MenuDTO m) {
        MenuVO vo = new MenuVO();
        vo.setDisable(m.getDisable());
        vo.setMenuId(String.valueOf(m.getMenuId()));
        vo.setIcon(m.getIcon());
        vo.setLevel(m.getLevel());
        vo.setName(m.getName());
        vo.setOwner(m.getOwner());
        vo.setSort(m.getSort());
        vo.setParentId(String.valueOf(m.getParentId()));
        vo.setUrl(m.getUrl());

        List<FunctionDTO> functions = m.getFunctions();
        if (CollectionUtils.isNotEmpty(functions)) {
            functions.forEach(f -> {
                FunctionVO functionVO = new FunctionVO();
                functionVO.setFunctionId(String.valueOf(f.getFunctionId()));
                functionVO.setOwner(f.getOwner());
                functionVO.setIcon(f.getIcon());
                functionVO.setName(f.getName());
                functionVO.setSort(f.getSort());
                vo.getFunctions().add(functionVO);
            });
        }
        return vo;
    }

    private MenuVO apply(MenuDTO menu) {
        MenuVO vo = this.copyMenus(menu);
        vo.setChildren(getChildrens(menu.getChildren()));
        return vo;
    }

}
