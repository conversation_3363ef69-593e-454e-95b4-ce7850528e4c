package com.dl.dops.magicvideo.web.auth;

import com.dl.dops.biz.common.forest.magicvideo.dto.TemplateAuthDTO;
import com.dl.dops.biz.common.forest.magicvideo.param.AddTemplateAndAuthParam;
import com.dl.dops.biz.common.forest.magicvideo.param.AuthParam;
import com.dl.dops.biz.common.forest.magicvideo.param.ChangeSourceTemplateParam;
import com.dl.dops.biz.common.forest.magicvideo.param.SwitchStatusParam;
import com.dl.dops.biz.common.forest.magicvideo.param.TemplateAuthPageQueryParam;
import com.dl.dops.biz.magicvideo.manager.MagicVideoManager;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/dops/magicvideo/templateauth")
@Api("模板授权模块")
public class TemplateAuthInternalController {
    @Autowired
    private MagicVideoManager magicVideoManager;

    @PostMapping("/list")
    @ApiOperation("系统模板列表")
    public ResultPageModel<TemplateAuthDTO> list(@RequestBody @Validated TemplateAuthPageQueryParam param) {
        return magicVideoManager.list(param);
    }

    @PostMapping("/addandauth")
    @ApiOperation("创建系统模板并授权")
    public ResultModel<TemplateAuthDTO> addAndAuth(@RequestBody @Validated AddTemplateAndAuthParam param) {
        return magicVideoManager.addAndAuth(param);
    }

    @PostMapping("/auth")
    @ApiOperation("修改授权")
    public ResultModel<Void> auth(@RequestBody @Validated AuthParam param) {
        return magicVideoManager.auth(param);
    }

    @PostMapping("/switchstatus")
    @ApiOperation("系统模板启停")
    public ResultModel<Void> switchStatus(@RequestBody @Validated SwitchStatusParam param) {
        return magicVideoManager.switchStatus(param);
    }

    @PostMapping("/changesourcetemplate")
    @ApiOperation("修改系统模板的来源模板")
    public ResultModel<Void> changeSourceTemplate(@RequestBody @Validated ChangeSourceTemplateParam param) {
        return magicVideoManager.changeSourceTemplate(param);
    }
}
