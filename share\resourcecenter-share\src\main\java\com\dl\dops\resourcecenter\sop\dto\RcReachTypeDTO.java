package com.dl.dops.resourcecenter.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 触达形式
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-05-30 16:18
 */
@ApiModel("资源中心sop触达形式")
public class RcReachTypeDTO implements Serializable {

    private static final long serialVersionUID = 6283794050468946496L;
    @ApiModelProperty("类型")
    private Integer type;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("有效期")
    private Integer validityPeriod;

    @ApiModelProperty("群sop是否支持")
    private Boolean supportByGroupSop;

    @ApiModelProperty("个人sop是否支持")
    private Boolean supportByContactSop;

    @ApiModelProperty("是否消耗企微次数")
    private Boolean consumeWeComTime;

    @ApiModelProperty("描述")
    private String desc;

    @ApiModelProperty("排序")
    private Integer sort;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getValidityPeriod() {
        return validityPeriod;
    }

    public void setValidityPeriod(Integer validityPeriod) {
        this.validityPeriod = validityPeriod;
    }

    public Boolean getSupportByGroupSop() {
        return supportByGroupSop;
    }

    public void setSupportByGroupSop(Boolean supportByGroupSop) {
        this.supportByGroupSop = supportByGroupSop;
    }

    public Boolean getSupportByContactSop() {
        return supportByContactSop;
    }

    public void setSupportByContactSop(Boolean supportByContactSop) {
        this.supportByContactSop = supportByContactSop;
    }

    public Boolean getConsumeWeComTime() {
        return consumeWeComTime;
    }

    public void setConsumeWeComTime(Boolean consumeWeComTime) {
        this.consumeWeComTime = consumeWeComTime;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
