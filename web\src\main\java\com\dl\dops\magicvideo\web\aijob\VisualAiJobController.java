package com.dl.dops.magicvideo.web.aijob;

import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.forest.magicvideo.dto.VisualDmJobInfoDTO;
import com.dl.dops.biz.common.forest.magicvideo.dto.VisualTtsJobInfoDTO;
import com.dl.dops.biz.common.forest.magicvideo.param.VisualAiJobPageQueryParam;
import com.dl.dops.biz.common.util.DateUtil;
import com.dl.dops.biz.magicvideo.manager.MagicVideoAiJobManager;
import com.dl.dops.magicvideo.web.aijob.param.VisualAiJobPageParam;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-15 16:03
 */
@RestController
@RequestMapping("/dops/magicvideo/aijob")
@Api("ai任务管理")
public class VisualAiJobController {

    @Resource
    private MagicVideoAiJobManager magicVideoAiJobManager;

    private static final Integer AI_JOB_SUCCESS_STATUS = 2;

    @ApiOperation("分页查询数字人任务信息")
    @PostMapping("/pagedmjob")
    public ResultPageModel<VisualDmJobInfoDTO> pageDmJob(@RequestBody @Validated VisualAiJobPageParam param) {
        VisualAiJobPageQueryParam rpcParam = new VisualAiJobPageQueryParam();
        rpcParam.setJobStatusList(Collections.singletonList(AI_JOB_SUCCESS_STATUS));
        rpcParam.setTenantCode(param.getTenantCode());
        rpcParam.setNeedQueryDeleted(Const.ONE);
        rpcParam.setPageIndex(param.getPageIndex());
        rpcParam.setPageSize(param.getPageSize());
        if (Objects.nonNull(param.getDate())) {
            rpcParam.setMinDt(DateUtil.getMinDate(param.getDate()));
            rpcParam.setMaxDt(DateUtil.getMaxDate(param.getDate()));
        } else {
            rpcParam.setMaxDt(param.getMaxDt());
            rpcParam.setMinDt(param.getMinDt());
        }

        return magicVideoAiJobManager.pageDmJob(rpcParam);
    }

    @ApiOperation("分页查询TTS任务信息")
    @PostMapping("/pagettsjob")
    public ResultPageModel<VisualTtsJobInfoDTO> pageTtsJob(@RequestBody @Validated VisualAiJobPageParam param) {
        VisualAiJobPageQueryParam rpcParam = new VisualAiJobPageQueryParam();
        rpcParam.setJobStatusList(Collections.singletonList(AI_JOB_SUCCESS_STATUS));
        rpcParam.setTenantCode(param.getTenantCode());
        rpcParam.setNeedQueryDeleted(Const.ONE);
        rpcParam.setPageIndex(param.getPageIndex());
        rpcParam.setPageSize(param.getPageSize());
        if (Objects.nonNull(param.getDate())) {
            rpcParam.setMinDt(DateUtil.getMinDate(param.getDate()));
            rpcParam.setMaxDt(DateUtil.getMaxDate(param.getDate()));
        } else {
            rpcParam.setMaxDt(param.getMaxDt());
            rpcParam.setMinDt(param.getMinDt());
        }

        return magicVideoAiJobManager.pageTtsJob(rpcParam);
    }

}
