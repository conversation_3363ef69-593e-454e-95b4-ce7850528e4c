package com.dl.dops.magicvideo.deliveryplan.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @describe: TemplateInternalPageQueryParam
 * @author: zhousx
 * @date: 2023/9/7 14:17
 */
@Data
public class TemplateInternalPageQueryBO extends AbstractPageParam {
    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty(value = "租户id", required = true)
    @NotBlank
    private String tenantCode;
}
