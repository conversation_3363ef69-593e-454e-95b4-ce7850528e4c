package com.dl.dops.resourcecenter.web.controllers.sop;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.enums.SymbolE;
import com.dl.dops.biz.common.service.CosPathService;
import com.dl.dops.biz.common.util.OperatorUtil;
import com.dl.dops.biz.resourcecenter.dal.category.po.CategoryPO;
import com.dl.dops.biz.resourcecenter.dal.sop.po.SopPO;
import com.dl.dops.biz.resourcecenter.manager.category.CategoryManager;
import com.dl.dops.biz.resourcecenter.manager.material.MaterialManager;
import com.dl.dops.biz.resourcecenter.manager.material.bo.MaterialBO;
import com.dl.dops.biz.resourcecenter.manager.sop.SopEventCntManager;
import com.dl.dops.biz.resourcecenter.manager.sop.SopEventManager;
import com.dl.dops.biz.resourcecenter.manager.sop.SopManager;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopAddBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventCntBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventCntSaveBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopEventSaveBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopModifyBO;
import com.dl.dops.biz.resourcecenter.manager.sop.bo.SopSearchBO;
import com.dl.dops.resourcecenter.material.dto.RcMaterialDTO;
import com.dl.dops.resourcecenter.material.enums.RcMaterialTypeEnum;
import com.dl.dops.resourcecenter.sop.dto.RcSopDTO;
import com.dl.dops.resourcecenter.sop.dto.RcSopDetailDTO;
import com.dl.dops.resourcecenter.sop.dto.RcSopEventCntDTO;
import com.dl.dops.resourcecenter.sop.dto.RcSopEventDetailDTO;
import com.dl.dops.resourcecenter.sop.enums.RcSopEventCntContentTypeEnum;
import com.dl.dops.resourcecenter.sop.enums.RcSopStatusEnum;
import com.dl.dops.resourcecenter.sop.param.RcSopEventBatchSaveParam;
import com.dl.dops.resourcecenter.sop.param.RcSopPageQueryParam;
import com.dl.dops.resourcecenter.sop.param.RcSopSaveParam;
import com.dl.dops.resourcecenter.web.controllers.AbstractController;
import com.dl.dops.resourcecenter.web.controllers.sop.convert.SopConvert;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class SopProcess extends AbstractController {
    private static final Logger LOGGER = LoggerFactory.getLogger(SopProcess.class);
    @Autowired
    private SopManager sopManager;
    @Autowired
    private SopEventManager sopEventManager;
    @Autowired
    private SopEventCntManager sopEventCntManager;
    @Autowired
    private MaterialManager materialManager;
    @Autowired
    private CategoryManager categoryManager;
    @Resource
    private OperatorUtil operatorUtil;
    @Autowired
    private CosPathService cosPathService;

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    public ResultPageModel<RcSopDTO> pageQuery(RcSopPageQueryParam param) {
        SopSearchBO bo = SopConvert.cnvSopPageQueryParam2SearchBO(param);

        if (Objects.nonNull(param.getCategory1())) {
            bo.setCategory1(Long.valueOf(param.getCategory1()));
        }
        if (StringUtils.isNumeric(param.getCategory2())) {
            bo.setCategory2(Long.valueOf(param.getCategory2()));
        }

        IPage<SopBO> iPage = sopManager.pageQuery(bo);
        if (CollectionUtils.isEmpty(iPage.getRecords())) {
            return pageQueryModel(iPage, Collections.emptyList());
        }

        //查询目录名称，key-categoryId,value-categoryName
        Map<Long, String> categoryNameMap = this.queryCategoryNameMap(iPage.getRecords());

        List<RcSopDTO> resultList = iPage.getRecords().stream().map(sopBO -> {
            RcSopDTO rcSopDTO = new RcSopDTO();
            SopConvert.fillSopDTO(sopBO, rcSopDTO, categoryNameMap);
            return rcSopDTO;
        }).collect(Collectors.toList());
        return pageQueryModel(iPage, resultList);
    }

    @Transactional
    public ResultModel delete(Long sopId) {
        SopPO existPO = sopManager.getOne(Wrappers.lambdaQuery(SopPO.class).eq(SopPO::getSopId, sopId)
                .eq(SopPO::getIsDeleted, Const.ZERO));
        Assert.notNull(existPO, "sop不存在");
        Assert.isTrue(!RcSopStatusEnum.RELEASED.getCode().equals(existPO.getStatus()), "该sop已发布，请先将状态改为取消发布再进行删除操作");

        //1.逻辑删除sop
        sopManager.logicDelete(sopId);

        //2.逻辑删除事件
        sopEventManager.logicDeleteBySopId(sopId);

        return ResultModel.success(null);
    }

    public ResultModel done(Long sopId) {
        SopBO sopBO = sopManager.selectBySopId(sopId);
        Assert.isTrue(Objects.nonNull(sopBO), "sop不存在");
        //若非草稿状态，直接返回
        if (!RcSopStatusEnum.DRAFT.getCode().equals(sopBO.getStatus())) {
            return ResultModel.success(null);
        }

        //修改状态为取消发布状态
        SopModifyBO sopModifyBO = new SopModifyBO();
        sopModifyBO.setSopId(sopId);
        sopModifyBO.setStatus(RcSopStatusEnum.CANCEL_RELEASED.getCode());
        sopManager.modify(sopModifyBO);
        return ResultModel.success(null);
    }

    public ResultModel batchSaveEvent(RcSopEventBatchSaveParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getSopId()), "sopId有误");
        //1.判断sop是否存在
        Long sopId = Long.valueOf(param.getSopId());
        SopBO sopBO = sopManager.selectBySopId(sopId);
        Assert.isTrue(Objects.nonNull(sopBO), "sop不存在");

        //2.构建保存参数列表
        List<SopEventSaveBO> saveBOList = param.getEventList().stream().map(eventSaveParam -> {
            SopEventSaveBO saveBO = new SopEventSaveBO();
            saveBO.setSopId(sopId);
            if (StringUtils.isNotBlank(eventSaveParam.getContent())) {
                Assert.isTrue(eventSaveParam.getContent().length() <= Const.FIVE_HUNDREDS, "发送内容长度最大为500");
                saveBO.setContent(eventSaveParam.getContent());
            } else {
                saveBO.setContent(SymbolE.BLANK.getValue());
            }
            if (StringUtils.isNotBlank(eventSaveParam.getEventId())) {
                saveBO.setEventId(Long.valueOf(eventSaveParam.getEventId()));
            }
            saveBO.setName(eventSaveParam.getName());
            saveBO.setRemark(eventSaveParam.getRemark());
            saveBO.setReachType(eventSaveParam.getReachWay());
            saveBO.setRuleContent(eventSaveParam.getRuleContent());
            saveBO.setRuleType(eventSaveParam.getRuleType());
            saveBO.setValidityPeriod(eventSaveParam.getValidityPeriod());

            List<SopEventCntSaveBO> cntSaveBOList = eventSaveParam.getSopEventCntList().stream().map(cntSaveParam -> {
                SopEventCntSaveBO cntSaveBO = new SopEventCntSaveBO();
                cntSaveBO.setCntId(Long.valueOf(cntSaveParam.getContentId()));
                cntSaveBO.setCntType(cntSaveParam.getContentType());
                return cntSaveBO;
            }).collect(Collectors.toList());
            saveBO.setCntSaveBOList(cntSaveBOList);
            return saveBO;
        }).collect(Collectors.toList());

        //3.批量保存事件和事件内容
        sopEventManager.batchSave(sopId, saveBOList);

        return ResultModel.success(null);
    }

    public ResultModel<RcSopDTO> info(Long sopId) {
        SopBO sopBO = sopManager.selectBySopId(sopId);
        Assert.notNull(sopBO, "sop模板不存在");

        //查询目录名称，key-categoryId,value-categoryName
        Map<Long, String> categoryNameMap = queryCategoryNameMap(Lists.newArrayList(sopBO));

        RcSopDTO rcSopDTO = SopConvert.cnvSopDTO2TplVO(sopBO, categoryNameMap);
        return ResultModel.success(rcSopDTO);
    }

    public ResultModel<String> add(RcSopSaveParam param) {
        //1.查询是否已存在该名称的未删除的sop
        SopPO exsitNameSop = sopManager.getOne(Wrappers.lambdaQuery(SopPO.class).eq(SopPO::getName, param.getName())
                .eq(SopPO::getIsDeleted, Const.ZERO));
        Assert.isNull(exsitNameSop, "当前名称的sop模板已存在");

        //2.保存sop基本信息
        SopAddBO bo = SopConvert.cnvSopSaveParam2BO(param);
        Long sopId = sopManager.add(bo);

        return ResultModel.success(String.valueOf(sopId));
    }

    public ResultModel modify(RcSopSaveParam param) {
        Assert.isTrue(StringUtils.isNotBlank(param.getSopId()), "sopId不能为空");
        //1.查询是否已存在该名称的未删除的sop,若存在该名称，判断是否是当前sop
        SopPO exsitNameSop = sopManager.getOne(Wrappers.lambdaQuery(SopPO.class).eq(SopPO::getName, param.getName())
                .eq(SopPO::getIsDeleted, Const.ZERO));
        if (Objects.nonNull(exsitNameSop) && !exsitNameSop.getSopId().equals(Long.valueOf(param.getSopId()))) {
            throw new IllegalArgumentException("当前名称的sop模板已存在");
        }

        //2.查询sop信息
        SopBO sopBO = sopManager.selectBySopId(Long.valueOf(param.getSopId()));
        if (Objects.isNull(sopBO)) {
            return ResultModel.failure("1", "该sop模板不存在");
        }

        //3.保存sop基本信息
        SopModifyBO bo = SopConvert.cnvSopTplSaveParam2ModifyBO(param);
        sopManager.modify(bo);

        return ResultModel.success(null);
    }

    public ResultModel publishOrCancel(Long sopId) {
        sopManager.tplPublishOrCancel(sopId);
        return ResultModel.success(null);
    }

    public ResultModel<RcSopEventDetailDTO> eventDetail(Long eventId) {
        //查询事件
        SopEventBO sopEventBO = sopEventManager.selectByEventId(eventId);
        Assert.isTrue(Objects.nonNull(sopEventBO), "sop事件不存在");

        RcSopEventDetailDTO detailVO = new RcSopEventDetailDTO();
        SopConvert.fillSopEventVO(sopEventBO, detailVO);

        //查询事件内容
        List<SopEventCntBO> sopEventCntBOList = sopEventCntManager.listByEventId(eventId);
        //若没有事件内容，则直接返回
        if (CollectionUtils.isEmpty(sopEventCntBOList)) {
            return ResultModel.success(detailVO);
        }

        //区分素材列表和海报列表
        List<SopEventCntBO> materialCntList = new ArrayList<>();
        List<SopEventCntBO> posterCntList = new ArrayList<>();
        sopEventCntBOList.forEach(dto -> {
            if (RcSopEventCntContentTypeEnum.MATERIAL.getCode().equals(dto.getCntType())) {
                materialCntList.add(dto);
            } else {
                posterCntList.add(dto);
            }
        });

        List<RcSopEventCntDTO> sopEventCntList = new ArrayList<>();
        //处理素材列表
        sopEventCntList.addAll(handleCntMaterialList(materialCntList));
        //处理海报列表
        sopEventCntList.addAll(handleCntPosterList(posterCntList));
        detailVO.setSopEventCntList(sopEventCntList);

        return ResultModel.success(detailVO);
    }

    public ResultModel<Map<Integer, List<RcSopEventDetailDTO>>> listEvent(Long sopId) {
        List<SopEventBO> eventDTOList = sopEventManager.listBySopId(sopId);
        if (CollectionUtils.isEmpty(eventDTOList)) {
            return ResultModel.success(Collections.emptyMap());
        }

        List<Long> eventIdList = eventDTOList.stream().map(SopEventBO::getEventId).collect(Collectors.toList());

        //查询内容列表
        List<SopEventCntBO> sopEventCntBOList = sopEventCntManager.listByEventIdList(eventIdList);
        //提取素材列表
        List<SopEventCntBO> materialCntList = new ArrayList<>();
        sopEventCntBOList.forEach(dto -> {
            if (RcSopEventCntContentTypeEnum.MATERIAL.getCode().equals(dto.getCntType())) {
                materialCntList.add(dto);
            }
        });

        //key-eventId
        Map<Long, List<SopEventCntBO>> materialCntMap = CollectionUtils.isNotEmpty(materialCntList) ?
                materialCntList.stream().collect(Collectors.groupingBy(SopEventCntBO::getEventId)) :
                Collections.emptyMap();
        //key-素材id
        Map<Long, MaterialBO> cntMaterialBOMap = this.buildCntMaterialBOMap(materialCntList);

        //最后的结果map，key是ruleContent中的第一个数字（天）
        Map<Integer, List<RcSopEventDetailDTO>> resultMap = new HashMap<>();
        eventDTOList.stream().forEach(eventDTO -> {
            RcSopEventDetailDTO sopEventVO = new RcSopEventDetailDTO();
            SopConvert.fillSopEventVO(eventDTO, sopEventVO);

            //处理素材
            List<SopEventCntBO> thisEventMaterialCntList = materialCntMap.get(eventDTO.getEventId());
            if (CollectionUtils.isNotEmpty(thisEventMaterialCntList)) {
                List<RcSopEventCntDTO> rcSopEventCntDTOList = thisEventMaterialCntList.stream().map(cntDTO -> {
                    MaterialBO materialBO = cntMaterialBOMap.get(cntDTO.getCntId());
                    if (Objects.isNull(materialBO)) {
                        return null;
                    }

                    RcSopEventCntDTO result = SopConvert.cnvSopEventCntDTO2VO(cntDTO);
                    RcMaterialDTO materialDTO = new RcMaterialDTO();
                    materialDTO.setMaterialId(materialBO.getMaterialId());
                    materialDTO.setTitle(materialBO.getTitle());
                    materialDTO.setMaterialType(materialBO.getMaterialType());
                    materialDTO.setLogoImg(materialBO.getLogoImg());
                    if (!RcMaterialTypeEnum.ARTICLE.getCode().equals(materialBO.getMaterialType())) {
                        materialDTO.setContent(materialBO.getContent());
                    }
                    result.setMaterial(materialDTO);
                    cosPathService.postProcessObjectStorageMaterialDTO(materialDTO);
                    return result;
                }).filter(Objects::nonNull).collect(Collectors.toList());
                sopEventVO.setSopEventCntList(rcSopEventCntDTOList);
            }

            //按照ruleContent的第一个数字进行分组
            String[] rules = eventDTO.getRuleContent().split(Const.TIME_RULE_SPLITTER);
            String day = rules[0];
            List<RcSopEventDetailDTO> eventBaseVOList = resultMap.get(Integer.valueOf(day));
            if (Objects.isNull(eventBaseVOList)) {
                eventBaseVOList = new ArrayList<>();
                resultMap.put(Integer.valueOf(day), eventBaseVOList);
            }
            eventBaseVOList.add(sopEventVO);
        });

        Map<Integer, List<RcSopEventDetailDTO>> finalResultMap = new HashMap<>(resultMap.size());
        //将value中的list按照ruleContent排序
        Iterator<Map.Entry<Integer, List<RcSopEventDetailDTO>>> iterator = resultMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, List<RcSopEventDetailDTO>> entry = iterator.next();
            List<RcSopEventDetailDTO> sopEventDetailVOList = entry.getValue().stream()
                    .sorted(Comparator.comparing(RcSopEventDetailDTO::getRuleContent)).collect(Collectors.toList());

            finalResultMap.put(entry.getKey(), sopEventDetailVOList);
        }

        return ResultModel.success(finalResultMap);
    }

    public ResultModel<RcSopDetailDTO> detail(Long sopId){
        //1.查询sop信息
        SopBO sopBO = sopManager.selectBySopId(sopId);
        Assert.notNull(sopBO, "sop模板不存在");

        //2.查询目录名称，key-categoryId,value-categoryName
        Map<Long, String> categoryNameMap = queryCategoryNameMap(Lists.newArrayList(sopBO));

        RcSopDetailDTO rcSopDetailDTO = new RcSopDetailDTO();
        SopConvert.fillSopDTO(sopBO,rcSopDetailDTO, categoryNameMap);

        //3.查询事件
        List<SopEventBO> eventBOList = sopEventManager.listBySopId(sopId);
        if (CollectionUtils.isEmpty(eventBOList)) {
            return ResultModel.success(rcSopDetailDTO);
        }

        List<Long> eventIdList = eventBOList.stream().map(SopEventBO::getEventId).collect(Collectors.toList());
        //4.查询内容列表
        List<SopEventCntBO> sopEventCntBOList = sopEventCntManager.listByEventIdList(eventIdList);
        //提取素材列表
        List<SopEventCntBO> materialCntList = new ArrayList<>();
        sopEventCntBOList.forEach(dto -> {
            if (RcSopEventCntContentTypeEnum.MATERIAL.getCode().equals(dto.getCntType())) {
                materialCntList.add(dto);
            }
        });

        //key-eventId
        Map<Long, List<SopEventCntBO>> materialCntMap = CollectionUtils.isNotEmpty(materialCntList) ?
                materialCntList.stream().collect(Collectors.groupingBy(SopEventCntBO::getEventId)) :
                Collections.emptyMap();
        //key-素材id
        Map<Long, MaterialBO> cntMaterialBOMap = this.buildCntMaterialBOMap(materialCntList);
        //5.类型转换
        List<RcSopEventDetailDTO> eventDetailDTOList =  eventBOList.stream().map(eventBO -> {
            RcSopEventDetailDTO sopEventDetailDTO = new RcSopEventDetailDTO();
            SopConvert.fillSopEventVO(eventBO, sopEventDetailDTO);

            //处理素材
            List<SopEventCntBO> thisEventMaterialCntList = materialCntMap.get(eventBO.getEventId());
            if (CollectionUtils.isNotEmpty(thisEventMaterialCntList)) {
                List<RcSopEventCntDTO> rcSopEventCntDTOList = thisEventMaterialCntList.stream().map(cntBO -> {
                    MaterialBO materialBO = cntMaterialBOMap.get(cntBO.getCntId());
                    if (Objects.isNull(materialBO)) {
                        return null;
                    }

                    RcSopEventCntDTO result = SopConvert.cnvSopEventCntDTO2VO(cntBO);
                    RcMaterialDTO materialDTO = new RcMaterialDTO();
                    materialDTO.setMaterialId(materialBO.getMaterialId());
                    materialDTO.setTitle(materialBO.getTitle());
                    materialDTO.setMaterialType(materialBO.getMaterialType());
                    materialDTO.setLogoImg(materialBO.getLogoImg());
                    if (!RcMaterialTypeEnum.ARTICLE.getCode().equals(materialBO.getMaterialType())) {
                        materialDTO.setContent(materialBO.getContent());
                    }
                    cosPathService.postProcessObjectStorageMaterialDTO(materialDTO);
                    result.setMaterial(materialDTO);
                    return result;
                }).filter(Objects::nonNull).collect(Collectors.toList());
                sopEventDetailDTO.setSopEventCntList(rcSopEventCntDTOList);
            }
            return sopEventDetailDTO;
        }).collect(Collectors.toList());
        rcSopDetailDTO.setSopEventDetailList(eventDetailDTOList);
        return ResultModel.success(rcSopDetailDTO);
    }

    /**
     * 处理素材列表
     *
     * @param materialCntList
     * @return
     */
    private List<RcSopEventCntDTO> handleCntMaterialList(List<SopEventCntBO> materialCntList) {
        //查询素材信息列表
        List<MaterialBO> materialBOList = materialManager
                .batchDetail(materialCntList.stream().map(SopEventCntBO::getCntId).collect(Collectors.toList()));
        Map<Long, MaterialBO> cntMaterialBOMap = materialBOList.stream().collect(Collectors
                .toMap((cntMaterialDTO) -> Long.valueOf(cntMaterialDTO.getMaterialId()), Function.identity(),
                        (d1, d2) -> d1));

        return materialCntList.stream().map(cntDTO -> {
            RcSopEventCntDTO result = SopConvert.cnvSopEventCntDTO2VO(cntDTO);
            if (Objects.isNull(cntMaterialBOMap.get(cntDTO.getCntId()))) {
                return null;
            }

            MaterialBO materialBO = cntMaterialBOMap.get(cntDTO.getCntId());
            RcMaterialDTO dto = new RcMaterialDTO();
            dto.setMaterialId(materialBO.getMaterialId());
            dto.setTitle(materialBO.getTitle());
            if (!RcMaterialTypeEnum.ARTICLE.getCode().equals(materialBO.getMaterialType())) {
                dto.setContent(materialBO.getContent());
            }
            dto.setSize(materialBO.getSize());
            dto.setLogoImg(materialBO.getLogoImg());
            dto.setMaterialType(materialBO.getMaterialType());
            result.setMaterial(dto);
            return result;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 查询目录名称
     *
     * @param sopBOList
     * @return
     */
    private Map<Long, String> queryCategoryNameMap(List<SopBO> sopBOList) {
        //提取分类id列表
        Set<Long> categoryIdList = new HashSet<>();
        sopBOList.stream().forEach(dto -> {
            if (Objects.nonNull(dto.getCategory1())) {
                categoryIdList.add(dto.getCategory1());
            }
            if (Objects.nonNull(dto.getCategory2())) {
                categoryIdList.add(dto.getCategory2());
            }
        });
        if (org.springframework.util.CollectionUtils.isEmpty(categoryIdList)) {
            return new HashMap<>();
        }
        List<CategoryPO> poList = categoryManager
                .list(Wrappers.lambdaQuery(CategoryPO.class).in(CategoryPO::getBizId, categoryIdList)
                        .eq(CategoryPO::getIsDeleted, Const.ZERO));
        return poList.stream().collect(Collectors.toMap(CategoryPO::getBizId, CategoryPO::getName));
    }

    /**
     * 处理海报列表
     *
     * @param materialCntList
     * @return
     */
    private List<RcSopEventCntDTO> handleCntPosterList(List<SopEventCntBO> materialCntList) {
        //todo:查询海报信息列表

        return materialCntList.stream().map(cntDTO -> {
            RcSopEventCntDTO result = SopConvert.cnvSopEventCntDTO2VO(cntDTO);

            return result;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private Map<Long, MaterialBO> buildCntMaterialBOMap(List<SopEventCntBO> materialCntList) {
        if (CollectionUtils.isEmpty(materialCntList)) {
            return Collections.emptyMap();
        }
        //查询素材信息列表
        List<MaterialBO> materialBOList = materialManager
                .batchDetail(materialCntList.stream().map(SopEventCntBO::getCntId).collect(Collectors.toList()));
        //key-素材id
        Map<Long, MaterialBO> cntMaterialDTOMap = materialBOList.stream().collect(Collectors
                .toMap((cntMaterialDTO) -> Long.valueOf(cntMaterialDTO.getMaterialId()), Function.identity(),
                        (d1, d2) -> d1));
        return cntMaterialDTOMap;
    }

}
