package com.dl.dops.biz.resourcecenter.manager.rs.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-10-21 15:19
 */
@Data
public class RsScriptTplDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long scriptId;

    private String question;

    private String content;

    /**
     * 一级分类id
     */
    private Long category1;

    /**
     * 二级分类id
     */
    private Long category2;

    private Integer type;

    private Date createDt;

    private Long createBy;

    private Integer publishStatus;

    private List<Long> materialIds;
}
