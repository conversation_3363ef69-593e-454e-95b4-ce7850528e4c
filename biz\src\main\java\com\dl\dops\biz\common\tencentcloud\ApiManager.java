package com.dl.dops.biz.common.tencentcloud;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.dops.biz.common.tencentcloud.cos.GetTempCredentialBO;
import com.dl.dops.biz.common.tencentcloud.cos.CosTempCredentialDTO;
import com.dl.dops.biz.common.tencentcloud.cos.policy.Policy;
import com.dl.dops.biz.common.tencentcloud.cos.policy.Statement;
import com.dl.dops.biz.common.tencentcloud.cos.properties.CosProperties;
import com.dl.dops.biz.common.tencentcloud.properties.ApiProperties;
import com.dl.dops.biz.common.tencentcloud.properties.TencentCloudProperties;
import com.qcloud.Module.Sts;
import com.qcloud.QcloudApiModuleCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;
import java.util.TreeMap;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ApiManager {

    static final String POLICY_COS_RESOURCES_PREFIX = "qcs::cos:";

    @Autowired
    private TencentCloudProperties properties;

    private CloudApiResult getFederationToken(Policy policy, long startTime, long expiredTime) throws Exception {
        ApiProperties apiProperties = properties.getApi();
        CosProperties cosProperties = properties.getCosProperties();
        TreeMap<String, Object> config = new TreeMap<String, Object>();

        config.put("SecretId", apiProperties.getSecretId());
        config.put("SecretKey", apiProperties.getSecretKey());
        /* 请求方法类型 POST、GET */
        config.put("RequestMethod", "POST");
        config.put("DefaultRegion", properties.getDefaultRegion());
        config.put("bucket", cosProperties.getBucketId());
        QcloudApiModuleCenter module = new QcloudApiModuleCenter(new Sts(), config);
        TreeMap<String, Object> params = new TreeMap<String, Object>();
        /* 将需要输入的参数都放入 params 里面，必选参数是必填的。 */
        /* DescribeInstances 接口的部分可选参数如下 */
        params.put("version", "2018-08-13");
        params.put("Region", cosProperties.getRegion());
        params.put("name", "requesttoken");
        log.info(JSONUtil.toJsonStr(policy));
        params.put("policy", URLEncoder.encode(JSONUtil.toJsonStr(policy), "utf-8"));
        params.put("DurationSeconds", expiredTime);
        params.put("Timestamp", startTime);
        String result = null;
        /* call 方法正式向指定的接口名发送请求，并把请求参数params传入，返回即是接口的请求结果。 */
        result = module.call("GetFederationToken", params);
        log.info(result);
        return CloudApiResult.result(result);
    }

    private Policy createUploadPolicy(List<String> path) {
        CosProperties cosProperties = properties.getCosProperties();
        ApiProperties apiProperties = properties.getApi();
        String region = cosProperties.getRegion();
        String appId = apiProperties.getAppId();
        String bucketId = cosProperties.getBucketId();
        List<String> resources = path.parallelStream().map(p -> {
            StringBuilder s = new StringBuilder(POLICY_COS_RESOURCES_PREFIX);
            s.append(region).append(":").append("uid/").append(appId).append(":").append(bucketId).append(p)
                    .append("/*");
            return s.toString();
        }).collect(Collectors.toList());
        Statement s = Statement.createSimpleObjectPolicy(resources, true);

        return Policy.builder().statement(Collections.singletonList(s)).build();
    }

    /**
     * 获取临时token
     *
     * @param bo
     * @return
     */
    public CosTempCredentialDTO getCosTempCredential(GetTempCredentialBO bo) {
        CloudApiResult result = null;
        try {
            long startTime = System.currentTimeMillis() / 1000L;
            long expiredTime = 3600L;
            result = execute(bo, startTime, expiredTime);
            if (result.getCode() != 0) {
                log.error(result.getCodeDesc());
                throw BusinessServiceException.getInstance("获取凭证失败");
            }
            JSONObject jsonObject = result.getData();
            CosTempCredentialDTO dto = jsonObject.get("credentials", CosTempCredentialDTO.class);
            dto.setRegion(properties.getCosProperties().getRegion());
            dto.setBucketId(properties.getCosProperties().getBucketId());
            dto.setStartTime(startTime);
            dto.setExpiredTime(startTime + expiredTime);
            return dto;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw BusinessServiceException.getInstance("获取凭证失败");
        }
    }

    /**
     * 生成临时token
     *
     * @param bo
     * @return
     */
    private CloudApiResult execute(GetTempCredentialBO bo, long startTime, long expiredTime) throws Exception {
        StringBuilder path = new StringBuilder();
        if (StringUtils.isNotBlank(bo.getModule())) {
            path.append("/").append(bo.getModule());
        }
        if (StringUtils.isNotBlank(bo.getTarget())) {
            path.append("/").append(bo.getTarget());
        }
        Policy policy = createUploadPolicy(Collections.singletonList(path.toString()));
        CloudApiResult result = getFederationToken(policy, startTime, expiredTime);
        return result;
    }
}
