package com.dl.dops.web.common.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-28 10:14
 */
@Component
public class EnvUtil {

    @Value("${spring.profiles.active}")
    private String springProfilesActive;

    private static final String PROD = "prod";

    private static final String TEST = "test";

    private static final String CDEV = "cdev";

    private static final String DEV = "dev";

    public boolean isTest() {
        if (TEST.equals(springProfilesActive)) {
            return true;
        }
        return false;
    }

    public boolean isCdev() {
        if (CDEV.equals(springProfilesActive)) {
            return true;
        }
        return false;
    }

    public boolean isProd() {
        if (PROD.equals(springProfilesActive)) {
            return true;
        }
        return false;
    }

    public boolean isDev() {
        if (DEV.equals(springProfilesActive)) {
            return true;
        }
        return false;
    }

}
