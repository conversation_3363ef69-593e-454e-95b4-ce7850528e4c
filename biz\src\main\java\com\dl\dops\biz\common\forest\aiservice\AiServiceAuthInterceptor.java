package com.dl.dops.biz.common.forest.aiservice;

import com.dl.aiservice.share.common.auth.AuthHeader;
import com.dl.aiservice.share.common.auth.AuthTokenDTO;
import com.dl.dops.biz.common.config.DlDomainConfig;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.common.forest.basicservice.dto.TenantInfoDTO;
import com.dl.dops.biz.common.service.tenant.TenantInfoService;
import com.dl.dops.biz.common.util.ApplicationUtil;
import com.dl.dops.resourcecenter.tenantauth.util.RsaUtil;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.Date;

/**
 * @describe: AiServiceAuthInterceptor
 * @author: zhousx
 * @date: 2023/3/20 17:04
 */
@Slf4j
public class AiServiceAuthInterceptor implements Interceptor {

    private static final String AISERVICE = "/aiservice";

    @Override
    public boolean beforeExecute(ForestRequest request) {
        request = setUrl(request);
        AuthTokenDTO authTokenDTO = new AuthTokenDTO();
        String url = request.getMethod().getMetaRequest().getUrl();
        if (AiServiceClient.TTS_URL.equals(url) || AiServiceClient.VOICE_TRAIN_URL.equals(url)) {
            authTokenDTO.setChannel((Integer) request.getArgument(0));
        } else {
            authTokenDTO.setChannel(Const.ZERO);
        }

        authTokenDTO.setTokenCreateDt(new Date());
        authTokenDTO.setTenantCode(Const.DEFAULT_TENANT_CODE);
        TenantInfoService tenantInfoManager = ApplicationUtil.getBean(TenantInfoService.class);
        TenantInfoDTO sysTenantInfoPO = tenantInfoManager.info(Const.DEFAULT_TENANT_CODE);
        Assert.notNull(sysTenantInfoPO, "租户不存在");
        request.addHeader(AuthHeader.TOKEN_HEADER_NAME, genAuthToken(authTokenDTO, sysTenantInfoPO.getPrivateKey()));
        request.addHeader(AuthHeader.TENANT_CODE_HEADER_NAME, Const.DEFAULT_TENANT_CODE);
        return Boolean.TRUE;
    }

    private ForestRequest setUrl(ForestRequest request) {
        DlDomainConfig bean = ApplicationUtil.getBean(DlDomainConfig.class);
        return request.setUrl(bean.localCallbackDomain() + AISERVICE + request.getMethod().getMetaRequest().getUrl());
    }

    private String genAuthToken(AuthTokenDTO authTokenDTO, String privateKey) {
        String str = JsonUtils.toJSON(authTokenDTO);
        try {
            return RsaUtil.encryptByPrivateKey(privateKey, str);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw BusinessServiceException.getInstance("aiservice token 生成失败");
        }
    }
}
