package com.dl.dops.system.web.voicetrain.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-07 09:57
 */
@Data
public class VoiceTrainResultPageQueryParam extends AbstractPageParam {

    /**
     * 训练名
     */
    @ApiModelProperty("训练名")
    private String name;

    /**
     * 厂商训练模型编号
     */
    @ApiModelProperty("厂商训练模型编号")
    private String extModelCode;

    /**
     * 厂商，3-深声科技（线上训练），6-火山引擎
     */
    @ApiModelProperty("厂商，3-深声科技（线上训练），6-火山引擎")
    private Integer channel;

    /**
     * 训练状态：1 训练中；0 训练完成；-1 训练失败
     */
    @ApiModelProperty("训练状态：1 训练中；0 训练完成；-1 训练失败")
    private Integer status;

}
