package com.dl.dops.biz.common.constant;

public interface Const {
    String SIMPLE_JWT_SECRET = "ZGluZ2xpa2VqaV9oYW5nemhvdQ==";//JWT秘钥
    String SIMPLE_JWT_PREFIX = "dl_jwt_";//JWT前缀

    /**
     * 默认租户
     */
    String DEFAULT_TENANT_CODE = "DL";

    /**
     * dops token header
     */
    String TOKEN_HEADER_NAME = "X-Dops-Authorization";

    Integer ZERO = 0;

    Integer ONE = 1;

    Integer TWO = 2;

    Integer THREE = 3;

    Integer FOUR = 4;

    Integer FIVE = 5;

    Integer SIX = 6;

    Integer SEVEN = 7;

    Integer EIGHT = 8;

    Integer NINE = 9;
    Integer NINETY_NIE = 99;

    Integer TEN = 10;

    Integer FOURTEEN = 14;

    Integer FIFTEEN = 15;

    Integer TWENTY = 20;

    Integer THIRTY = 30;

    Integer FIFTY = 50;

    Integer ONE_HUNDRED = 100;

    Integer TWO_HUNDREDS = 200;

    Integer FIVE_HUNDREDS = 500;

    String ZERO_STR = "0";

    String ONE_STR = "1";

    Long ZERO_LONG = 0L;

    Long ONE_LONG = 1L;

    String VERTICAL_LINE = "|";

    String SLASH = "/";

    String HTTP_PREFIX = "http";

    String TIME_RULE_SPLITTER = "-";

    String NULL = "null";

    /**
     * 默认密码
     */
    String ADM_USER_DEFAULT_PWD = "123456";

    /**
     * 系统编码
     */
    String SYSTEM_CODE = "X-System-code";

    /**
     * 系统访问操作人员
     */
    String BASIC_SERVICE_INVOKE_OPERATOR = "X-invoke-operator";

    /**
     * 系统访问操作租户
     */
    String BASIC_SERVICE_INVOKE_TENANTCODE = "X-invoke-tenantCode";

    /**
     * wc的系统编码
     */
    String DL_WEALTH_CENTER = "dl-wealth-center";

    /**
     * magic的系统编码
     */
    String MAGIC_SYSTEM_CODE = "dl-magicvideo";
}
