package com.dl.dops.biz.resourcecenter.dal.tenant.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

@Data
@TableName(value = "tenant_auth")
public class TenantAuthPO extends BasePO {
    private static final long serialVersionUID = -1L;
    @TableId
    Long id;

    /**
     * 租户id
     */
    @TableField("tenant_code")
    String tenantCode;

    @TableField("status")
    Integer status;
}