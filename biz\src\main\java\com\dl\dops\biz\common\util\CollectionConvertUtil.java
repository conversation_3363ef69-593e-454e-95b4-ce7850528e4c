package com.dl.dops.biz.common.util;

import org.apache.commons.collections.CollectionUtils;

import java.lang.reflect.Array;
import java.util.*;
import java.util.function.Function;

public class CollectionConvertUtil {

    /**
     * 实体list转map
     *
     * @param poList 实体list
     * @param k      实体-》主键函数
     * @param v      实体-》值函数
     * @param <PO>   实体
     * @param <V>    值泛型
     * @return
     */
    public static <PO, V> Map<String, Set<V>> listToMap(List<PO> poList, Function<PO, String> k, Function<PO, V> v) {
        Map<String, Set<V>> map = new HashMap<>();
        poList.forEach(po -> {
            String key = k.apply(po);
            V value = v.apply(po);
            Set<V> set;
            if (!map.containsKey(key)) {
                set = new HashSet<>();
            } else {
                set = map.get(key);
            }
            set.add(value);
        });
        return map;
    }

    public static <T> T[] collectionToArray(Class<T> tClass, Collection<T> collection) {
        if (CollectionUtils.isEmpty(collection)) {
            return (T[]) Array.newInstance(tClass, 0);
        }
        return collection.toArray((T[]) Array.newInstance(tClass, collection.size()));
    }
}
