package com.dl.dops.biz.common.util.excel;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class ExcelData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 表头
     * 写入excel时通过反射的方式解析出field名称用于设置excel title
     */
    private List<Field> fields;

    /**
     * 数据
     * 写入excel时通过反射获取到getField方法填充excel
     */
    private List<? extends Object> rows;

    /**
     * 页签名称
     */
    private String name;

    @Data
    public static class Field {

        /**
         * 字段名称
         */
        private String fieldName;

        /**
         * 字段中文描述（用于导出excel时生成title）
         */
        private String title;

        private boolean dateType;

        private Map<String, Object> codeMapping;
    }

}

