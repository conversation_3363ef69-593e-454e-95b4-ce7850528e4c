package com.dl.dops.biz.system.manager.dynamicchart;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.dops.biz.common.service.CommonService;
import com.dl.dops.biz.system.dal.templatedynamicchart.po.TemplateDynamicChartPO;
import com.dl.framework.common.bo.PageQueryDO;

public interface TemplateDynamicChartManager  extends IService<TemplateDynamicChartPO>, CommonService {
    IPage<TemplateDynamicChartPO> pagePO(TemplateDynamicChartPO po, PageQueryDO pageQueryDO);
}
