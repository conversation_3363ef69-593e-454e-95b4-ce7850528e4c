package com.dl.dops.biz.resourcecenter.dal.pack.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-13 09:37
 */
@Data
@TableName("pack_chain_path")
public class PackChainPathPO extends BasePO {

    private static final long serialVersionUID = -9201663387108516230L;
    @TableId
    private Long id;

    @TableField("pack_id")
    private Long packId;

    @TableField("chain_path_id")
    private Long chainPathId;

    @TableField("name")
    private String name;

    @TableField("sort")
    private Integer sort;

    @TableField("is_deleted")
    private Integer isDeleted;
}
