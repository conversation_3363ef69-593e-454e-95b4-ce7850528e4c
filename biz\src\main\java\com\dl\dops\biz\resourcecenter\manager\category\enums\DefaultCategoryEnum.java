package com.dl.dops.biz.resourcecenter.manager.category.enums;

/**
 * @ClassName PosterDefaultCategoryEnum
 * @Description 海报默认分类枚举类
 * <AUTHOR>
 * @Date 2022/4/6 10:40
 * @Version 1.0
 **/
public enum DefaultCategoryEnum {

    ALL("-1", "全部"),

    UNCLASSIFIED("0", "未分类");

    private String code;
    private String desc;

    DefaultCategoryEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
