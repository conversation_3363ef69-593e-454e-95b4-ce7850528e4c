package com.dl.dops.biz.system.dal.role.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 14:10
 */
@Data
@TableName("sys_user_role")
public class UserRolePO extends BasePO {
    private static final long serialVersionUID = -210865596952814926L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    private Long roleId;

}

