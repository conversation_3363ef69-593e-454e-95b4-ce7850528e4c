package com.dl.dops.resourcecenter.material.param;

import com.dl.framework.common.bo.PageQueryDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

@Data
public class RcMaterialPageQueryParam extends PageQueryDO implements Serializable {
    private static final long serialVersionUID = -1895009470750122599L;

    @ApiModelProperty("标题")
    @Length(max = 100)
    private String title;

    @ApiModelProperty("素材类型")
    private Integer materialType;

    /**
     * @see:TagDefaultEnum
     */
    @ApiModelProperty("标签id，,-1表示查全部，0表示查无标签")
    private String tagId;

    @ApiModelProperty("发布状态，1-已发布，2-取消发布")
    private Integer publishStatus;

    @ApiModelProperty("分类id列表")
    private String categoryId;

}
