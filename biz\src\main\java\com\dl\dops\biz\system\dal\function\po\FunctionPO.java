package com.dl.dops.biz.system.dal.function.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.dops.biz.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-06 16:30
 */
@Data
@TableName("sys_function")
public class FunctionPO extends BasePO {

    private static final long serialVersionUID = -8561489621636165440L;
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 功能id
     */
    private Long functionId;

    /**
     * 名称
     */
    private String name;

    /**
     * 权限代码
     */
    private String functionCode;

    /**
     * 图标标识
     */
    private String icon;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * is_deleted
     */
    private int isDeleted;
}
