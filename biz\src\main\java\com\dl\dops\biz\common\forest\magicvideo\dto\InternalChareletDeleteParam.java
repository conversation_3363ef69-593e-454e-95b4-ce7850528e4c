package com.dl.dops.biz.common.forest.magicvideo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-13 09:27
 */
@Data
public class InternalChareletDeleteParam {

    @NotNull(message = "bizId不能为空")
    @ApiModelProperty("bizId不能为空")
    private Long bizId;

    @NotBlank(message = "修改人名称不能为空")
    @ApiModelProperty("修改人名称")
    private String modifyName;

    @ApiModelProperty("修改人")
    private Long modifyBy;
}
