package com.dl.dops.biz.common.forest.magicvideo;

import com.dl.dops.biz.common.config.DlDomainConfig;
import com.dl.dops.biz.common.util.ApplicationUtil;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MagicVideoInterceptor implements Interceptor {

    private static final String MAGIC_VIDEO_PATH = "/magicvideo/visual/internal";
    //private static final String MAGIC_VIDEO_PATH = "/visual/internal";

    @Override
    public boolean beforeExecute(ForestRequest request) {
        request = setUrl(request);
        return Boolean.TRUE;
    }

    private ForestRequest setUrl(ForestRequest request) {
        DlDomainConfig bean = ApplicationUtil.getBean(DlDomainConfig.class);
        return request.setUrl(bean.localCallbackDomain() + MAGIC_VIDEO_PATH + request.getUrl());
        //return request.setUrl("http://localhost:8081" + MAGIC_VIDEO_PATH + request.getUrl());
    }

}
