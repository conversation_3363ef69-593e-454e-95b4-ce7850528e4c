package com.dl.dops.biz.system.manager.dynamicchart.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.system.dal.templatedynamicchart.TemplateDynamicChartMapper;
import com.dl.dops.biz.system.dal.templatedynamicchart.po.TemplateDynamicChartPO;
import com.dl.dops.biz.system.manager.dynamicchart.TemplateDynamicChartManager;
import com.dl.framework.common.bo.PageQueryDO;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class TemplateDynamicChartManagerImpl extends ServiceImpl<TemplateDynamicChartMapper, TemplateDynamicChartPO>
        implements TemplateDynamicChartManager {

    @Override
    public IPage<TemplateDynamicChartPO> pagePO(TemplateDynamicChartPO po, PageQueryDO pageQueryDO) {
        LambdaQueryWrapper<TemplateDynamicChartPO> queryWrapper = Wrappers.lambdaQuery(TemplateDynamicChartPO.class);

        queryWrapper.like(StringUtils.isNotBlank(po.getName()), TemplateDynamicChartPO::getName, po.getName())
                .eq(StringUtils.isNotBlank(po.getTemplateId()), TemplateDynamicChartPO::getTemplateId, po.getTemplateId())
                .eq(Objects.nonNull(po.getType()), TemplateDynamicChartPO::getType, po.getType())
                .orderByDesc(TemplateDynamicChartPO::getId);

        IPage<TemplateDynamicChartPO> pageResult = baseMapper.selectPage(convert(pageQueryDO), queryWrapper);
        return pageResult;
    }
}
