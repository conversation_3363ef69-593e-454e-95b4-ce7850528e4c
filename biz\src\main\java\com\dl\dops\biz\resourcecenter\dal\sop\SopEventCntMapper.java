package com.dl.dops.biz.resourcecenter.dal.sop;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.dops.biz.common.annotation.BaseDao;
import com.dl.dops.biz.resourcecenter.dal.sop.po.SopEventCntPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@BaseDao
@DS("resourcecenter")
public interface SopEventCntMapper extends BaseMapper<SopEventCntPO> {

    void batchInsert(@Param("list") List<SopEventCntPO> list);

    void batchLogicDeleteByIds(@Param("list") List<Long> list, @Param("operatorId") Long operatorId);

    void batchLogicDeleteByEventIds(@Param("list") List<Long> list, @Param("operatorId") Long operatorId);

}
