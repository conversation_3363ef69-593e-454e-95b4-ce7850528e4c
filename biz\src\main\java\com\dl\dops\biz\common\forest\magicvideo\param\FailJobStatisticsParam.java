package com.dl.dops.biz.common.forest.magicvideo.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-18 10:15
 */
@Data
public class FailJobStatisticsParam implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @NotNull(message = "最小时间不能为空")
    @ApiModelProperty("最小时间")
    private Date minDt;

    @NotNull(message = "最大时间不能为空")
    @ApiModelProperty("最大时间")
    private Date maxDt;
}
