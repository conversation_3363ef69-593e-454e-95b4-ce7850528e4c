package com.dl.dops.resourcecenter.web.controllers.templatedynamicchart.convert;

import com.dl.dops.biz.system.dal.templatedynamicchart.po.TemplateDynamicChartPO;
import com.dl.dops.resourcecenter.web.controllers.templatedynamicchart.vo.TemplateDynamicChartVO;

public class TemplateDynamicChartConverter {
    public static TemplateDynamicChartVO fillToDTO(TemplateDynamicChartPO input) {
        TemplateDynamicChartVO result = new TemplateDynamicChartVO();
        result.setTemplateId(input.getTemplateId());
        result.setId(input.getId().toString());
        result.setName(input.getName());
        result.setPreviewVideoUrl(input.getPreviewVideoUrl());
        result.setCoverUrl(input.getCoverUrl());
        result.setParamJson(input.getParamJson());
        result.setType(input.getType());
        return result;
    }
}
