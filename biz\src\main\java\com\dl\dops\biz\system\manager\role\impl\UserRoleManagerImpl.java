package com.dl.dops.biz.system.manager.role.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.dops.biz.common.constant.Const;
import com.dl.dops.biz.system.dal.role.RoleMapper;
import com.dl.dops.biz.system.dal.role.UserRoleMapper;
import com.dl.dops.biz.system.dal.role.po.RolePO;
import com.dl.dops.biz.system.dal.role.po.UserRolePO;
import com.dl.dops.biz.system.manager.role.UserRoleManager;
import com.dl.dops.biz.system.manager.role.dto.RoleIdDTO;
import com.dl.dops.biz.system.manager.user.bo.UserRolesParamBO;
import com.dl.dops.biz.system.manager.user.impl.UserRoleMenuRedisCache;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-02 14:16
 */
@Component
@DS("dops")
public class UserRoleManagerImpl extends ServiceImpl<UserRoleMapper, UserRolePO> implements UserRoleManager {

    @Autowired
    private UserRoleMenuRedisCache userRoleMenuRedisCache;

    @Autowired
    private RoleMapper roleMapper;

    private void validateRoleIds(List<Long> roleIds) {
        LambdaQueryWrapper<RolePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(RolePO::getRoleId, roleIds).eq(RolePO::getIsDeleted, Const.ZERO);
        Integer cnt = roleMapper.selectCount(lambdaQueryWrapper);
        Assert.isTrue(roleIds.size() == cnt, "roleids非法");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUserRoles(UserRolesParamBO bo) {
        Long userId = bo.getUserId();
        List<Long> roleIds = bo.getRoleIds();
        Assert.notNull(userId, "用户id不能为空");
        LambdaQueryWrapper<UserRolePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        //校验角色id
        validateRoleIds(roleIds);
        //1、删除旧用户关联的角色
        lambdaQueryWrapper.eq(UserRolePO::getUserId, userId);
        baseMapper.delete(lambdaQueryWrapper);

        //2、增加用户关联的角色
        if (CollectionUtils.isNotEmpty(roleIds)) {
            List<UserRolePO> list = roleIds.stream().map(roleId -> createSysUserRole(roleId, userId))
                    .collect(Collectors.toList());
            saveBatch(list);
        }
        //3、更新用户-角色缓存
        userRoleMenuRedisCache.updateUserRoles(bo);
    }

    @Override
    public List<RoleIdDTO> findByUserId(Long userId) {
        List<UserRolePO> list = baseMapper
                .selectList(Wrappers.<UserRolePO>lambdaQuery().eq(UserRolePO::getUserId, userId));
        return list.stream().map(t -> RoleIdDTO.builder().roleId(t.getRoleId()).build()).collect(Collectors.toList());
    }

    private UserRolePO createSysUserRole(Long roleId, Long userId) {
        UserRolePO po = new UserRolePO();
        po.setRoleId(roleId);
        po.setUserId(userId);
        return po;
    }

}
